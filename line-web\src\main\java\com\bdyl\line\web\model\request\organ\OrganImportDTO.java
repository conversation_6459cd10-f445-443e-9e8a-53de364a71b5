package com.bdyl.line.web.model.request.organ;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 组织导入DTO
 *
 * <AUTHOR>
 */
@Data
public class OrganImportDTO {
    /**
     * 组织类型 {@link com.bdyl.line.common.constant.enums.RegionLevelEnum}
     */
    @ExcelProperty("组织类型")
    private String orgType;

    /**
     * 地区名称
     */
    @ExcelProperty("地区名称")
    private String regionName;

    /**
     * 平台名称
     */
    @ExcelProperty("平台名称")
    private String platformName;

    /**
     * 管理员手机号
     */
    @ExcelProperty("管理员手机号")
    private String adminPhone;

    /**
     * 管理员账号
     */
    @ExcelProperty("管理员账号")
    private String adminAccount;

    /**
     * 管理员密码
     */
    @ExcelProperty("管理员密码")
    private String adminPassword;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    /**
     * 失败原因
     */
    @ExcelProperty("失败原因")
    private String failReason;
}
