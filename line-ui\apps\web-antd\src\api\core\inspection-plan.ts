import { requestClient } from '#/api/request';

enum Api {
  inspection = '/api/line/inspection',
}

export interface pagePrams {
  page: number;
  size: number;
  status?: string; // 任务状态
  startDate?: string; // 开始日期
  endDate?: string; // 结束日期
  responsibleUserId?: string; // 巡检负责人id
  planId?: string; // 计划id
}
export interface Result {
  page: number;
  size: number;
  total: number;
  orders: Orders[];
  data: pageResult[];
}

export interface Orders {
  column: string;
  value: string;
  sort: string;
}

export interface pageResult {
  id: number;
  planId: number;
  planName: string;
  taskName: string;
  cycleType: string;
  cycleValue: number;
  scheduledTime: string;
  actualStartTime: string;
  actualEndTime: string;
  status: string;
  responsibleUserId: number;
  responsibleUserName: string;
  cameraCount: number;
  cameraIds: number[];
  completedCount: number;
  remarks: string;
  createTime: string;
  updateTime: string;
}
// 获取巡检计划列表
export const getInspectionPlanList_Api = (params: pagePrams) => {
  return requestClient.get<Result>(`${Api.inspection}/plan/page`, { params });
};

// 添加巡检计划
export interface postParameter {
  name: string;
  cycleType: string; // 字典InspectionCycleEnum
  cycleValue: number;
  cameraIds: number[];
  startDate: string;
  responsibleUserId: number;
  description: string;
  dayValue: number;
  timeSlots: TimeSlots[];
  startTime: string;
  endTime: string;
}

export interface TimeSlots {
  startTime: string;
  endTime: string;
}
export const addInspectionPlan_Api = (data: postParameter) => {
  return requestClient.post(`${Api.inspection}/plan`, data);
};

// 更新巡检计划
export const updateInspectionPlan_Api = (id: string, data: postParameter) => {
  return requestClient.put(`${Api.inspection}/plan/${id}`, data);
};

// 删除巡检计划
export const delInspectionPlan_Api = (id: string) => {
  return requestClient.delete(`${Api.inspection}/plan/${id}`);
};

// 更新巡检计划启用状态
export const updateInspectionPlanStatus_Api = (id: string, status: string) => {
  return requestClient.put(
    `${Api.inspection}/plan/${id}/status?status=${status}`,
  );
};

// 获取巡检计划详情
export const getInspectionPlan_Api = (id: string) => {
  return requestClient.get(`${Api.inspection}/plan/${id}`);
};

// 导出巡检计划
export const exportInspectionPlan_Api = (params: any) => {
  return requestClient.download(`${Api.inspection}/plan/export`, { params });
};

// 分页获取巡检任务
export interface taskPageResult {
  page: number;
  size: number;
  total: number;
  data: taskPageData[];
}

export interface taskPageData {
  id: number;
  planId: number;
  planName: string;
  taskName: string;
  cycleType: string;
  cycleValue: number;
  scheduledTime: string;
  actualStartTime: string;
  actualEndTime: string;
  status: string;
  responsibleUserId: number;
  responsibleUserName: string;
  cameraCount: number;
  completedCount: number;
  remarks: string;
  createTime: string;
  updateTime: string;
}
// 获取任务列表
export const getTaskList_Api = (params: pagePrams) => {
  return requestClient.get<taskPageResult>(`${Api.inspection}/task/page`, {
    params,
  });
};

// 获取任务下的所有摄像头
export interface taskRootObject {
  code: string;
  msg: string;
  request: string;
  server: string;
  data: CameraData[];
}

export interface CameraData {
  id: number;
  taskId: number;
  cameraId: number;
  cameraName: string;
  cameraStatus: string;
  status: string;
  sortOrder: number;
}
// 获取任务下所有摄像头
export const getCameraList_Api = (id: string) => {
  return requestClient.get<taskRootObject>(`${Api.inspection}/task/${id}`);
};

// 提交巡检任务
export interface recordResult {
  image: string;
  taskCameraId: number; // 任务摄像头关联id
  isNormal: boolean; // 是否正常
  remarks: string;
}
export const submitInspectionTask_Api = (data: recordResult) => {
  return requestClient.post(`${Api.inspection}/record`, data);
};

// 巡检任务导出
export const exportInspectionTask_Api = (params: any) => {
  return requestClient.download(`${Api.inspection}/task/export`, { params });
};

// 巡检记录相关接口
export interface InspectionRecordPageParams {
  page: number;
  size: number;
  taskId?: number; // 任务ID
  cameraId?: number; // 摄像头ID
  cameraName?: string; // 摄像头名称
  startDate?: string; // 开始日期
  endDate?: string; // 结束日期
  isNormal?: boolean; // 是否正常
  inspectorId?: number; // 巡检人员ID
  responsibleUserId?: number; // 负责人ID
  cycleType?: string; // 巡检周期
}

export interface InspectionRecordResponse {
  id: number;
  taskId: number;
  taskName: string;
  taskCameraId: number;
  cameraId: number;
  cameraName: string;
  inspectionImage: string;
  isNormal: boolean;
  remarks: string;
  inspectionTime: string;
  inspectorId: number;
  inspectorName: string;
  scheduledStartTime?: string; // 应巡检开始时间
  scheduledEndTime?: string; // 应巡检结束时间
  actualStartTime?: string; // 实际巡查开始时间
  actualEndTime?: string; // 实际巡查结束时间
  cycleType?: string; // 巡检周期
  responsibleUserName?: string; // 负责人姓名
}

export interface InspectionRecordPageResult {
  data: InspectionRecordResponse[];
  page: number;
  size: number;
  total: number;
}

// 分页查询巡检记录
export const getInspectionRecordList_Api = (
  params: InspectionRecordPageParams,
) => {
  return requestClient.get<InspectionRecordPageResult>(
    `${Api.inspection}/record/page`,
    { params },
  );
};

// 查询摄像头的巡检记录
export const getCameraInspectionRecordList_Api = (taskCameraId: string) => {
  return requestClient.get<InspectionRecordPageResult>(
    `${Api.inspection}/record/detail/${taskCameraId}`,
  );
};

// 导出巡检记录
export const exportInspectionRecord_Api = (params: any) => {
  return requestClient.download(`${Api.inspection}/record/export`, { params });
};
