<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import {
  getDepartmentList_Api,
  deleteDepartment_Api,
  updateDepartmentStatus_Api,
} from '#/api/core';
import tableComp from '#/components/TableComp/table.vue';

import EditModal from './components/EditModal.vue';

import { useDictStore } from '#/store';

import { formatDate } from '@vben/utils';

const dictStore = useDictStore();

// 状态选项
const statusOptions = dictStore.getDictOptions('StatusEnum');

// 查询条件
const queryParam = reactive<any>({
  name: undefined, // 部门名称
  status: undefined, // 状态
  page: 1,
  size: 10,
});

const loading = ref(false);
const modalFormRef = ref();

// 表格数据源
const tableData = reactive<any>({
  data: {},
});

// 表格相关配置
const columns = [
  { title: '部门名称', dataIndex: 'name' },
  // { title: '部门负责人', dataIndex: 'leaderName' },
  { title: '备注', dataIndex: 'remark' },
  { title: '状态', dataIndex: 'status' },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    customRender: ({ record }: { record: any }) => {
      return formatDate(record.createTime, 'YYYY-MM-DD HH:mm:ss');
    },
  },
  { title: '操作', dataIndex: 'operation', width: '220px' },
];

// 加载部门列表
const loadDepartmentList = async () => {
  loading.value = true;
  try {
    const res = await getDepartmentList_Api(queryParam);

    // 更新表格数据源
    const temp = {
      data: res.data,
      page: res.page,
      size: res.size,
      total: res.total,
    };
    tableData.data = temp;
  } catch (error) {
    console.error('获取部门列表失败', error);
    message.error('获取部门列表失败');
  } finally {
    loading.value = false;
  }
};
const success = (data: any) => {
  queryParam.page = data.pi;
  queryParam.size = data.ps;
  loadDepartmentList();
};

// 查询
const searchTable = () => {
  queryParam.page = 1;
  loadDepartmentList();
};

// 重置
const resetTable = () => {
  queryParam.name = undefined;
  queryParam.status = undefined;
  queryParam.page = 1;
  queryParam.size = 10;
  loadDepartmentList();
};

// 添加部门
const handleAdd = () => {
  modalFormRef.value.openModal('create');
};

// 编辑部门
const handleEdit = (record: any) => {
  modalFormRef.value.openModal('update', record);
};

// 删除部门
const handleDelete = async (record: any) => {
  try {
    await deleteDepartment_Api(record.id);
    message.success('操作成功');
    loadDepartmentList();
  } catch (error) {
    console.error('删除部门失败', error);
  }
};

// 修改部门状态
const handleStatusChange = (record: any) => {
  const temp = {
    id: record.id,
    status: record.status,
  };

  updateDepartmentStatus_Api(temp)
    .then(() => {
      message.success('操作成功');
      loadDepartmentList();
    })
    .catch((error) => {
      console.error('更新部门状态失败', error);
      loadDepartmentList(); // 刷新数据，恢复原状态
    });
};

// 部门保存成功回调
const handleSaveSuccess = () => {
  loadDepartmentList();
};

onMounted(() => {
  loadDepartmentList();
});
</script>

<template>
  <Page>
    <a-card class="table_header_search mb-5">
      <a-row :gutter="20">
        <a-col :span="6">
          <label>部门名称：</label>
          <div class="table_header_wrp_cont">
            <a-input
              v-model:value="queryParam.name"
              allow-clear
              placeholder="请输入部门名称"
            />
          </div>
        </a-col>
        <a-col :span="6">
          <label>状态：</label>
          <div class="table_header_wrp_cont">
            <a-select
              v-model:value="queryParam.status"
              allow-clear
              placeholder="请选择状态"
              style="width: 100%"
              :field-names="{
                label: 'dictLabel',
                value: 'dictValue',
              }"
              :options="statusOptions"
            >
            </a-select>
          </div>
        </a-col>

        <a-col :span="4">
          <a-space>
            <a-button type="primary" class="searchBtn" @click="searchTable">
              查询
            </a-button>
            <a-button class="refBtn" @click="resetTable">重置</a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <a-card size="small">
      <div class="table_action_btn_wrp">
        <a-button class="addBtn" type="primary" @click="handleAdd">
          新建
        </a-button>
      </div>

      <table-Comp
        :columns="columns"
        :data-source="tableData.data"
        :loading="loading"
        :scroll="{ x: 1200 }"
        @is-loading-fuc="(e) => (loading = e)"
        @success="success"
      >
        <!-- 状态列 -->
        <template #status="{ record }">
          <a-switch
            v-model:checked="record.status"
            checkedValue="ENABLE"
            unCheckedValue="DISABLE"
            @change="handleStatusChange(record)"
          />
        </template>

        <!-- 备注列 -->
        <template #remark="{ record }">
          <a-tooltip
            v-if="record.remark && record.remark.length > 20"
            :title="record.remark"
          >
            <span>{{ record.remark.substring(0, 20) }}...</span>
          </a-tooltip>
          <span v-else>{{ record.remark || '-' }}</span>
        </template>

        <!-- 操作列 -->
        <template #operation="{ record }">
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-popconfirm
            title="数据删除后不可恢复，请确认后继续?"
            @confirm="handleDelete(record)"
          >
            <a-button type="link" danger>删除</a-button>
          </a-popconfirm>
        </template>
      </table-Comp>
    </a-card>

    <!-- 部门弹窗 -->
    <EditModal ref="modalFormRef" @success="handleSaveSuccess" />
  </Page>
</template>
