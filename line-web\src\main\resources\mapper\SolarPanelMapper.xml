<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdyl.line.web.mapper.SolarPanelMapper">
    <select id="selectLatestByTerminalId" resultType="com.bdyl.line.web.entity.SolarPanelEntity">
        SELECT *
        FROM t_solar_panel
        WHERE terminal_id = #{terminalId}
        ORDER BY create_time DESC LIMIT 1
    </select>

    <select id="selectHourStatByTerminalIdAndDay" resultType="com.bdyl.line.web.model.response.solarpanel.SolarPanelHourStatItem">
        SELECT
            HOUR(create_time) AS hour,
            AVG(battery_temperature) AS batteryTemperature,
            AVG(battery_voltage) AS batteryVoltage,
            AVG(battery_current) AS batteryCurrent,
            AVG(battery_level) AS batteryLevel,
            AVG(dc_load_voltage) AS dcLoadVoltage,
            AVG(dc_load_current) AS dcLoadCurrent,
            AVG(dc_load_power) AS dcLoadPower,
            AVG(dc_load_energy_today) AS dcLoadEnergyToday,
            AVG(pv_voltage) AS pvVoltage,
            AVG(pv_current) AS pvCurrent,
            AVG(pv_power) AS pvPower
        FROM t_solar_panel
        WHERE terminal_id = #{terminalId}
          AND create_time &gt;= #{start}
          AND create_time &lt;= #{end}
        GROUP BY HOUR(create_time)
        ORDER BY hour
    </select>
</mapper> 