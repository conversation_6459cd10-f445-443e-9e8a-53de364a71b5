<script lang="ts" setup>
import { computed, nextTick, reactive, ref } from 'vue';

import { message } from 'ant-design-vue';

import { addScene_Api, updateScene_Api } from '#/api/core/scene';

const emit = defineEmits(['success', 'update:open']);

const open = ref<boolean>(false);
const title = ref<string>('新增场景');
const formRef = ref();
const loading = ref<boolean>(false);

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  description: '',
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入场景名称', trigger: 'blur' },
    { max: 30, message: '场景名称不能超过30个字符', trigger: 'blur' },
  ],
  description: [
    { max: 200, message: '场景描述不能超过200个字符', trigger: 'blur' },
  ],
};

// 是否为编辑模式
const isEdit = computed(() => {
  return !!formData.id;
});

// 重置表单
const resetForm = () => {
  formData.id = '';
  formData.name = '';
  formData.description = '';
  nextTick(() => {
    formRef.value?.resetFields();
  });
};

// 关闭弹窗
const closeModal = () => {
  open.value = false;
  resetForm();
};

// 打开弹窗
const openModal = (record?: any) => {
  resetForm();
  open.value = true;
  
  if (record) {
    // 编辑模式
    title.value = '编辑场景';
    nextTick(() => {
      formData.id = record.id;
      formData.name = record.name;
      formData.description = record.description || '';
    });
  } else {
    // 新增模式
    title.value = '新增场景';
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;
    
    if (isEdit.value) {
      // 更新场景
      await updateScene_Api(formData.id, {
        name: formData.name,
        description: formData.description,
      });
      message.success('更新成功');
    } else {
      // 新增场景
      await addScene_Api({
        name: formData.name,
        description: formData.description,
      });
      message.success('添加成功');
    }
    
    closeModal();
    emit('success');
  } catch (error) {
    console.error('保存场景失败', error);
  } finally {
    loading.value = false;
  }
};

// 暴露方法给父组件调用
defineExpose({
  openModal,
});
</script>

<template>
  <a-modal
    v-model:open="open"
    :title="title"
    :width="500"
    :maskClosable="false"
    @cancel="closeModal"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
    >
      <a-form-item label="场景名称" name="name">
        <a-input
          v-model:value="formData.name"
          placeholder="请输入场景名称"
          :maxlength="30"
          show-count
        />
      </a-form-item>

      <a-form-item label="场景描述" name="description">
        <a-textarea
          v-model:value="formData.description"
          placeholder="请输入场景描述"
          :maxlength="200"
          :auto-size="{ minRows: 3, maxRows: 5 }"
          show-count
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>

<style lang="scss" scoped></style>