package com.bdyl.line.common.exception;

import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import com.bdyl.boot.JsonResult;
import com.bdyl.boot.exception.BizException;
import com.bdyl.line.common.constant.error.CommonError;

/**
 * 全局异常处理器
 * <p>
 * 用于捕获并处理应用程序中发生的各种异常，确保返回统一格式的错误响应。 根据不同的异常类型，设置适当的HTTP状态码和错误信息，提供友好的用户体验。
 * </p>
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     *
     * @param e 业务异常对象
     * @param request HTTP请求对象，用于获取请求路径等信息
     * @return 包含错误码和错误信息的统一响应对象
     */
    @ExceptionHandler(BizException.class)
    public JsonResult<?> handleBusinessException(BizException e, HttpServletRequest request) {
        log.warn("业务异常: {} {}", request.getMethod(), request.getRequestURI(), e);
        return JsonResult.failed(e.getCode(), e.getMessage(), null);
    }

    /**
     * 处理参数校验异常
     * <p>
     * 处理通过@Valid注解验证失败产生的异常
     * </p>
     *
     * @param e 参数校验异常对象
     * @param request HTTP请求对象，用于获取请求路径等信息
     * @return 包含错误码和错误信息的统一响应对象
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public JsonResult<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException e,
        HttpServletRequest request) {
        String message = e.getBindingResult().getFieldErrors().stream().map(FieldError::getDefaultMessage)
            .collect(Collectors.joining(", "));
        log.warn("参数校验异常: {} {}, {}", request.getMethod(), request.getRequestURI(), message);
        return JsonResult.failed(CommonError.PARAM_ERROR.getCode(), message, null);
    }

    /**
     * 处理参数绑定异常
     * <p>
     * 处理表单数据绑定失败产生的异常
     * </p>
     *
     * @param e 参数绑定异常对象
     * @param request HTTP请求对象，用于获取请求路径等信息
     * @return 包含错误码和错误信息的统一响应对象
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public JsonResult<?> handleBindException(BindException e, HttpServletRequest request) {
        String message = e.getBindingResult().getFieldErrors().stream().map(FieldError::getDefaultMessage)
            .collect(Collectors.joining(", "));
        log.warn("参数绑定异常: {} {}, {}", request.getMethod(), request.getRequestURI(), message);
        return JsonResult.failed(CommonError.BAD_REQUEST.getCode(), message, null);
    }

    /**
     * 处理约束违反异常
     * <p>
     * 处理通过@Validated注解验证失败产生的异常
     * </p>
     *
     * @param e 约束违反异常对象
     * @param request HTTP请求对象，用于获取请求路径等信息
     * @return 包含错误码和错误信息的统一响应对象
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public JsonResult<?> handleConstraintViolationException(ConstraintViolationException e,
        HttpServletRequest request) {
        String message = e.getMessage();
        log.warn("约束违反异常: {} {}, {}", request.getMethod(), request.getRequestURI(), message);
        return JsonResult.failed(CommonError.PARAM_ERROR.getCode(), message, null);
    }

    /**
     * 处理请求参数缺失异常
     * <p>
     * 处理必要的请求参数未提供的情况
     * </p>
     *
     * @param e 请求参数缺失异常对象
     * @param request HTTP请求对象，用于获取请求路径等信息
     * @return 包含错误码和错误信息的统一响应对象
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public JsonResult<?> handleMissingServletRequestParameterException(MissingServletRequestParameterException e,
        HttpServletRequest request) {
        log.warn("请求参数缺失: {} {}, {}", request.getMethod(), request.getRequestURI(), e.getMessage());
        return JsonResult.failed(CommonError.PARAM_ERROR.getCode(), "缺少必要参数: " + e.getParameterName(), null);
    }

    /**
     * 处理请求方法不支持异常
     * <p>
     * 处理使用了不支持的HTTP方法的情况（如使用POST请求访问只允许GET的接口）
     * </p>
     *
     * @param e 请求方法不支持异常对象
     * @param request HTTP请求对象，用于获取请求路径等信息
     * @return 包含错误码和错误信息的统一响应对象
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public JsonResult<?> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e,
        HttpServletRequest request) {
        log.warn("请求方法不支持: {} {}", request.getMethod(), request.getRequestURI());
        return JsonResult.failed(CommonError.METHOD_NOT_ALLOWED.getCode(), CommonError.METHOD_NOT_ALLOWED.getMessage(),
            null);
    }

    /**
     * 处理无处理器异常
     * <p>
     * 处理请求路径无对应处理器的情况（404错误）
     * </p>
     *
     * @param e 无处理器异常对象
     * @param request HTTP请求对象，用于获取请求路径等信息
     * @return 包含错误码和错误信息的统一响应对象
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public JsonResult<?> handleNoHandlerFoundException(NoHandlerFoundException e, HttpServletRequest request) {
        log.warn("资源不存在: {} {}", request.getMethod(), request.getRequestURI());
        return JsonResult.failed(CommonError.NOT_FOUND.getCode(), CommonError.NOT_FOUND.getMessage(), null);
    }

    /**
     * 处理参数类型不匹配异常
     * <p>
     * 处理请求参数类型与方法参数类型不匹配的情况（如API需要整数，但提供了字符串）
     * </p>
     *
     * @param e 参数类型不匹配异常对象
     * @param request HTTP请求对象，用于获取请求路径等信息
     * @return 包含错误码和错误信息的统一响应对象
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public JsonResult<?> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e,
        HttpServletRequest request) {
        log.warn("参数类型不匹配: {} {}, 参数: {}, 类型: {}", request.getMethod(), request.getRequestURI(), e.getName(),
            e.getRequiredType().getSimpleName());
        return JsonResult.failed(CommonError.PARAM_ERROR.getCode(), "参数类型不匹配: " + e.getName(), null);
    }

    /**
     * 处理HTTP消息不可读异常
     * <p>
     * 处理请求体格式错误导致无法解析的情况（如JSON格式错误）
     * </p>
     *
     * @param e HTTP消息不可读异常对象
     * @param request HTTP请求对象，用于获取请求路径等信息
     * @return 包含错误码和错误信息的统一响应对象
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public JsonResult<?> handleHttpMessageNotReadableException(HttpMessageNotReadableException e,
        HttpServletRequest request) {
        log.warn("HTTP消息不可读: {} {}", request.getMethod(), request.getRequestURI(), e);
        return JsonResult.failed(CommonError.PARAM_ERROR.getCode(), "HTTP消息不可读", null);
    }

    /**
     * 处理非法参数异常
     * <p>
     * 处理API内部校验参数时抛出的IllegalArgumentException异常
     * </p>
     *
     * @param e 非法参数异常对象
     * @param request HTTP请求对象，用于获取请求路径等信息
     * @return 包含错误码和错误信息的统一响应对象
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public JsonResult<?> handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
        log.warn("非法参数: {} {}", request.getMethod(), request.getRequestURI(), e);
        return JsonResult.failed(CommonError.PARAM_ERROR.getCode(), e.getMessage(), null);
    }

    /**
     * 处理所有未知异常
     * <p>
     * 处理所有未被其他异常处理器捕获的异常，作为最后的防护
     * </p>
     *
     * @param e 未知异常对象
     * @param request HTTP请求对象，用于获取请求路径等信息
     * @return 包含错误码和错误信息的统一响应对象
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public JsonResult<?> handleException(Exception e, HttpServletRequest request) {
        log.error("系统异常: {} {}", request.getMethod(), request.getRequestURI(), e);
        return JsonResult.failed(CommonError.ERROR.getCode(), "系统内部错误，请稍后重试", null);
    }
}
