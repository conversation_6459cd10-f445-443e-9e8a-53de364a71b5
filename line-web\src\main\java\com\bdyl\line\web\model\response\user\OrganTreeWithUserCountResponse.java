package com.bdyl.line.web.model.response.user;

import java.util.List;

import lombok.Data;

/**
 * 组织树节点响应对象，包含用户数量。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class OrganTreeWithUserCountResponse {
    /**
     * 组织ID
     */
    private Long id;
    /**
     * 组织名称
     */
    private String name;
    /**
     * 子组织
     */
    private List<OrganTreeWithUserCountResponse> children;
    /**
     * 用户数量
     */
    private Integer userCount;
}
