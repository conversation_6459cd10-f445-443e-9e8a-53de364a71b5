---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: *.java
alwaysApply: false

## Java代码风格规范

### 1. 文件组织

- 每个Java源文件只包含一个顶级类
- 文件编码使用UTF-8
- 文件末尾必须有一个空行
- 避免单个文件过长，建议不超过2000行
- 按以下顺序组织文件内容：
  1. 包声明
  2. 导入语句
  3. 类注释
  4. 类定义
  5. 字段
  6. 构造函数
  7. 方法

### 2. 命名规范

- **类名**：使用大驼峰命名法（PascalCase）
  ```java
  public class DeviceManager {}
  public class PluginLoader {}
  ```

- **方法名**：使用小驼峰命名法（camelCase）
  ```java
  public void loadPlugin() {}
  public String getDeviceStatus() {}
  ```

- **变量名**：使用小驼峰命名法
  ```java
  private String deviceId;
  private Map<String, Object> pluginConfig;
  ```

- **常量名**：全大写，单词间用下划线分隔
  ```java
  public static final int MAX_RETRY_COUNT = 3;
  private static final String DEFAULT_TIMEOUT = "30s";
  ```

### 3. 代码格式

- **缩进**：使用4个空格，不使用Tab
- **行长度**：最大120字符
- **方法长度**：建议不超过50行
- **参数数量**：建议不超过5个
- **return语句**：每个方法中return语句不超过5个
- **大括号风格**：
  ```java
  if (condition) {
      // 代码
  }
  ```
- **空格规则**：
  - 关键字后加空格：if、for、while等
  - 运算符前后加空格：+、-、*、/、=等
  - 逗号后加空格
  - 左大括号前加空格

### 4. 注释规范

- **类注释**：必须包含类的功能描述
  ```java
  /**
   * 设备管理器类，负责设备的注册、状态管理等功能
   *
   * <AUTHOR>
   * @since 版本号
   */

### 5. 最佳实践

- **异常处理**：
  - 必须处理或声明所有检查型异常
  - 使用自定义异常类进行业务异常处理
  - 避免捕获通用Exception
  ```java
  try {
      // 业务代码
  } catch (SpecificException e) {
      // 具体异常处理
  }
  ```

- **资源管理**：
  - 使用try-with-resources管理资源
  ```java
  try (InputStream is = new FileInputStream(file)) {
      // 使用资源
  }
  ```

- **集合使用**：
  - 优先使用接口类型声明
  - 考虑线程安全性
  ```java
  // 推荐
  Map<String, Object> map = new ConcurrentHashMap<>();
  // 不推荐
  HashMap<String, Object> map = new HashMap<>();
  ```

### 6. 测试规范

- **命名规范**：
  - 测试类名以Test结尾
  - 测试方法名清晰表达测试目的
  ```java
  public class DeviceManagerTest {
      @Test
      public void shouldLoadPluginSuccessfully() {}
  }
  ```

- **测试原则**：
  - 每个测试方法只测试一个场景
  - 使用合适的断言方法
  - 准备测试数据和清理测试数据

### 7. 日志规范

- 使用SLF4J作为日志门面
- 合理使用日志级别：
  ```java
  // 错误信息
  logger.error("Failed to load plugin: {}", pluginId, exception);
  // 警告信息
  logger.warn("Plugin configuration not found: {}", pluginId);
  // 重要业务信息
  logger.info("Plugin loaded successfully: {}", pluginId);
  // 调试信息
  logger.debug("Processing plugin configuration: {}", config);
