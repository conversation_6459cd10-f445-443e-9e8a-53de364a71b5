import { requestClient } from '#/api/request';

enum Api {
  role = '/api/uaa/roles',
}

// 分页查询角色
export interface RoleParams {
  page: number;
  size: number;
  name?: string;
  status?: string;
}

// 创建/修改角色
export interface RoleParameter {
  id?: number;
  name: string;
  code: string;
  description?: string;
  status: string;
}

// 角色权限参数
export interface RolePermissionsParameter {
  roleId: number;
  permissionIds: number[];
}

// 分页获取角色列表
export const getRoleList_Api = (params: RoleParams) => {
  return requestClient.get(`${Api.role}`, { params });
};

// 创建角色
export const addRole_Api = (data: RoleParameter) => {
  return requestClient.post(`${Api.role}`, data);
};

// 修改角色
export const updateRole_Api = (id: number, data: RoleParameter) => {
  return requestClient.put(`${Api.role}/${id}`, data);
};

// 删除角色
export const deleteRole_Api = (id: number) => {
  return requestClient.delete(`${Api.role}/${id}`);
};

// 获取角色详情
export const getRole_Api = (id: any) => {
  return requestClient.get(`${Api.role}/${id}`);
};

// 获取角色权限
export const getRolePermissions_Api = async (roleId: number) => {
  return await requestClient.get(`/api/uaa/permissions/role/${roleId}`);
};

// 为角色分配权限
export const assignRolePermissions_Api = (
  id: number,
  data: RolePermissionsParameter,
) => {
  return requestClient.post(`${Api.role}/${id}/permissions`, data);
};
