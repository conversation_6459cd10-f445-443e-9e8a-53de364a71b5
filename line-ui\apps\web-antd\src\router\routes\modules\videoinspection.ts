import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    name: 'VideoInspection',
    path: '/video-inspection',
    meta: {
      icon: 'mdi:video-marker', // 视频巡检父路由图标（可根据实际需求替换mdi图标）
      order: 20, // 排序建议（参考basemanagement=20, videomonitoring=25, alarmmanagement=30）
      title: $t('page.videoinspection.title'), // 需在国际化文件中添加对应翻译项
      authority: ['inspection:menu'],
    },
    children: [
      {
        name: 'InspectionPlan',
        path: 'inspection-plan',
        component: () =>
          import('#/views/video-inspection/inspection-plan/index.vue'), // 需确保对应视图文件存在
        meta: {
          icon: 'mdi:calendar-check', // 巡检计划图标
          order: 40,
          title: $t('page.videoinspection.planTitle'), // 需在国际化文件中添加对应翻译项
          authority: ['inspection:plan:menu'],
        },
      },
      {
        name: 'InspectionTask',
        path: 'inspection-task',
        component: () =>
          import('#/views/video-inspection/inspection-task/index.vue'), // 需确保对应视图文件存在
        meta: {
          icon: 'mdi:clipboard-check', // 巡检任务图标
          order: 45,
          title: $t('page.videoinspection.taskTitle'), // 需在国际化文件中添加对应翻译项
          authority: ['inspection:task:menu'],
        },
      },
      {
        name: 'InspectionAbnormalRecord',
        path: 'inspection-abnormal-record',
        component: () =>
          import(
            '#/views/video-inspection/inspection-abnormal-record/index.vue'
          ), // 需确保对应视图文件存在
        meta: {
          icon: 'mdi:alert-circle', // 异常记录图标
          order: 50,
          title: $t('page.videoinspection.abnormalTitle'), // 需在国际化文件中添加对应翻译项
          authority: ['inspection:task:camera:menu'],
        },
      },
    ],
  },
];

export default routes;
