package com.bdyl.line.web.service;

import java.time.LocalDate;

import com.baomidou.mybatisplus.extension.service.IService;

import com.bdyl.boot.data.query.Page;
import com.bdyl.line.web.entity.SolarPanelEntity;
import com.bdyl.line.web.model.request.solarpanel.SolarPanelPageRequest;
import com.bdyl.line.web.model.request.solarpanel.SolarPanelRequest;
import com.bdyl.line.web.model.response.solarpanel.SolarPanelHourStatItem;
import com.bdyl.line.web.model.response.solarpanel.SolarPanelResponse;

/**
 * 太阳能电池板服务接口。
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface SolarPanelService extends IService<SolarPanelEntity> {
    /**
     * 新增太阳能电池板
     *
     * @param request 请求对象
     * @param currentOrganId
     * @return 是否成功
     */
    boolean create(SolarPanelRequest request, Long currentOrganId);

    /**
     * 分页查询太阳能电池板
     *
     * @param request 分页请求
     * @return 分页结果
     */
    Page<SolarPanelResponse> page(SolarPanelPageRequest request);

    /**
     * 查询终端最新实时数据
     *
     * @param terminalId 终端ID
     * @return 实时数据
     */
    SolarPanelResponse getLatestByTerminalId(Long terminalId);

    /**
     * 查询终端某天24小时统计数据
     *
     * @param terminalId 终端ID
     * @param day 日期(yyyy-MM-dd)
     * @return 24小时统计
     */
    java.util.List<SolarPanelHourStatItem> stat24Hour(Long terminalId, LocalDate day);
}
