package com.bdyl.line.web.ws;

import java.util.concurrent.Executor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;
import org.springframework.web.socket.server.support.DefaultHandshakeHandler;
import org.springframework.web.socket.server.support.HttpSessionHandshakeInterceptor;

/**
 * WebSocket配置类 配置WebSocket的端点、拦截器和握手处理器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    /**
     * 成员变量
     */
    @Autowired
    private ApplicationContext applicationContext;

    @Bean
    public ServletServerContainerFactoryBean createWebSocketContainer() {
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        // 设置消息缓冲区大小
        container.setMaxTextMessageBufferSize(8192);
        container.setMaxBinaryMessageBufferSize(8192);
        // 设置空闲超时时间（毫秒）
        container.setMaxSessionIdleTimeout(600000L);
        return container;
    }

    @Bean("webSocketExecutor")
    public Executor webSocketExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("ws-exec-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }

    /**
     * 保存视频的线程池
     *
     * @return 线程池
     */
    @Bean("videoSaveExecutor")
    public Executor videoSaveExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10); // 核心线程数5个，控制并发保存数量
        executor.setMaxPoolSize(20); // 最大线程数10个
        executor.setQueueCapacity(200); // 队列容量200，防止任务丢失
        executor.setThreadNamePrefix("video-save-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(120); // 等待2分钟完成所有任务
        executor.initialize();
        return executor;
    }

    /**
     * 注册WebSocket处理器 配置WebSocket的访问路径、拦截器和握手处理器
     *
     * @param registry WebSocket处理器注册表
     */
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 通过ApplicationContext获取WsHandler，避免循环依赖
        WsHandler wsHandler = applicationContext.getBean(WsHandler.class);
        registry.addHandler(wsHandler, "/ws/line/{deviceCode}").addInterceptors(new HttpSessionHandshakeInterceptor())
            .setHandshakeHandler(new DefaultHandshakeHandler()).setAllowedOrigins("*");
    }
}
