package com.bdyl.line.web.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 巡检记录实体类，对应巡检记录表。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_inspection_record", autoResultMap = true)
public class InspectionRecordEntity extends BaseEntity {
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 组织ID
     */
    private Long organId;

    /**
     * 巡检任务ID
     */
    private Long taskId;

    /**
     * 任务摄像头关联ID
     */
    private Long taskCameraId;

    /**
     * 摄像头ID
     */
    private Long cameraId;

    /**
     * 摄像头名称
     */
    private String cameraName;

    /**
     * 巡检图片(单张)
     */
    private String inspectionImage;

    /**
     * 是否正常
     */
    private Boolean isNormal;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 巡检时间
     */
    private LocalDateTime inspectionTime;

    /**
     * 巡检人员ID
     */
    private Long inspectorId;

    /**
     * 巡检人员姓名
     */
    private String inspectorName;

    /**
     * 巡检周期类型 {@link com.bdyl.line.common.constant.enums.InspectionCycleEnum}
     */
    private String cycleType;

    /**
     * 巡检负责人ID
     */
    private Long responsibleUserId;

    /**
     * 巡检负责人姓名
     */
    private String responsibleUserName;
}
