package com.bdyl.line.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 巡检任务摄像头状态枚举
 *
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@AllArgsConstructor
public enum InspectionTaskCameraStatusEnum {

    /**
     * 待巡检
     */
    PENDING("PENDING", "待巡检", "摄像头待巡检"),
    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成", "摄像头巡检已完成");

    /**
     * value
     */
    private final String value;
    /**
     * 名称
     */
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据枚举值获取枚举类
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static InspectionTaskCameraStatusEnum fromValue(String value) {
        for (InspectionTaskCameraStatusEnum status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown status: " + value);
    }
}
