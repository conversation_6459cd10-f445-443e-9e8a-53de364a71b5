package com.bdyl.line.web.ws;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 初始化CameraWebSocketClient的事件发布器
 *
 * <AUTHOR>
 * @since 1.0
 */
@Component
public class CameraWebSocketClientPublisherInitializer implements ApplicationContextAware {
    /**
     * 初始化CameraWebSocketClient的事件发布器
     *
     * @param applicationContext applicationContext
     * @throws BeansException BeansException
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        CameraWebSocketClient.setPublisher(applicationContext);
    }
}
