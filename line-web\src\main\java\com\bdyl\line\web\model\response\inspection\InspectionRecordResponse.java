package com.bdyl.line.web.model.response.inspection;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 巡检记录响应对象
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class InspectionRecordResponse {
    /**
     * 记录ID
     */
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务摄像头关联ID
     */
    private Long taskCameraId;

    /**
     * 摄像头ID
     */
    private Long cameraId;

    /**
     * 摄像头名称
     */
    private String cameraName;

    /**
     * 巡检图片(单张)
     */
    private String inspectionImage;
    /**
     * 是否正常
     */
    private Boolean isNormal;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 巡检时间
     */
    private LocalDateTime inspectionTime;

    /**
     * 巡检人员ID
     */
    private Long inspectorId;

    /**
     * 巡检人员姓名
     */
    private String inspectorName;
    /**
     * 巡检周期 {@link com.bdyl.line.common.constant.enums.InspectionCycleEnum}
     */
    private String inspectionCycle;

    /**
     * 计划开始时间
     */
    private LocalDateTime scheduledStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime scheduledEndTime;

    /**
     * 巡检周期类型 {@link com.bdyl.line.common.constant.enums.InspectionCycleEnum}
     */
    private String cycleType;

    /**
     * 巡检负责人ID
     */
    private Long responsibleUserId;

    /**
     * 巡检负责人姓名
     */
    private String responsibleUserName;
}
