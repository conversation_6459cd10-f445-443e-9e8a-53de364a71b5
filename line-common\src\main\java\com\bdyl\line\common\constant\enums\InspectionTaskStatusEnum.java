package com.bdyl.line.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 巡检任务状态枚举
 *
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@AllArgsConstructor
public enum InspectionTaskStatusEnum {

    /**
     * 待巡检
     */
    PENDING("PENDING", "待巡检", "任务已创建，等待巡检"),
    /**
     * 进行中
     */
    IN_PROGRESS("IN_PROGRESS", "进行中", "任务正在执行中"),
    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成", "任务已完成"),
    /**
     * 漏检
     */
    MISSED("MISSED", "漏检", "任务超时未完成");

    /**
     * value
     */
    private final String value;
    /**
     * 名称
     */
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据枚举值获取枚举类
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static InspectionTaskStatusEnum fromValue(String value) {
        for (InspectionTaskStatusEnum status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown status: " + value);
    }
}
