package com.bdyl.line.web.model.response.permission;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 权限导出DTO
 */
@Data
public class PermissionExportDTO {

    /**
     * 权限ID
     */
    @ExcelProperty("权限ID")
    private Long id;
    /**
     * 权限名称
     */
    @ExcelProperty("权限名称")
    private String name;

    /**
     * 权限编码
     */
    @ExcelProperty("权限编码")
    private String code;

    /**
     * 权限类型
     */
    @ExcelProperty("权限类型")
    private String type;

    /**
     * api路径
     */
    @ExcelProperty("API路径")
    private String apiPath;

    /**
     * API方法
     */
    @ExcelProperty("API方法")
    private String apiMethod;

    /**
     * 父权限ID
     */
    @ExcelProperty("父权限ID")
    private Long parentId;

    /**
     * 排序号
     */
    @ExcelProperty("排序号")
    private Integer sortOrder;

    /**
     * 状态
     */
    @ExcelProperty("状态")
    private String status;

    /**
     * 描述
     */
    @ExcelProperty("描述")
    private String description;
}
