<script setup lang="ts">
import { h, onMounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { message } from 'ant-design-vue';

import { getIotAlarmList_Api, exportIotAlarm_Api } from '#/api/core/iot-alarm';
import DictTag from '#/components/DictTag/index.vue';
import tableComp from '#/components/TableComp/table.vue';
import { useDictStore } from '#/store/dict';
import { formatDate } from '@vben/utils';

import { downloadFileFromBlob } from '@vben/utils';

const { getDictOptions } = useDictStore();

// 查询条件
const queryParam = reactive<any>({
  type: undefined, // 报警类型
  terminalName: undefined, // 终端名称
  alarmTime: [], // 报警时间
  page: 1,
  size: 10,
});

const loading = ref(false);

// 表格数据源
const tableData = reactive<any>({
  data: {},
});

// 表格相关配置
const columns = [
  {
    title: '报警类型',
    dataIndex: 'type',
    customRender: ({ record }: { record: any }) => {
      return h(DictTag, {
        dictName: 'IotAlertTypeEnum',
        dictValue: record.type,
      });
    },
  },
  { title: '报警内容', dataIndex: 'content' },
  { title: '终端名称', dataIndex: 'terminalName' },
  { title: '区域', dataIndex: 'region' },
  { title: '地址', dataIndex: 'address' },
  {
    title: '报警时间',
    dataIndex: 'createTime',
    customRender: ({ record }: { record: any }) => {
      return formatDate(record.createTime, 'YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ record }: { record: any }) => {
      return h(DictTag, {
        dictName: 'IotAlertStatusEnum',
        dictValue: record.status,
      });
    },
  },
];

// 加载IOT告警列表
const loadAlarmList = async () => {
  loading.value = true;
  try {
    queryParam.createTimeStart = queryParam.alarmTime
      ? queryParam.alarmTime[0]?.format('YYYY-MM-DD HH:mm:ss')
      : undefined;
    queryParam.createTimeEnd = queryParam.alarmTime
      ? queryParam.alarmTime[1]?.format('YYYY-MM-DD HH:mm:ss')
      : undefined;

    const res = await getIotAlarmList_Api(queryParam);

    // 更新表格数据源
    const temp = {
      data: res.data || [],
      page: res.page || 1,
      size: res.size || 10,
      total: res.total || 0,
    };
    tableData.data = temp;
  } catch (error) {
    console.error('获取IOT告警列表失败', error);
    message.error('获取IOT告警列表失败');
  } finally {
    loading.value = false;
  }
};

// 分页变化
const handleTableChange = (data: any) => {
  queryParam.page = data.pn;
  queryParam.size = data.ps;
  loadAlarmList();
};

// 查询
const searchTable = () => {
  queryParam.page = 1;
  loadAlarmList();
};

// 重置
const resetTable = () => {
  queryParam.type = undefined;
  queryParam.terminalName = undefined;
  queryParam.alarmTime = [];
  queryParam.page = 1;
  loadAlarmList();
};

// 表格操作成功回调
const success = () => {
  loadAlarmList();
};

// 导出
const handleExport = async () => {
  const params = {
    terminalName: queryParam.terminalName,
    type: queryParam.type,
    createTimeStart: queryParam.alarmTime
      ? queryParam.alarmTime[0]?.format('YYYY-MM-DD HH:mm:ss')
      : undefined,
    createTimeEnd: queryParam.alarmTime
      ? queryParam.alarmTime[1]?.format('YYYY-MM-DD HH:mm:ss')
      : undefined,
  };
  const res = await exportIotAlarm_Api(params);
  downloadFileFromBlob({ source: res, fileName: '物联告警数据.xlsx' });
};

onMounted(() => {
  loadAlarmList();
});
</script>

<template>
  <Page>
    <a-card class="table_header_search mb-5">
      <a-row :gutter="20">
        <a-col :span="6">
          <label>报警类型：</label>
          <div class="table_header_wrp_cont">
            <a-select
              v-model:value="queryParam.type"
              allow-clear
              placeholder="请选择报警类型"
              style="width: 100%"
            >
              <a-select-option
                v-for="item in getDictOptions('IotAlertTypeEnum')"
                :key="item.dictValue"
                :value="item.dictValue"
              >
                {{ item.dictLabel }}
              </a-select-option>
            </a-select>
          </div>
        </a-col>
        <a-col :span="6">
          <label>报警时间：</label>
          <div class="table_header_wrp_cont">
            <a-range-picker
              v-model:value="queryParam.alarmTime"
              :placeholder="['开始时间', '结束时间']"
              format="YYYY-MM-DD HH:mm:ss"
              show-time
              style="width: 100%"
            />
          </div>
        </a-col>
        <a-col :span="6">
          <label>终端名称：</label>
          <div class="table_header_wrp_cont">
            <a-input
              v-model:value="queryParam.terminalName"
              allow-clear
              placeholder="请输入终端名称"
            />
          </div>
        </a-col>

        <a-col :span="4">
          <a-space>
            <a-button type="primary" class="searchBtn" @click="searchTable">
              查询
            </a-button>
            <a-button class="refBtn" @click="resetTable">重置</a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <a-card size="small">
      <div class="table_action_btn_wrp">
        <a-button class="addBtn" @click="handleExport"> 导出 </a-button>
      </div>
      <tableComp
        :columns="columns"
        :data-source="tableData.data"
        :loading="loading"
        @is-loading-fuc="(e) => (loading = e)"
        @success="success"
        @table-change="handleTableChange"
      >
        <!-- 报警内容列 -->
        <template #content="{ record }">
          <a-tooltip
            v-if="record.content && record.content.length > 20"
            :title="record.content"
          >
            <span>{{ record.content.substring(0, 20) }}...</span>
          </a-tooltip>
          <span v-else>{{ record.content || '-' }}</span>
        </template>
        <!-- 描述列 -->
        <template #address="{ record }">
          <a-tooltip
            v-if="record.address && record.address.length > 20"
            :title="record.address"
          >
            <span>{{ record.address.substring(0, 20) }}...</span>
          </a-tooltip>
          <span v-else>{{ record.address || '-' }}</span>
        </template>
      </tableComp>
    </a-card>
  </Page>
</template>

<style lang="scss" scoped>
.table_header_search {
  .table_header_wrp_cont {
    margin-top: 5px;
  }

  label {
    font-weight: 500;
    color: #333;
  }
}

.searchBtn {
  background: #1890ff;
  border-color: #1890ff;
}

.refBtn {
  background: #fff;
  border-color: #d9d9d9;
  color: #666;
}
</style>
