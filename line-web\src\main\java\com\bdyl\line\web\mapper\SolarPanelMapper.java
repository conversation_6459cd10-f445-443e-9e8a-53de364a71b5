package com.bdyl.line.web.mapper;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bdyl.line.web.entity.SolarPanelEntity;
import com.bdyl.line.web.model.response.solarpanel.SolarPanelHourStatItem;

/**
 * 太阳能电池板Mapper接口。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Mapper
public interface SolarPanelMapper extends BaseMapper<SolarPanelEntity> {
    /**
     * 查询终端最新一条数据
     *
     * @param terminalId 终端ID
     * @return 实体
     */
    SolarPanelEntity selectLatestByTerminalId(@Param("terminalId") Long terminalId);

    /**
     * 查询终端某天每小时统计
     *
     * @param terminalId 终端ID
     * @param start 开始时间
     * @param end 结束时间
     * @return 每小时统计列表
     */
    List<SolarPanelHourStatItem> selectHourStatByTerminalIdAndDay(@Param("terminalId") Long terminalId,
        @Param("start") LocalDateTime start, @Param("end") LocalDateTime end);
}
