package com.bdyl.line.web.model.response.user;

import java.util.List;

import lombok.Data;

/**
 * 行政区划树节点响应对象，包含用户数量和是否有组织的标记。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class RegionTreeWithUserCountResponse {
    /**
     * 组织ID
     */
    private Long id;
    /**
     * 区划编码
     */
    private String code;
    /**
     * 区划名称
     */
    private String name;
    /**
     * 子区划
     */
    private List<RegionTreeWithUserCountResponse> children;
    /**
     * 用户数量
     */
    private Integer userCount;
    /**
     * 是否有对应的组织
     */
    private Boolean hasOrgan;
}
