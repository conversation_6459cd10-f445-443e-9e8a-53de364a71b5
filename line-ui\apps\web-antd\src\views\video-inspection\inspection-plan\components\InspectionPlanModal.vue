<script lang="ts" setup>
import { computed, nextTick, reactive, ref } from 'vue';

import { message } from 'ant-design-vue';

import {
  addInspectionPlan_Api,
  updateInspectionPlan_Api,
  getInspectionPlan_Api,
} from '#/api/core/inspection-plan';
import { getUsers_Api } from '#/api/core/user';

import DeviceSelectModal from './DeviceSelectModal.vue';
import { useDictStore } from '#/store/dict';

const dictStore = useDictStore();

const emit = defineEmits(['success', 'update:open']);

const open = ref<boolean>(false);
const title = ref<string>('新增巡检计划');
const formRef = ref();
const loading = ref<boolean>(false);
const deviceSelectRef = ref();

// 用户列表
const userOptions = ref<any[]>([]);

// 巡检周期选项
const cycleOptions = dictStore.getDictOptions('InspectionCycleEnum');

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  cycleType: '' as string,
  cycleValue: 1, // 周期值
  cameraIds: [] as number[],
  selectedCameras: [] as any[],
  startDate: '',
  responsibleUserId: undefined,
  responsibleUserName: '',
  status: 'ENABLE',

  // 后端字段
  dayValue: 1, // 日值（用于周和月周期）, 周类型：1=周一, 2=周二, ..., 7=周日 月类型：1-31表示每月几号
  timeSlots: [{ startTime: '', endTime: '' }] as Array<{
    startTime: string;
    endTime: string;
  }>,
  startTime: '', // 开始时间
  endTime: '', // 结束时间
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入计划名称', trigger: 'blur' },
    { max: 128, message: '计划名称不能超过128个字符', trigger: 'blur' },
  ],
  cycleType: [
    { required: true, message: '请选择巡检周期类型', trigger: 'change' },
  ],
  cameraIds: [
    { required: true, message: '请选择摄像头', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一个摄像头', trigger: 'change' },
  ],
  startDate: [{ required: true, message: '请选择启动日期', trigger: 'change' }],
  responsibleUserId: [
    { required: true, message: '请选择巡检负责人', trigger: 'change' },
  ],
  startTime: [{ required: true, message: '请设置执行时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请设置执行时间', trigger: 'change' }],
  cycleValue: [
    { required: true, message: '请输入间隔时间', trigger: 'change' },
    // { type: 'number', message: '请输入数字', trigger: 'change' },
  ],
  dayValue: [
    { required: true, message: '请选择日期', trigger: 'change' },
    // { type: 'number', message: '请输入数字', trigger: 'change' },
  ],
};

// 是否为编辑模式
const isEdit = computed(() => {
  return !!formData.id;
});
const isDetail = ref(false);

// 周几选项
const weekDayOptions = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 7 },
];

// 月份日期选项（1-31日）
const monthDayOptions = Array.from({ length: 31 }, (_, i) => ({
  label: `${i + 1}日`,
  value: i + 1,
}));

// 添加时间段
const addTimeSlot = () => {
  formData.timeSlots.push({ startTime: '', endTime: '' });
};

// 删除时间段
const removeTimeSlot = (index: number) => {
  if (formData.timeSlots.length > 1) {
    formData.timeSlots.splice(index, 1);
  }
};

// 重置表单
const resetForm = () => {
  formData.id = '';
  formData.name = '';
  formData.cycleType = '';
  formData.cycleValue = 1;
  formData.cameraIds = [];
  formData.selectedCameras = [];
  formData.startDate = '';
  formData.responsibleUserId = undefined;
  formData.responsibleUserName = '';
  formData.status = 'ENABLE';

  // 重置后端字段
  formData.dayValue = 1;
  formData.timeSlots = [{ startTime: '', endTime: '' }];
  formData.startTime = '';
  formData.endTime = '';

  nextTick(() => {
    formRef.value?.resetFields();
  });
};

// 关闭弹窗
const closeModal = () => {
  open.value = false;
  isDetail.value = false;
  resetForm();
};

// 获取用户列表
const getUserList = async () => {
  try {
    const res = await getUsers_Api({ page: 1, size: 1000 });
    userOptions.value = res?.data || [];
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
};

// 打开弹窗
const openModal = async (record?: any, detail?: boolean) => {
  resetForm();
  open.value = true;
  await getUserList();

  if (detail) {
    isDetail.value = true;
  }

  if (record) {
    // 编辑模式
    title.value = isDetail.value ? '巡检计划详情' : '编辑巡检计划';
    // 如果是编辑，需要获取详细信息
    try {
      const detail = await getInspectionPlan_Api(record.id);
      nextTick(() => {
        formData.id = detail.id;
        formData.name = detail.name;
        formData.cycleType = detail.cycleType;
        formData.cycleValue = detail.cycleValue || 1;
        formData.cameraIds = detail.cameraIds || [];
        formData.selectedCameras = detail.cameras || [];
        formData.startDate = detail.startDate;
        formData.responsibleUserId = detail.responsibleUserId;
        formData.responsibleUserName = detail.responsibleUserName;
        formData.status = detail.status;

        // 加载后端字段数据
        if (detail.dayValue) formData.dayValue = detail.dayValue;
        if (detail.timeSlots) formData.timeSlots = detail.timeSlots;
        if (detail.startTime) formData.startTime = detail.startTime;
        if (detail.endTime) formData.endTime = detail.endTime;
      });
    } catch (error) {
      console.error('获取巡检计划详情失败:', error);
      message.error('获取巡检计划详情失败');
    }
  } else {
    // 新增模式
    title.value = '新增巡检计划';
  }
};

// 选择设备
const selectDevice = () => {
  if (isEdit.value) {
    deviceSelectRef.value?.openModal(formData.selectedCameras);
  } else {
    deviceSelectRef.value?.openModal(null);
  }
};

// 设备选择确认
const handleDeviceSelect = (cameraIds: number[], cameras: any[]) => {
  formData.cameraIds = cameraIds;
  formData.selectedCameras = cameras;

  // 触发表单验证
  formRef.value?.validateFields(['cameraIds']);
};

// 用户选择处理
const handleUserChange = (userId: number) => {
  const selectedUser = userOptions.value.find((user) => user.id === userId);
  if (selectedUser) {
    formData.responsibleUserName =
      selectedUser.realName || selectedUser.username || selectedUser.name;
  }
};

// 验证动态字段
const validateDynamicFields = () => {
  switch (formData.cycleType) {
    case 'HOUR':
      const validTimeSlots = formData.timeSlots.filter(
        (slot) => slot.startTime && slot.endTime,
      );
      if (validTimeSlots.length === 0) {
        message.error('请至少设置一个有效的时间段');
        return false;
      }
      break;
    case 'DAY':
    case 'WEEK':
    case 'MONTH':
      if (!formData.startTime || !formData.endTime) {
        message.error('请设置执行时间');
        return false;
      }
      break;
  }
  return true;
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();

    // 验证动态字段
    if (!validateDynamicFields()) {
      return;
    }

    loading.value = true;

    const submitData: any = {
      name: formData.name,
      cycleType: formData.cycleType,
      cycleValue: formData.cycleValue,
      cameraIds: formData.cameraIds,
      startDate: formData.startDate,
      responsibleUserId: formData.responsibleUserId,
      dayValue: formData.dayValue,
      timeSlots: formData.timeSlots.filter(
        (slot) => slot.startTime && slot.endTime,
      ),
      startTime: formData.startTime,
      endTime: formData.endTime,
    };

    if (isEdit.value) {
      // 更新巡检计划
      await updateInspectionPlan_Api(formData.id, submitData);
      message.success('更新成功');
    } else {
      // 新增巡检计划
      await addInspectionPlan_Api(submitData);
      message.success('添加成功');
    }

    closeModal();
    emit('success');
  } catch (error) {
    console.error('保存巡检计划失败', error);
  } finally {
    loading.value = false;
  }
};

// 暴露方法给父组件调用
defineExpose({
  openModal,
});
</script>

<template>
  <a-modal
    v-model:open="open"
    :title="title"
    :width="600"
    :maskClosable="false"
    @cancel="closeModal"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      :disabled="isDetail"
    >
      <a-form-item label="计划名称" name="name">
        <a-input
          v-model:value="formData.name"
          placeholder="请输入计划名称"
          :maxlength="128"
          show-count
        />
      </a-form-item>

      <a-form-item label="设备" name="cameraIds">
        <div class="device-select-area">
          <a-button type="primary" @click="selectDevice"> 选择摄像头 </a-button>
          <div
            v-if="formData.selectedCameras.length > 0"
            class="selected-devices"
          >
            已选择 {{ formData.selectedCameras.length }} 个摄像头
          </div>
        </div>
      </a-form-item>

      <a-form-item label="巡检负责人" name="responsibleUserId">
        <a-select
          v-model:value="formData.responsibleUserId"
          placeholder="请选择巡检负责人"
          show-search
          :filter-option="false"
          @change="handleUserChange"
        >
          <a-select-option
            v-for="user in userOptions"
            :key="user.id"
            :value="user.id"
          >
            {{ user.realName || user.username || user.name }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="巡检周期" name="cycleType">
        <a-radio-group v-model:value="formData.cycleType">
          <a-radio-button
            v-for="option in cycleOptions"
            :key="option.dictValue"
            :value="option.dictValue"
          >
            {{ option.dictLabel }}
          </a-radio-button>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="启动日期" name="startDate">
        <a-date-picker
          v-model:value="formData.startDate"
          style="width: 100%"
          placeholder="请选择启动日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </a-form-item>

      <!-- 动态字段：根据巡检周期类型显示不同的配置 -->

      <!-- 小时周期：频次设置 -->
      <template v-if="formData.cycleType === 'HOUR'">
        <a-form-item label="频次设置" required>
          <div class="time-slots-container">
            <div
              v-for="(slot, index) in formData.timeSlots"
              :key="index"
              class="time-slot-item"
            >
              <a-time-picker
                v-model:value="slot.startTime"
                placeholder="开始时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 120px"
              />
              <span class="time-separator">至</span>
              <a-time-picker
                v-model:value="slot.endTime"
                placeholder="结束时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 120px"
              />
              <a-button
                v-if="formData.timeSlots.length > 1"
                type="text"
                danger
                @click="removeTimeSlot(index)"
              >
                删除
              </a-button>
            </div>
            <a-button type="dashed" @click="addTimeSlot" style="width: 100%">
              + 添加时间段
            </a-button>
          </div>
        </a-form-item>
      </template>

      <!-- 日周期：间隔时间和时间范围 -->
      <template v-if="formData.cycleType === 'DAY'">
        <a-form-item label="间隔时间" name="cycleValue">
          <a-input-number
            v-model:value="formData.cycleValue"
            :min="1"
            placeholder="间隔天数"
            style="width: 120px"
          />
          <span style="margin-left: 8px">天/次</span>
        </a-form-item>
        <a-form-item label="执行时间" required>
          <div class="time-range-container">
            <a-time-picker
              v-model:value="formData.startTime"
              placeholder="开始时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 120px"
            />
            <span class="time-separator">至</span>
            <a-time-picker
              v-model:value="formData.endTime"
              placeholder="结束时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 120px"
            />
          </div>
        </a-form-item>
      </template>

      <!-- 周周期：间隔周数、任务时间（周几）、时间范围 -->
      <template v-if="formData.cycleType === 'WEEK'">
        <a-form-item label="间隔时间" name="cycleValue">
          <a-input-number
            v-model:value="formData.cycleValue"
            :min="1"
            placeholder="间隔周数"
            style="width: 120px"
          />
          <span style="margin-left: 8px">周/次</span>
        </a-form-item>
        <a-form-item label="任务时间" name="dayValue">
          <a-select
            v-model:value="formData.dayValue"
            placeholder="选择周几"
            style="width: 120px"
          >
            <a-select-option
              v-for="option in weekDayOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="执行时间" required name="startTime">
          <div class="time-range-container">
            <a-time-picker
              v-model:value="formData.startTime"
              placeholder="开始时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 120px"
            />
            <span class="time-separator">至</span>
            <a-time-picker
              v-model:value="formData.endTime"
              placeholder="结束时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 120px"
            />
          </div>
        </a-form-item>
      </template>

      <!-- 月周期：间隔月数、任务时间（几号）、时间范围 -->
      <template v-if="formData.cycleType === 'MONTH'">
        <a-form-item label="间隔时间" name="cycleValue">
          <a-input-number
            v-model:value="formData.cycleValue"
            :min="1"
            placeholder="间隔月数"
            style="width: 120px"
          />
          <span style="margin-left: 8px">月/次</span>
        </a-form-item>
        <a-form-item label="任务时间" name="dayValue">
          <a-select
            v-model:value="formData.dayValue"
            placeholder="选择日期"
            style="width: 120px"
          >
            <a-select-option
              v-for="option in monthDayOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="执行时间" required name="startTime">
          <div class="time-range-container">
            <a-time-picker
              v-model:value="formData.startTime"
              placeholder="开始时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 120px"
            />
            <span class="time-separator">至</span>
            <a-time-picker
              v-model:value="formData.endTime"
              placeholder="结束时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 120px"
            />
          </div>
        </a-form-item>
      </template>
    </a-form>

    <!-- 设备选择弹窗 -->
    <DeviceSelectModal ref="deviceSelectRef" @confirm="handleDeviceSelect" />

    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button
        v-if="!isDetail"
        type="primary"
        :loading="loading"
        @click="handleSubmit"
      >
        确定
      </a-button>
    </template>
  </a-modal>
</template>

<style lang="scss" scoped>
.device-select-area {
  display: flex;
  align-items: center;
  gap: 12px;

  .selected-devices {
    color: #1890ff;
    font-size: 14px;
  }
}

.time-slots-container {
  .time-slot-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;

    .time-separator {
      color: #666;
      font-size: 14px;
    }
  }
}

.time-range-container {
  display: flex;
  align-items: center;
  gap: 8px;

  .time-separator {
    color: #666;
    font-size: 14px;
  }
}
</style>
