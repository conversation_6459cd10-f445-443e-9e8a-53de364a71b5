import { requestClient } from '#/api/request';

enum Api {
  home = '/api/line/home',
}

// 获取设备数量统计
export const getDeviceStatic_Api = () => {
  return requestClient.get(`${Api.home}/device-stats`);
};

// 获取业务报警数量统计
export const getAlertStatic_Api = () => {
  return requestClient.get(`${Api.home}/biz-alert-stats`);
};

// 物联报警数量统计
export const getIotStatic_Api = () => {
  return requestClient.get(`${Api.home}/iot-alert-stats`);
};

// 业务报警按年份统计
export const getAlertStaticByYear_Api = (year: any) => {
  return requestClient.get(
    `${Api.home}/biz-alert-month-type-stats?year=${year}`,
  );
};

// 获取场景报警统计
export const getSceneStaticByYear_Api = (year: any) => {
  return requestClient.get(`${Api.home}/biz-alert-scene-stats?year=${year}`);
};

// 获取漏检率趋势
export const getMissedDetectionTrend_Api = (year: any) => {
  return requestClient.get(`${Api.home}/miss-inspection-stats?year=${year}`);
};
