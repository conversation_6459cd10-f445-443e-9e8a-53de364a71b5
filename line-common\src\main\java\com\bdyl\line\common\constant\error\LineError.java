package com.bdyl.line.common.constant.error;

import lombok.AllArgsConstructor;
import lombok.Getter;

import com.bdyl.boot.exception.Error;

/**
 * 业务error
 */
@Getter
@AllArgsConstructor
public enum LineError implements Error {

    /**
     * uaa服务创建用户失败
     */
    USER_AUTH_INSERT_FAILED("UAA_001", "创建用户失败"),

    /**
     * UAA用户删除失败
     */
    USER_AUTH_DELETE_FAILED("UAA_002", "删除用户失败"),
    /**
     * UAA用户更新失败
     */
    USER_AUTH_UPDATE_FAILED("UAA_003", "更新用户失败"),
    /**
     * 您无权创建平台级用户
     */
    USER_AUTH_CREATE_PLATFORM_USER_FAILED("UAA_004", "您无权创建平台级用户"),

    // ===========================分界线============================= //

    /**
     * 该行政区划不存在
     */
    REGION_NOT_EXISTED("REGION_001", "该行政区划不存在"),

    /**
     * 该行政区划级别不匹配
     */
    REGION_LEVEL_NOT_MATCHED("REGION_002", "所选组织类型(市级、省级)与行政区划级别不匹配"),

    // ===========================分界线============================= //

    /**
     * 该区划下已存在组织
     */
    ORGAN_EXISTED("ORGAN_001", "该区划下已存在组织"),
    /**
     * 组织不存在
     */
    ORGAN_NOT_EXISTED("ORGAN_002", "组织不存在"),
    /**
     * 父组织不存在
     */
    PARENT_ORGAN_NOT_EXISTED("ORGAN_003", "父组织不存在"),
    /**
     * 该组织管理员不存在
     */
    ORGAN_ADMIN_NOT_EXISTED("ORGAN_004", "该组织管理员不存在"),
    /**
     * 修改组织管理员信息失败
     */
    ORGAN_ADMIN_UPDATE_FAILED("ORGAN_005", "修改组织管理员信息失败"),

    /**
     * 只能创建下级区划的组织
     */
    ORGAN_CREATE_ONLY_SUB_REGION("ORGAN_006", "只能创建下级区划的组织"),

    /**
     * 组织下有用户，无法删除组织
     */
    ORGAN_DELETE_HAS_USER("ORGAN_007", "组织下有用户，无法删除组织"),
    /**
     * 省级组织只能创建市级组织
     */
    ORGAN_CREATE_ONLY_CITY("ORGAN_008", "省级组织只能创建市级组织"),
    /**
     * 市级组织不能创建下级组织
     */
    ORGAN_CANT_CREATE_SUB_ORGAN("ORGAN_009", "市级组织不能创建下级组织"),

    // ===========================分界线============================= //

    /**
     * 该用户不存在
     */
    USER_NOT_EXISTED("USER_001", "该用户不存在"),

    /**
     * 用户新增失败
     */
    USER_INSERT_FAILED("USER_002", "用户新增失败"),

    /**
     * 账户名不能和手机号相同
     */
    USER_ACCOUNT_PHONE_SAME("USER_003", "账户名不能和手机号相同"),

    // ===========================分界线============================= //

    /**
     * 部门不存在
     */
    DEPARTMENT_NOT_EXISTED("DEPARTMENT_001", "部门不存在"),

    // ===========================分界线============================= //

    /**
     * 终端不存在
     */
    TERMINAL_NOT_EXISTED("TERMINAL_001", "终端不存在"),

    /**
     * 终端编码已存在
     */
    TERMINAL_CODE_EXISTED("TERMINAL_002", "终端编码已存在"),
    /**
     * 该终端没有绑定摄像头
     */
    TERMINAL_NO_CAMERA("TERMINAL_003", "该终端没有绑定摄像头"),

    // ===========================分界线============================= //

    /**
     * 场景不存在
     */
    SCENE_NOT_EXISTED("SCENE_001", "场景不存在"),

    /**
     * 场景名称重复
     */
    SCENE_NAME_EXISTED("SCENE_002", "场景名称重复"),

    // ===========================分界线============================= //

    /**
     * 摄像头不存在
     */
    CAMERA_NOT_EXISTED("CAMERA_001", "摄像头不存在"),
    /**
     * 没有指定摄像头通道无法播放视频
     */
    CAMERA_NO_CHANNEL("CAMERA_002", "没有指定摄像头通道无法播放视频"),
    /**
     * 摄像头编码重复
     */
    CAMERA_CODE_EXISTED("CAMERA_003", "摄像头编码重复"),

    /**
     * 摄像头还未进行录像,无法回放
     */
    CAMERA_NOT_RECORDING("CAMERA_004", "摄像头还未进行录像,无法回放"),
    /**
     * 获取视频流失败
     */
    CAMERA_GET_STREAM_FAILED("CAMERA_005", "获取视频流失败"),

    // ===========================分界线============================= //
    /**
     * 巡检计划名称已存在
     */
    INSPECTION_PLAN_NAME_EXISTED("INSPECTION_PLAN_001", "巡检计划名称已存在"),
    /**
     * 巡检负责人不存在
     */
    USER_NOT_EXIST("INSPECTION_PLAN_002", "巡检负责人不存在"),
    /**
     * 巡检摄像头不存在
     */
    CAMERA_NOT_EXIST("INSPECTION_PLAN_003", "巡检摄像头不存在"),
    /**
     * 巡检计划不存在
     */
    INSPECTION_PLAN_NOT_EXIST("INSPECTION_PLAN_004", "巡检计划不存在"),

    // ===========================分界线============================= //
    /**
     * 巡检任务不存在
     */
    INSPECTION_TASK_NOT_EXIST("INSPECTION_TASK_001", "巡检任务不存在"),
    /**
     * 任务摄像头关联不存在
     */
    INSPECTION_TASK_CAMERA_NOT_EXIST("INSPECTION_TASK_002", "任务摄像头关联不存在"),

    // ===========================分界线============================= //
    /**
     * 摄像头模型配置不存在
     */
    CAMERA_MODEL_CONFIG_NOT_EXIST("CAMERA_MODEL_CONFIG_001", "摄像头模型配置不存在");

    /**
     * 错误码
     */
    private final String code;

    /**
     * 错误信息
     */
    private final String message;
}
