{"name": "@vben/node-utils", "version": "5.5.6", "private": true, "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "internal/node-utils"}, "license": "MIT", "type": "module", "scripts": {"stub": "pnpm unbuild --stub"}, "files": ["dist"], "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./src/index.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs"}}, "dependencies": {"@changesets/git": "catalog:", "@manypkg/get-packages": "catalog:", "chalk": "catalog:", "consola": "catalog:", "dayjs": "catalog:", "execa": "catalog:", "find-up": "catalog:", "ora": "catalog:", "pkg-types": "catalog:", "prettier": "catalog:", "rimraf": "catalog:"}}