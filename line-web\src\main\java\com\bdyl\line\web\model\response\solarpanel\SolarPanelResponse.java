package com.bdyl.line.web.model.response.solarpanel;

import lombok.Data;

/**
 * 太阳能电池板响应对象。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class SolarPanelResponse {
    /**
     * 太阳能电池板ID
     */
    private Long id;
    /**
     * 终端设备ID
     */
    private Long terminalId;
    /**
     * 电池温度(℃)
     */
    private Double batteryTemperature;
    /**
     * 电池电压(V)
     */
    private Double batteryVoltage;
    /**
     * 电池电流(A)
     */
    private Double batteryCurrent;
    /**
     * 电池电量百分比(%)
     */
    private Double batteryLevel;
    /**
     * 直流负载电压(V)
     */
    private Double dcLoadVoltage;
    /**
     * 直流负载电流(A)
     */
    private Double dcLoadCurrent;
    /**
     * 直流负载功率(W)
     */
    private Double dcLoadPower;
    /**
     * 直流负载今日能耗(kWh)
     */
    private Double dcLoadEnergyToday;
    /**
     * 光伏电压(V)
     */
    private Double pvVoltage;
    /**
     * 光伏电流(A)
     */
    private Double pvCurrent;
    /**
     * 光伏功率(W)
     */
    private Double pvPower;
}
