<script setup lang="ts">
import { useAttrs } from 'vue';

import { Tag } from 'ant-design-vue';

import { useDictStore } from '#/store/dict';

interface Prop {
  dictName: string;
  dictValue: any;
}

const props = withDefaults(defineProps<Prop>(), {});

const { getDictLable } = useDictStore();

const attrs = useAttrs();
</script>

<template>
  <Tag v-bind="attrs">{{ getDictLable(dictName, dictValue) }}</Tag>
</template>

<style lang="scss" scoped></style>
