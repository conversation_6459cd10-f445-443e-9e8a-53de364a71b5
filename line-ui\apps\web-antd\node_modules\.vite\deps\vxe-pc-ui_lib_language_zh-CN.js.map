{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/lib/language/zh-CN.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _default = exports.default = {\n  vxe: {\n    base: {\n      pleaseInput: '请输入',\n      pleaseSelect: '请选择',\n      comma: '，',\n      fullStop: '。'\n    },\n    loading: {\n      text: '加载中...'\n    },\n    error: {\n      downErr: '下载失败',\n      errLargeData: '当绑定的数据量过大时，应该请使用 {0}，否则可能会出现卡顿',\n      groupFixed: '如果使用分组表头，冻结列必须按组设置',\n      groupMouseRange: '分组表头与 \"{0}\" 不能同时使用，这可能会出现错误',\n      groupTag: '分组列头应该使用 \"{0}\" 而不是 \"{1}\"，这可能会出现错误',\n      scrollErrProp: '启用虚拟滚动后不支持该参数 \"{0}\"',\n      errConflicts: '参数 \"{0}\" 与 \"{1}\" 有冲突',\n      notSupportProp: '当启用参数 \"{0}\" 时不支持 \"{1}\"，应该为 \"{2}\"，否则将会出现错误',\n      notConflictProp: '当使用 \"{0}\" 时，应该设置 \"{1}\"，否则可能会存在功能冲突',\n      unableInsert: '无法插入到指定位置，请检查参数是否正确',\n      useErr: '安装 \"{0}\" 模块时发生错误，可能顺序不正确，依赖的模块需要在 Table 之前安装',\n      barUnableLink: '工具栏无法关联表格',\n      expandContent: '展开行的插槽应该是 \"content\"，请检查是否正确',\n      reqComp: '缺少 \"{0}\" 组件，请检查是否正确安装。 https://vxeui.com/#/start/useGlobal',\n      reqModule: '缺少 \"{0}\" 模块',\n      reqProp: '缺少必要的 \"{0}\" 参数，这可能会导致出现错误',\n      emptyProp: '参数 \"{0}\" 不允许为空',\n      errProp: '不支持的参数 \"{0}\"，可能为 \"{1}\"',\n      colRepet: 'column.{0}=\"{1}\" 重复了，这可能会导致某些功能无法使用',\n      notFunc: '方法 \"{0}\" 不存在',\n      errFunc: '参数 \"{0}\" 不是一个方法',\n      notValidators: '全局校验 \"{0}\" 不存在',\n      notFormats: '全局格式化 \"{0}\" 不存在',\n      notCommands: '全局指令 \"{0}\" 不存在',\n      notSlot: '插槽 \"{0}\" 不存在',\n      noTree: '树结构不支持 \"{0}\"',\n      notProp: '不支持的参数 \"{0}\"',\n      checkProp: '当数据量过大时可能会导致复选框卡顿，建议设置参数 \"{0}\" 提升渲染速度',\n      coverProp: '\"{0}\" 的参数 \"{1}\" 重复定义，这可能会出现错误',\n      uniField: '字段名 \"{0}\" 重复定义，这可能会出现错误',\n      repeatKey: '主键重复 {0}=\"{1}\"，这可能会出现错误',\n      delFunc: '方法 \"{0}\" 已废弃，请使用 \"{1}\"',\n      delProp: '参数 \"{0}\" 已废弃，请使用 \"{1}\"',\n      delEvent: '事件 \"{0}\" 已废弃，请使用 \"{1}\"',\n      removeProp: '参数 \"{0}\" 已废弃，不建议使用，这可能会导致出现错误',\n      errFormat: '全局的格式化内容应该使用 \"VXETable.formats\" 定义，挂载 \"formatter={0}\" 的方式已不建议使用',\n      notType: '不支持的文件类型 \"{0}\"',\n      notExp: '该浏览器不支持导入/导出功能',\n      impFields: '导入失败，请检查字段名和数据格式是否正确',\n      treeNotImp: '树表格不支持导入',\n      treeCrossDrag: '只能拖拽第一层级',\n      treeDragChild: '父级不能拖拽到自己的子级中',\n      reqPlugin: '扩展插件未安装 \"{1}\" https://vxeui.com/other{0}/#/{1}/install',\n      errMaxRow: '超过支持的最大数据量 {0} 行，这可能会导致出现错误'\n    },\n    table: {\n      emptyText: '暂无数据',\n      allTitle: '全选/取消',\n      seqTitle: '序号',\n      actionTitle: '操作',\n      confirmFilter: '筛选',\n      resetFilter: '重置',\n      allFilter: '全部',\n      sortAsc: '升序：最低到最高',\n      sortDesc: '降序：最高到最低',\n      filter: '对所选的列启用筛选',\n      impSuccess: '成功导入 {0} 条记录',\n      expLoading: '正在导出中',\n      expSuccess: '导出成功',\n      expError: '导出失败',\n      expFilename: '导出_{0}',\n      expOriginFilename: '导出_源_{0}',\n      customTitle: '列设置',\n      customAll: '全部',\n      customConfirm: '确认',\n      customClose: '关闭',\n      customCancel: '取消',\n      customRestore: '恢复默认',\n      maxFixedCol: '最大冻结列的数量不能超过 {0} 个',\n      dragTip: '移动：{0}',\n      resizeColTip: '宽：{0} 像素',\n      resizeRowTip: '高：{0} 像素',\n      rowGroupContentTotal: '{0}（{1}）'\n    },\n    grid: {\n      selectOneRecord: '请至少选择一条记录！',\n      deleteSelectRecord: '您确定要删除所选记录吗？',\n      removeSelectRecord: '您确定要移除所选记录吗？',\n      dataUnchanged: '数据未改动！',\n      delSuccess: '成功删除所选记录！',\n      saveSuccess: '保存成功！',\n      operError: '发生错误，操作失败！'\n    },\n    select: {\n      search: '搜索',\n      loadingText: '加载中',\n      emptyText: '暂无数据'\n    },\n    pager: {\n      goto: '前往',\n      gotoTitle: '页数',\n      pagesize: '{0}条/页',\n      total: '共 {0} 条记录',\n      pageClassifier: '页',\n      homePage: '首页',\n      homePageTitle: '首页',\n      prevPage: '上一页',\n      prevPageTitle: '上一页',\n      nextPage: '下一页',\n      nextPageTitle: '下一页',\n      prevJump: '向上跳页',\n      prevJumpTitle: '向上跳页',\n      nextJump: '向下跳页',\n      nextJumpTitle: '向下跳页',\n      endPage: '末页',\n      endPageTitle: '末页'\n    },\n    alert: {\n      title: '系统提示'\n    },\n    button: {\n      confirm: '确认',\n      cancel: '取消',\n      clear: '清除'\n    },\n    filter: {\n      search: '搜索'\n    },\n    custom: {\n      cstmTitle: '列设置',\n      cstmRestore: '恢复默认',\n      cstmCancel: '取消',\n      cstmConfirm: '确定',\n      cstmConfirmRestore: '请确认是否恢复成默认列配置？',\n      cstmDragTarget: '移动：{0}',\n      setting: {\n        colSort: '排序',\n        sortHelpTip: '点击并拖动图标可以调整列的排序',\n        colTitle: '列标题',\n        colResizable: '列宽（像素）',\n        colVisible: '是否显示',\n        colFixed: '冻结列',\n        colFixedMax: '冻结列（最多 {0} 列）',\n        fixedLeft: '左侧',\n        fixedUnset: '不设置',\n        fixedRight: '右侧'\n      }\n    },\n    import: {\n      modes: {\n        covering: '覆盖方式（直接覆盖表格数据）',\n        insert: '底部追加（在表格的底部追加新数据）',\n        insertTop: '顶部追加（在表格的顶部追加新数据）',\n        insertBottom: '底部追加（在表格的底部追加新数据）'\n      },\n      impTitle: '导入数据',\n      impFile: '文件名',\n      impSelect: '选择文件',\n      impType: '文件类型',\n      impOpts: '参数设置',\n      impMode: '导入模式',\n      impConfirm: '导入',\n      impCancel: '取消'\n    },\n    export: {\n      types: {\n        csv: 'CSV (逗号分隔)(*.csv)',\n        html: '网页(*.html)',\n        xml: 'XML 数据(*.xml)',\n        txt: '文本文件(制表符分隔)(*.txt)',\n        xls: 'Excel 97-2003 工作簿(*.xls)',\n        xlsx: 'Excel 工作簿(*.xlsx)',\n        pdf: 'PDF (*.pdf)'\n      },\n      modes: {\n        empty: '空数据',\n        current: '当前数据（当前页的数据）',\n        selected: '选中数据（当前页选中的数据）',\n        all: '全量数据（包括所有分页的数据）'\n      },\n      printTitle: '打印数据',\n      expTitle: '导出数据',\n      expName: '文件名',\n      expNamePlaceholder: '请输入文件名',\n      expSheetName: '标题',\n      expSheetNamePlaceholder: '请输入标题',\n      expType: '保存类型',\n      expMode: '选择数据',\n      expCurrentColumn: '全部字段',\n      expColumn: '选择字段',\n      expOpts: '参数设置',\n      expOptHeader: '表头',\n      expHeaderTitle: '是否需要表头',\n      expOptFooter: '表尾',\n      expFooterTitle: '是否需要表尾',\n      expOptColgroup: '分组表头',\n      expOptTitle: '列标题',\n      expTitleTitle: '是否为列标题，否则显示为列的字段名',\n      expColgroupTitle: '如果存在，则支持带有分组结构的表头',\n      expOptMerge: '合并',\n      expMergeTitle: '如果存在，则支持带有合并结构的单元格',\n      expOptAllExpand: '展开树',\n      expAllExpandTitle: '如果存在，则支持将带有层级结构的数据全部展开',\n      expOptUseStyle: '样式',\n      expUseStyleTitle: '如果存在，则支持带样式的单元格',\n      expOptOriginal: '源数据',\n      expOriginalTitle: '如果为源数据，则支持导入到表格中',\n      expPrint: '打印',\n      expConfirm: '导出',\n      expCancel: '取消'\n    },\n    modal: {\n      errTitle: '错误提示',\n      zoomMin: '最小化',\n      zoomIn: '最大化',\n      zoomOut: '还原',\n      close: '关闭',\n      miniMaxSize: '最小化窗口的数量不能超过 {0} 个',\n      footPropErr: 'show-footer 仅用于启用表尾，需配合 show-confirm-button | show-cancel-button | 插槽使用'\n    },\n    drawer: {\n      close: '关闭'\n    },\n    form: {\n      folding: '收起',\n      unfolding: '展开'\n    },\n    toolbar: {\n      import: '导入',\n      export: '导出',\n      print: '打印',\n      refresh: '刷新',\n      zoomIn: '全屏',\n      zoomOut: '还原',\n      custom: '列设置',\n      customAll: '全部',\n      customConfirm: '确认',\n      customRestore: '重置',\n      fixedLeft: '冻结在左侧',\n      fixedRight: '冻结在右侧',\n      cancelFixed: '取消冻结列'\n    },\n    datePicker: {\n      yearTitle: '{0} 年'\n    },\n    input: {\n      date: {\n        m1: '01 月',\n        m2: '02 月',\n        m3: '03 月',\n        m4: '04 月',\n        m5: '05 月',\n        m6: '06 月',\n        m7: '07 月',\n        m8: '08 月',\n        m9: '09 月',\n        m10: '10 月',\n        m11: '11 月',\n        m12: '12 月',\n        quarterLabel: '{0} 年',\n        monthLabel: '{0} 年',\n        dayLabel: '{0} 年 {1}',\n        labelFormat: {\n          date: 'yyyy-MM-dd',\n          time: 'HH:mm:ss',\n          datetime: 'yyyy-MM-dd HH:mm:ss',\n          week: 'yyyy 年第 WW 周',\n          month: 'yyyy-MM',\n          quarter: 'yyyy 年第 q 季度',\n          year: 'yyyy'\n        },\n        weeks: {\n          w: '周',\n          w0: '周日',\n          w1: '周一',\n          w2: '周二',\n          w3: '周三',\n          w4: '周四',\n          w5: '周五',\n          w6: '周六'\n        },\n        months: {\n          m0: '一月',\n          m1: '二月',\n          m2: '三月',\n          m3: '四月',\n          m4: '五月',\n          m5: '六月',\n          m6: '七月',\n          m7: '八月',\n          m8: '九月',\n          m9: '十月',\n          m10: '十一月',\n          m11: '十二月'\n        },\n        quarters: {\n          q1: '第一季度',\n          q2: '第二季度',\n          q3: '第三季度',\n          q4: '第四季度'\n        }\n      }\n    },\n    numberInput: {\n      currencySymbol: '¥'\n    },\n    imagePreview: {\n      popupTitle: '预览',\n      operBtn: {\n        zoomOut: '缩小',\n        zoomIn: '放大',\n        pctFull: '等比例缩放',\n        pct11: '显示原始尺寸',\n        rotateLeft: '向左旋转',\n        rotateRight: '向右旋转',\n        print: '点击打印图片',\n        download: '点击下载图片'\n      }\n    },\n    upload: {\n      fileBtnText: '点击或拖拽上传',\n      imgBtnText: '点击或拖拽上传',\n      dragPlaceholder: '请把文件拖放到这个区域即可上传',\n      imgSizeHint: '单张{0}',\n      imgCountHint: '最多{0}张',\n      fileTypeHint: '支持 {0} 文件类型',\n      fileSizeHint: '单个文件大小不超过{0}',\n      fileCountHint: '最多可上传{0}个文件',\n      uploadTypeErr: '文件类型不匹配！',\n      overCountErr: '最多只能选择{0}个文件！',\n      overCountExtraErr: '已超出最大数量{0}个，超出的{1}个文件将被忽略！',\n      overSizeErr: '文件大小最大不能超过{0}！',\n      reUpload: '重新上传',\n      uploadProgress: '上传中 {0}%',\n      uploadErr: '上传失败',\n      uploadSuccess: '上传成功',\n      moreBtnText: '更多（{0}）',\n      viewItemTitle: '点击查看',\n      morePopup: {\n        readTitle: '查看列表',\n        imageTitle: '上传图片',\n        fileTitle: '上传文件'\n      }\n    },\n    empty: {\n      defText: '暂无数据'\n    },\n    colorPicker: {\n      clear: '清除',\n      confirm: '确认',\n      copySuccess: '已复制到剪贴板：{0}'\n    },\n    formDesign: {\n      formName: '表单名称',\n      defFormTitle: '未命名的表单',\n      widgetPropTab: '控件属性',\n      widgetFormTab: '表单属性',\n      error: {\n        wdFormUni: '该类型的控件在表单中只允许添加一个',\n        wdSubUni: '该类型的控件在子表中只允许添加一个'\n      },\n      styleSetting: {\n        btn: '样式设置',\n        title: '表单的样式设置',\n        layoutTitle: '控件布局',\n        verticalLayout: '上下布局',\n        horizontalLayout: '横向布局',\n        styleTitle: '标题样式',\n        boldTitle: '标题加粗',\n        fontBold: '加粗',\n        fontNormal: '常规',\n        colonTitle: '显示冒号',\n        colonVisible: '显示',\n        colonHidden: '隐藏',\n        alignTitle: '对齐方式',\n        widthTitle: '标题宽度',\n        alignLeft: '居左',\n        alignRight: '居右',\n        unitPx: '像素',\n        unitPct: '百分比'\n      },\n      widget: {\n        group: {\n          base: '基础控件',\n          layout: '布局控件',\n          system: '系统控件',\n          module: '模块控件',\n          chart: '图表控件',\n          advanced: '高级控件'\n        },\n        copyTitle: '副本_{0}',\n        component: {\n          input: '输入框',\n          textarea: '文本域',\n          select: '下拉选择',\n          row: '一行多列',\n          title: '标题',\n          text: '文本',\n          subtable: '子表',\n          VxeSwitch: '是/否',\n          VxeInput: '输入框',\n          VxeNumberInput: '数字',\n          VxeDatePicker: '日期',\n          VxeTextarea: '文本域',\n          VxeSelect: '下拉选择',\n          VxeTreeSelect: '树形选择',\n          VxeRadioGroup: '单选框',\n          VxeCheckboxGroup: '复选框',\n          VxeUploadFile: '文件',\n          VxeUploadImage: '图片',\n          VxeRate: '评分',\n          VxeSlider: '滑块'\n        }\n      },\n      widgetProp: {\n        name: '控件名称',\n        placeholder: '提示语',\n        required: '必填校验',\n        multiple: '允许多选',\n        displaySetting: {\n          name: '显示设置',\n          pc: '电脑端',\n          mobile: '手机端',\n          visible: '显示',\n          hidden: '隐藏'\n        },\n        dataSource: {\n          name: '数据源',\n          defValue: '选项{0}',\n          addOption: '添加选项',\n          batchEditOption: '批量编辑',\n          batchEditTip: '每行对应一个选项，支持从表格、Excel、WPS 中直接复制粘贴。',\n          batchEditSubTip: '每行对应一个选项，如果是分组，子项可以是空格或制表键开头，支持从表格、Excel、WPS 中直接复制粘贴。',\n          buildOption: '生成选项'\n        },\n        rowProp: {\n          colSize: '列数',\n          col2: '两列',\n          col3: '三列',\n          col4: '四列',\n          col6: '六列',\n          layout: '布局'\n        },\n        textProp: {\n          name: '内容',\n          alignTitle: '对齐方式',\n          alignLeft: '居左',\n          alignCenter: '居中',\n          alignRight: '居右',\n          colorTitle: '字体颜色',\n          sizeTitle: '字体大小',\n          boldTitle: '字体加粗',\n          fontNormal: '常规',\n          fontBold: '加粗'\n        },\n        subtableProp: {\n          seqTitle: '序号',\n          showSeq: '显示序号',\n          showCheckbox: '允许多选',\n          errSubDrag: '子表不支持该控件，请使用其他控件',\n          colPlace: '将控件拖拽进来'\n        },\n        uploadProp: {\n          limitFileCount: '文件数量限制',\n          limitFileSize: '文件大小限制',\n          multiFile: '允许上传多个文件',\n          limitImgCount: '图片数量限制',\n          limitImgSize: '图片大小限制',\n          multiImg: '允许上传多张图片'\n        }\n      }\n    },\n    listDesign: {\n      fieldSettingTab: '字段设置',\n      listSettingTab: '参数设置',\n      searchTitle: '查询条件',\n      listTitle: '列表字段',\n      searchField: '查询字段',\n      listField: '列表字段',\n      activeBtn: {\n        ActionButtonUpdate: '编辑',\n        ActionButtonDelete: '删除'\n      },\n      search: {\n        addBtn: '编辑',\n        emptyText: '未配置查询条件',\n        editPopupTitle: '编辑查询字段'\n      },\n      searchPopup: {\n        colTitle: '标题',\n        saveBtn: '保存'\n      }\n    },\n    text: {\n      copySuccess: '已复制到剪贴板',\n      copyError: '当前环境不支持该操作'\n    },\n    countdown: {\n      formats: {\n        yyyy: '年',\n        MM: '月',\n        dd: '天',\n        HH: '时',\n        mm: '分',\n        ss: '秒'\n      }\n    },\n    plugins: {\n      extendCellArea: {\n        area: {\n          mergeErr: '无法对合并单元格进行该操作',\n          multiErr: '无法对多重选择区域进行该操作',\n          selectErr: '无法操作指定区域的单元格',\n          extendErr: '如果延伸的区域包含被合并的单元格，所有合并的单元格需大小相同',\n          pasteMultiErr: '无法粘贴，需要相同大小的复制的区域和粘贴的区域才能执行此操作',\n          cpInvalidErr: '该操作无法进行，您选择的区域中存在被禁止的列（{0}）'\n        },\n        fnr: {\n          title: '查找和替换',\n          findLabel: '查找',\n          replaceLabel: '替换',\n          findTitle: '查找内容：',\n          replaceTitle: '替换为：',\n          tabs: {\n            find: '查找',\n            replace: '替换'\n          },\n          filter: {\n            re: '正则表达式',\n            whole: '全词匹配',\n            sensitive: '区分大小写'\n          },\n          btns: {\n            findNext: '查找下一个',\n            findAll: '查找全部',\n            replace: '替换',\n            replaceAll: '替换全部',\n            cancel: '取消'\n          },\n          header: {\n            seq: '#',\n            cell: '单元格',\n            value: '值'\n          },\n          body: {\n            row: '行：{0}',\n            col: '列：{0}'\n          },\n          empty: '(空值)',\n          reError: '无效的正则表达式',\n          recordCount: '已找到 {0} 个单元格',\n          notCell: '找不到匹配的单元格',\n          replaceSuccess: '成功替换 {0} 个单元格'\n        }\n      },\n      filterComplexInput: {\n        menus: {\n          fixedColumn: '冻结列',\n          fixedGroup: '冻结分组',\n          cancelFixed: '取消冻结',\n          fixedLeft: '冻结左侧',\n          fixedRight: '冻结右侧'\n        },\n        cases: {\n          equal: '等于',\n          gt: '大于',\n          lt: '小于',\n          begin: '开头是',\n          endin: '结尾是',\n          include: '包含',\n          isSensitive: '区分大小写'\n        }\n      },\n      filterCombination: {\n        menus: {\n          sort: '排序',\n          clearSort: '清除排序',\n          sortAsc: '升序',\n          sortDesc: '降序',\n          fixedColumn: '冻结列',\n          fixedGroup: '冻结分组',\n          cancelFixed: '取消冻结',\n          fixedLeft: '冻结左侧',\n          fixedRight: '冻结右侧',\n          clearFilter: '清除筛选',\n          textOption: '文本筛选',\n          numberOption: '数值筛选'\n        },\n        popup: {\n          title: '自定义筛选的方式',\n          currColumnTitle: '当前列：',\n          and: '与',\n          or: '或',\n          describeHtml: '可用 ? 代表单个字符<br/>用 * 代表任意多个字符'\n        },\n        cases: {\n          equal: '等于',\n          unequal: '不等于',\n          gt: '大于',\n          ge: '大于或等于',\n          lt: '小于',\n          le: '小于或等于',\n          begin: '开头是',\n          notbegin: '开头不是',\n          endin: '结尾是',\n          notendin: '结尾不是',\n          include: '包含',\n          exclude: '不包含',\n          between: '介于',\n          custom: '自定义筛选',\n          insensitive: '不区分大小写',\n          isSensitive: '区分大小写'\n        },\n        empty: '(空白)',\n        notData: '无匹配项'\n      }\n    },\n    pro: {\n      area: {\n        mergeErr: '无法对合并单元格进行该操作',\n        multiErr: '无法对多重选择区域进行该操作',\n        extendErr: '如果延伸的区域包含被合并的单元格，所有合并的单元格需大小相同',\n        pasteMultiErr: '无法粘贴，需要相同大小的复制的区域和粘贴的区域才能执行此操作'\n      },\n      fnr: {\n        title: '查找和替换',\n        findLabel: '查找',\n        replaceLabel: '替换',\n        findTitle: '查找内容：',\n        replaceTitle: '替换为：',\n        tabs: {\n          find: '查找',\n          replace: '替换'\n        },\n        filter: {\n          re: '正则表达式',\n          whole: '全词匹配',\n          sensitive: '区分大小写'\n        },\n        btns: {\n          findNext: '查找下一个',\n          findAll: '查找全部',\n          replace: '替换',\n          replaceAll: '替换全部',\n          cancel: '取消'\n        },\n        header: {\n          seq: '#',\n          cell: '单元格',\n          value: '值'\n        },\n        empty: '(空值)',\n        reError: '无效的正则表达式',\n        recordCount: '已找到 {0} 个单元格',\n        notCell: '找不到匹配的单元格',\n        replaceSuccess: '成功替换 {0} 个单元格'\n      }\n    },\n    renderer: {\n      search: '搜索',\n      cases: {\n        equal: '等于',\n        unequal: '不等于',\n        gt: '大于',\n        ge: '大于或等于',\n        lt: '小于',\n        le: '小于或等于',\n        begin: '开头是',\n        notbegin: '开头不是',\n        endin: '结尾是',\n        notendin: '结尾不是',\n        include: '包含',\n        exclude: '不包含',\n        between: '介于',\n        custom: '自定义筛选',\n        insensitive: '不区分大小写',\n        isSensitive: '区分大小写'\n      },\n      combination: {\n        menus: {\n          sort: '排序',\n          clearSort: '清除排序',\n          sortAsc: '升序',\n          sortDesc: '降序',\n          fixedColumn: '冻结列',\n          fixedGroup: '冻结分组',\n          cancelFixed: '取消冻结',\n          fixedLeft: '冻结到左侧',\n          fixedRight: '冻结到右侧',\n          clearFilter: '清除筛选',\n          textOption: '文本筛选',\n          numberOption: '数值筛选'\n        },\n        popup: {\n          title: '自定义筛选的方式',\n          currColumnTitle: '当前列：',\n          and: '与',\n          or: '或',\n          describeHtml: '可用 ? 代表单个字符<br/>用 * 代表任意多个字符'\n        },\n        empty: '(空白)',\n        notData: '无匹配项'\n      }\n    }\n  }\n};"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW,QAAQ,UAAU;AAAA,MAC/B,KAAK;AAAA,QACH,MAAM;AAAA,UACJ,aAAa;AAAA,UACb,cAAc;AAAA,UACd,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,QACA,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,iBAAiB;AAAA,UACjB,UAAU;AAAA,UACV,eAAe;AAAA,UACf,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,UACjB,cAAc;AAAA,UACd,QAAQ;AAAA,UACR,eAAe;AAAA,UACf,eAAe;AAAA,UACf,SAAS;AAAA,UACT,WAAW;AAAA,UACX,SAAS;AAAA,UACT,WAAW;AAAA,UACX,SAAS;AAAA,UACT,UAAU;AAAA,UACV,SAAS;AAAA,UACT,SAAS;AAAA,UACT,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,WAAW;AAAA,UACX,WAAW;AAAA,UACX,UAAU;AAAA,UACV,WAAW;AAAA,UACX,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,eAAe;AAAA,UACf,WAAW;AAAA,UACX,WAAW;AAAA,QACb;AAAA,QACA,OAAO;AAAA,UACL,WAAW;AAAA,UACX,UAAU;AAAA,UACV,UAAU;AAAA,UACV,aAAa;AAAA,UACb,eAAe;AAAA,UACf,aAAa;AAAA,UACb,WAAW;AAAA,UACX,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,aAAa;AAAA,UACb,mBAAmB;AAAA,UACnB,aAAa;AAAA,UACb,WAAW;AAAA,UACX,eAAe;AAAA,UACf,aAAa;AAAA,UACb,cAAc;AAAA,UACd,eAAe;AAAA,UACf,aAAa;AAAA,UACb,SAAS;AAAA,UACT,cAAc;AAAA,UACd,cAAc;AAAA,UACd,sBAAsB;AAAA,QACxB;AAAA,QACA,MAAM;AAAA,UACJ,iBAAiB;AAAA,UACjB,oBAAoB;AAAA,UACpB,oBAAoB;AAAA,UACpB,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,WAAW;AAAA,QACb;AAAA,QACA,QAAQ;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,WAAW;AAAA,QACb;AAAA,QACA,OAAO;AAAA,UACL,MAAM;AAAA,UACN,WAAW;AAAA,UACX,UAAU;AAAA,UACV,OAAO;AAAA,UACP,gBAAgB;AAAA,UAChB,UAAU;AAAA,UACV,eAAe;AAAA,UACf,UAAU;AAAA,UACV,eAAe;AAAA,UACf,UAAU;AAAA,UACV,eAAe;AAAA,UACf,UAAU;AAAA,UACV,eAAe;AAAA,UACf,UAAU;AAAA,UACV,eAAe;AAAA,UACf,SAAS;AAAA,UACT,cAAc;AAAA,QAChB;AAAA,QACA,OAAO;AAAA,UACL,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,WAAW;AAAA,UACX,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,oBAAoB;AAAA,UACpB,gBAAgB;AAAA,UAChB,SAAS;AAAA,YACP,SAAS;AAAA,YACT,aAAa;AAAA,YACb,UAAU;AAAA,YACV,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,aAAa;AAAA,YACb,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,OAAO;AAAA,YACL,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,WAAW;AAAA,YACX,cAAc;AAAA,UAChB;AAAA,UACA,UAAU;AAAA,UACV,SAAS;AAAA,UACT,WAAW;AAAA,UACX,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,WAAW;AAAA,QACb;AAAA,QACA,QAAQ;AAAA,UACN,OAAO;AAAA,YACL,KAAK;AAAA,YACL,MAAM;AAAA,YACN,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,MAAM;AAAA,YACN,KAAK;AAAA,UACP;AAAA,UACA,OAAO;AAAA,YACL,OAAO;AAAA,YACP,SAAS;AAAA,YACT,UAAU;AAAA,YACV,KAAK;AAAA,UACP;AAAA,UACA,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,SAAS;AAAA,UACT,oBAAoB;AAAA,UACpB,cAAc;AAAA,UACd,yBAAyB;AAAA,UACzB,SAAS;AAAA,UACT,SAAS;AAAA,UACT,kBAAkB;AAAA,UAClB,WAAW;AAAA,UACX,SAAS;AAAA,UACT,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,eAAe;AAAA,UACf,kBAAkB;AAAA,UAClB,aAAa;AAAA,UACb,eAAe;AAAA,UACf,iBAAiB;AAAA,UACjB,mBAAmB;AAAA,UACnB,gBAAgB;AAAA,UAChB,kBAAkB;AAAA,UAClB,gBAAgB;AAAA,UAChB,kBAAkB;AAAA,UAClB,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,WAAW;AAAA,QACb;AAAA,QACA,OAAO;AAAA,UACL,UAAU;AAAA,UACV,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,UACP,aAAa;AAAA,UACb,aAAa;AAAA,QACf;AAAA,QACA,QAAQ;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,WAAW;AAAA,QACb;AAAA,QACA,SAAS;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,WAAW;AAAA,UACX,eAAe;AAAA,UACf,eAAe;AAAA,UACf,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,aAAa;AAAA,QACf;AAAA,QACA,YAAY;AAAA,UACV,WAAW;AAAA,QACb;AAAA,QACA,OAAO;AAAA,UACL,MAAM;AAAA,YACJ,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,aAAa;AAAA,cACX,MAAM;AAAA,cACN,MAAM;AAAA,cACN,UAAU;AAAA,cACV,MAAM;AAAA,cACN,OAAO;AAAA,cACP,SAAS;AAAA,cACT,MAAM;AAAA,YACR;AAAA,YACA,OAAO;AAAA,cACL,GAAG;AAAA,cACH,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,YACN;AAAA,YACA,QAAQ;AAAA,cACN,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,YACA,UAAU;AAAA,cACR,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,YACN;AAAA,UACF;AAAA,QACF;AAAA,QACA,aAAa;AAAA,UACX,gBAAgB;AAAA,QAClB;AAAA,QACA,cAAc;AAAA,UACZ,YAAY;AAAA,UACZ,SAAS;AAAA,YACP,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,SAAS;AAAA,YACT,OAAO;AAAA,YACP,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,OAAO;AAAA,YACP,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,iBAAiB;AAAA,UACjB,aAAa;AAAA,UACb,cAAc;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,eAAe;AAAA,UACf,eAAe;AAAA,UACf,cAAc;AAAA,UACd,mBAAmB;AAAA,UACnB,aAAa;AAAA,UACb,UAAU;AAAA,UACV,gBAAgB;AAAA,UAChB,WAAW;AAAA,UACX,eAAe;AAAA,UACf,aAAa;AAAA,UACb,eAAe;AAAA,UACf,WAAW;AAAA,YACT,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,WAAW;AAAA,UACb;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,QACX;AAAA,QACA,aAAa;AAAA,UACX,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAAA,QACA,YAAY;AAAA,UACV,UAAU;AAAA,UACV,cAAc;AAAA,UACd,eAAe;AAAA,UACf,eAAe;AAAA,UACf,OAAO;AAAA,YACL,WAAW;AAAA,YACX,UAAU;AAAA,UACZ;AAAA,UACA,cAAc;AAAA,YACZ,KAAK;AAAA,YACL,OAAO;AAAA,YACP,aAAa;AAAA,YACb,gBAAgB;AAAA,YAChB,kBAAkB;AAAA,YAClB,YAAY;AAAA,YACZ,WAAW;AAAA,YACX,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,YAAY;AAAA,YACZ,cAAc;AAAA,YACd,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,YAAY;AAAA,YACZ,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,SAAS;AAAA,UACX;AAAA,UACA,QAAQ;AAAA,YACN,OAAO;AAAA,cACL,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,QAAQ;AAAA,cACR,QAAQ;AAAA,cACR,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,YACA,WAAW;AAAA,YACX,WAAW;AAAA,cACT,OAAO;AAAA,cACP,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,KAAK;AAAA,cACL,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,cACV,WAAW;AAAA,cACX,UAAU;AAAA,cACV,gBAAgB;AAAA,cAChB,eAAe;AAAA,cACf,aAAa;AAAA,cACb,WAAW;AAAA,cACX,eAAe;AAAA,cACf,eAAe;AAAA,cACf,kBAAkB;AAAA,cAClB,eAAe;AAAA,cACf,gBAAgB;AAAA,cAChB,SAAS;AAAA,cACT,WAAW;AAAA,YACb;AAAA,UACF;AAAA,UACA,YAAY;AAAA,YACV,MAAM;AAAA,YACN,aAAa;AAAA,YACb,UAAU;AAAA,YACV,UAAU;AAAA,YACV,gBAAgB;AAAA,cACd,MAAM;AAAA,cACN,IAAI;AAAA,cACJ,QAAQ;AAAA,cACR,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA,YACA,YAAY;AAAA,cACV,MAAM;AAAA,cACN,UAAU;AAAA,cACV,WAAW;AAAA,cACX,iBAAiB;AAAA,cACjB,cAAc;AAAA,cACd,iBAAiB;AAAA,cACjB,aAAa;AAAA,YACf;AAAA,YACA,SAAS;AAAA,cACP,SAAS;AAAA,cACT,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,QAAQ;AAAA,YACV;AAAA,YACA,UAAU;AAAA,cACR,MAAM;AAAA,cACN,YAAY;AAAA,cACZ,WAAW;AAAA,cACX,aAAa;AAAA,cACb,YAAY;AAAA,cACZ,YAAY;AAAA,cACZ,WAAW;AAAA,cACX,WAAW;AAAA,cACX,YAAY;AAAA,cACZ,UAAU;AAAA,YACZ;AAAA,YACA,cAAc;AAAA,cACZ,UAAU;AAAA,cACV,SAAS;AAAA,cACT,cAAc;AAAA,cACd,YAAY;AAAA,cACZ,UAAU;AAAA,YACZ;AAAA,YACA,YAAY;AAAA,cACV,gBAAgB;AAAA,cAChB,eAAe;AAAA,cACf,WAAW;AAAA,cACX,eAAe;AAAA,cACf,cAAc;AAAA,cACd,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA,QACA,YAAY;AAAA,UACV,iBAAiB;AAAA,UACjB,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,WAAW;AAAA,UACX,aAAa;AAAA,UACb,WAAW;AAAA,UACX,WAAW;AAAA,YACT,oBAAoB;AAAA,YACpB,oBAAoB;AAAA,UACtB;AAAA,UACA,QAAQ;AAAA,YACN,QAAQ;AAAA,YACR,WAAW;AAAA,YACX,gBAAgB;AAAA,UAClB;AAAA,UACA,aAAa;AAAA,YACX,UAAU;AAAA,YACV,SAAS;AAAA,UACX;AAAA,QACF;AAAA,QACA,MAAM;AAAA,UACJ,aAAa;AAAA,UACb,WAAW;AAAA,QACb;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,YACP,MAAM;AAAA,YACN,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,IAAI;AAAA,UACN;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,gBAAgB;AAAA,YACd,MAAM;AAAA,cACJ,UAAU;AAAA,cACV,UAAU;AAAA,cACV,WAAW;AAAA,cACX,WAAW;AAAA,cACX,eAAe;AAAA,cACf,cAAc;AAAA,YAChB;AAAA,YACA,KAAK;AAAA,cACH,OAAO;AAAA,cACP,WAAW;AAAA,cACX,cAAc;AAAA,cACd,WAAW;AAAA,cACX,cAAc;AAAA,cACd,MAAM;AAAA,gBACJ,MAAM;AAAA,gBACN,SAAS;AAAA,cACX;AAAA,cACA,QAAQ;AAAA,gBACN,IAAI;AAAA,gBACJ,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA,MAAM;AAAA,gBACJ,UAAU;AAAA,gBACV,SAAS;AAAA,gBACT,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA,cACV;AAAA,cACA,QAAQ;AAAA,gBACN,KAAK;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO;AAAA,cACT;AAAA,cACA,MAAM;AAAA,gBACJ,KAAK;AAAA,gBACL,KAAK;AAAA,cACP;AAAA,cACA,OAAO;AAAA,cACP,SAAS;AAAA,cACT,aAAa;AAAA,cACb,SAAS;AAAA,cACT,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,UACA,oBAAoB;AAAA,YAClB,OAAO;AAAA,cACL,aAAa;AAAA,cACb,YAAY;AAAA,cACZ,aAAa;AAAA,cACb,WAAW;AAAA,cACX,YAAY;AAAA,YACd;AAAA,YACA,OAAO;AAAA,cACL,OAAO;AAAA,cACP,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,OAAO;AAAA,cACP,OAAO;AAAA,cACP,SAAS;AAAA,cACT,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,mBAAmB;AAAA,YACjB,OAAO;AAAA,cACL,MAAM;AAAA,cACN,WAAW;AAAA,cACX,SAAS;AAAA,cACT,UAAU;AAAA,cACV,aAAa;AAAA,cACb,YAAY;AAAA,cACZ,aAAa;AAAA,cACb,WAAW;AAAA,cACX,YAAY;AAAA,cACZ,aAAa;AAAA,cACb,YAAY;AAAA,cACZ,cAAc;AAAA,YAChB;AAAA,YACA,OAAO;AAAA,cACL,OAAO;AAAA,cACP,iBAAiB;AAAA,cACjB,KAAK;AAAA,cACL,IAAI;AAAA,cACJ,cAAc;AAAA,YAChB;AAAA,YACA,OAAO;AAAA,cACL,OAAO;AAAA,cACP,SAAS;AAAA,cACT,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ,OAAO;AAAA,cACP,UAAU;AAAA,cACV,OAAO;AAAA,cACP,UAAU;AAAA,cACV,SAAS;AAAA,cACT,SAAS;AAAA,cACT,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,aAAa;AAAA,cACb,aAAa;AAAA,YACf;AAAA,YACA,OAAO;AAAA,YACP,SAAS;AAAA,UACX;AAAA,QACF;AAAA,QACA,KAAK;AAAA,UACH,MAAM;AAAA,YACJ,UAAU;AAAA,YACV,UAAU;AAAA,YACV,WAAW;AAAA,YACX,eAAe;AAAA,UACjB;AAAA,UACA,KAAK;AAAA,YACH,OAAO;AAAA,YACP,WAAW;AAAA,YACX,cAAc;AAAA,YACd,WAAW;AAAA,YACX,cAAc;AAAA,YACd,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA,QAAQ;AAAA,cACN,IAAI;AAAA,cACJ,OAAO;AAAA,cACP,WAAW;AAAA,YACb;AAAA,YACA,MAAM;AAAA,cACJ,UAAU;AAAA,cACV,SAAS;AAAA,cACT,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,YACA,OAAO;AAAA,YACP,SAAS;AAAA,YACT,aAAa;AAAA,YACb,SAAS;AAAA,YACT,gBAAgB;AAAA,UAClB;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,YACL,OAAO;AAAA,YACP,SAAS;AAAA,YACT,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,YACP,UAAU;AAAA,YACV,SAAS;AAAA,YACT,SAAS;AAAA,YACT,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,aAAa;AAAA,YACb,aAAa;AAAA,UACf;AAAA,UACA,aAAa;AAAA,YACX,OAAO;AAAA,cACL,MAAM;AAAA,cACN,WAAW;AAAA,cACX,SAAS;AAAA,cACT,UAAU;AAAA,cACV,aAAa;AAAA,cACb,YAAY;AAAA,cACZ,aAAa;AAAA,cACb,WAAW;AAAA,cACX,YAAY;AAAA,cACZ,aAAa;AAAA,cACb,YAAY;AAAA,cACZ,cAAc;AAAA,YAChB;AAAA,YACA,OAAO;AAAA,cACL,OAAO;AAAA,cACP,iBAAiB;AAAA,cACjB,KAAK;AAAA,cACL,IAAI;AAAA,cACJ,cAAc;AAAA,YAChB;AAAA,YACA,OAAO;AAAA,YACP,SAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}