<script lang="ts" setup>
    import { onMounted, reactive, ref, watch,h } from 'vue';
    import { Page } from '@vben/common-ui';
    import dayjs from 'dayjs';
    import { message, notification, Modal  } from 'ant-design-vue';
    import tableComp from '#/components/TableComp/table.vue';
    const deviceId = ref();
    const terminalName = ref();
    const loading = ref<boolean>(true);
    // 表格数据源
    const dataSource = reactive<any>({
        data: {},
    });
    // 表格相关配置
    const columns = [
        { title: '白天夜晚', dataIndex: 'name' },
        { title: '设备状态', dataIndex: 'code'},
        { title: '充电状态', dataIndex: 'loction' },
        { title: '控制器温度', dataIndex: 'c'},
        { title: 'PV阵列',
            dataIndex: 'a',
            children:[
                { title: '电压', dataIndex: 'dep'},
                { title: '电流', dataIndex: 'dep'},
                { title: '发电功率', dataIndex: 'type'}
            ]
        },
        { title: '直流负载', dataIndex: 'b', children:[
                { title: '电压', dataIndex: 'type'},
                { title: '电流', dataIndex: 'type'},
                { title: '功率', dataIndex: 'type'}
            ]
        },
        { title: '蓄电池', dataIndex: 'type',children:[
                { title: '温度', dataIndex: 'type'},
                { title: '电流', dataIndex: 'type'},
                { title: '电压', dataIndex: 'type'},
                { title: '最高电压', dataIndex: 'type'},
                { title: '最低电压', dataIndex: 'type'},
                { title: '剩余电量', dataIndex: 'type'},
                { title: '电压状态', dataIndex: 'type'},


            ]
        },
        { title: '用电量', dataIndex: 'd',children:[
                { title: '当日', dataIndex: 'type'},
                { title: '当月', dataIndex: 'type'},
                { title: '当年', dataIndex: 'type'},
                { title: '总累计', dataIndex: 'type'},
            ]
        },
        { title: '更新时间', dataIndex: 'type',width: '180px'},
    ];

    //条件查询
    const queryParam = reactive({
        dayTime: dayjs(), // 名称
        page: 1,
        size: 10,
    });

    const initTableList = async () => {
        loading.value = false;
        const pre = {
            'page':queryParam.page,
            'size':queryParam.size,
            'deviceId':deviceId.value,
            'dayTime':queryParam.dayTime.format('YYYY-MM-DD'),
        }
        console.log(pre)
        // const resData = await DeviceApi.getDevicesList_Api(queryParam)
        // if (!resData) message.error( '获取数据失败！');
        dataSource.data = {
            code: '200',
            msg: 'yes',
            data: {
                total:1,
                size:10,
                current:1,
                records:[
                    {'id': '1','name': '白天夜晚','type':'20','status': true,'code': 158856012544,'dep': '1'},
                    {'id': '11','name': '1q123123','type':'76','status': true,'code': 158856022544,'dep': '1'},
                    {'id': '12','name': 'w123123','type':'34','status': true,'code': 158856302544,'dep': '2'},
                    {'id': '13','name': '1e23123','type':'56','status': true,'code': 158856402544,'dep': '3'},
                    {'id': '14','name': 't23123','type':'87','status': true,'code': 158855602544,'dep': '1'},
                    {'id': '15','name': '123123','type':'98','status': true,'code': 158856602544,'dep': '4'},
                    {'id': '16','name': '1y23123','type':'12','status': true,'code': 158857602544,'dep': '1'},
                    {'id': '17','name': '1u23123','type':'4','status': true,'code': 158856802544,'dep': '2'},
                    {'id': '18','name': '1i23123','type':'67','status': true,'code': 158859602544,'dep': '1'},
                    {'id': '19','name': '1o23123','type':'67','status': false,'code': 154885602544,'dep': '3'},
                ]
            },
        };
    };
    const success = (data: any) => {
        queryParam.page = data.pi;
        queryParam.size = data.ps;
        initTableList();
    };
    const searchTable = () => {
        queryParam.page = 1;
        initTableList();
    };

    // 重置
    const resetTable = () => {
        queryParam.dayTime = dayjs();
        queryParam.page = 1;
        queryParam.size = 10;
        initTableList();
    };
    onMounted(()=>{
        const pre = localStorage.getItem('terminalTemp') ? JSON.parse(localStorage.getItem('terminalTemp')) : {};
        deviceId.value = pre.deviceId
        terminalName.value = pre.terminalName
        initTableList()
    });
</script>

<template>
  <Page>
    <a-card class="table_header_search mb-5">
      <a-row :gutter="20">
        <a-col class="flex items-center text-[20px] text-[hsl(var(--primary))]">
          {{terminalName}}
        </a-col>
        <a-col :span="6">
          <label>日期：</label>
          <div class="table_header_wrp_cont">
            <a-date-picker v-model:value="queryParam.dayTime" format="YYYY-MM-DD" style="width: 100%"/>
          </div>
        </a-col>

        <a-col :span="5">
          <a-button type="primary" class="searchBtn" @click="searchTable">
            查询
          </a-button>
          <a-button class="refBtn" @click="resetTable">重置</a-button>
        </a-col>
      </a-row>
    </a-card>
    <a-card size="small">
      <div class="table_action_btn_wrp">
        <a-button class="addBtn" type="primary" >
          导出
        </a-button>
      </div>
      <table-Comp
        :dataSource="dataSource.data"
        :columns="columns"
        :loading="loading"
        @isLoadingFuc="(e) => loading = e"
        @success="success"
        :scroll="{ x: 2000 }"
      >
        <!-- 终端状态 -->
        <template #status="{ record }">
          <a-tag :bordered="false" color="success" v-if="record.status">正常</a-tag>
          <a-tag :bordered="false" color="error" v-if="!record.status">异常</a-tag>
        </template>
      </table-Comp>
    </a-card>
  </Page>
</template>
<style scoped lang="scss">
  :deep(.ant-table-thead> tr > th) {
    padding: 6px 16px !important;
    border: 1px solid #f4f4f4 !important;
  }
</style>
