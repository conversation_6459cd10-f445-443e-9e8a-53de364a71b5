<template>
  <a-modal
    v-model:open="visible"
    :destroyOnClose="true"
    title="设备位置"
    width="800px"
    :footer="null"
    :zIndex="9999"
    @cancel="handleClose"
  >
    <div class="location-modal-content">
      <div class="map-container">
        <LeaFletMap
          ref="mapRef"
          :mapCenter="mapCenter"
          @mapInited="handleMapInited"
        />
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import LeaFletMap from '../LeaFletMap/index.vue';
import videoPng from '#/assets/images/video_sxt.png';

// 摄像头图标配置
const CAMERA_ICON_URL = videoPng;

interface CameraItem {
  id?: string;
  name?: string;
  code?: string;
  status?: string;
  location?: string;
  longitude: number;
  latitude: number;
  batteryLevel?: number;
  remarks?: string;
}

interface Props {
  open: boolean;
  // 方式1: 传入cameras数组
  cameras?: CameraItem[];
  // 方式2: 传入单个经纬度
  longitude?: number;
  latitude?: number;
  deviceId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  open: false,
  cameras: () => [],
  longitude: 0,
  latitude: 0,
  deviceId: '',
});

const emit = defineEmits(['update:open']);

const visible = ref(false);
const mapRef = ref();
const mapCenter = ref<any>([30.65066, 104.09024]);
let mapInstance: any = null;

// 监听props变化
watch(
  () => props.open,
  (newVal) => {
    visible.value = newVal;
    if (newVal) {
      nextTick(() => {
        addMarkers();
      });
    }
  },
  // { immediate: true },
);

watch(
  () => visible.value,
  (newVal) => {
    emit('update:open', newVal);
  },
);

// 地图初始化完成
const handleMapInited = (map: any) => {
  mapInstance = map;
  addMarkers();
};

// 创建摄像头标记数据
const createCameraMarkers = () => {
  const markers: any[] = [];

  // 方式1: 如果有cameras数组，为每个摄像头创建标记
  if (props.cameras && props.cameras.length > 0) {
    props.cameras.forEach((camera: any) => {
      if (camera.longitude && camera.latitude) {
        markers.push({
          latitude: camera.latitude,
          longitude: camera.longitude,
          name: camera.name || '摄像头',
          code: camera.code,
          status: camera.status,
          location: camera.location,
          batteryLevel: camera.batteryLevel,
          remarks: camera.remarks,
          markerOptions: {
            icon: {
              iconUrl: CAMERA_ICON_URL,
              iconSize: [24, 24],
              iconAnchor: [12, 12],
            },
          },
          popupOptions: {
            offset: [0, -10],
            closeButton: true,
          },
        });
      }
    });
  }
  // 方式2: 如果有单个经纬度，创建单个标记
  else if (props.longitude && props.latitude) {
    markers.push({
      latitude: props.latitude,
      longitude: props.longitude,
      name: '设备位置',
      code: props.deviceId,
      status: 'UNKNOWN',
      markerOptions: {
        icon: {
          iconUrl: CAMERA_ICON_URL,
          iconSize: [24, 24],
          iconAnchor: [12, 12],
        },
      },
      popupOptions: {
        offset: [0, -10],
        closeButton: true,
      },
    });
  }

  return markers;
};

// 添加标记到地图
const cameraMarkers = ref<any>([]);
const addMarkers = () => {
  if (!mapInstance) return;

  nextTick(() => {
    cameraMarkers.value = createCameraMarkers();

    if (cameraMarkers.value.length === 0) return;

    // 使用地图组件的createMarker方法
    if (mapRef.value && mapRef.value.createMarker) {
      mapRef.value.createMarker(cameraMarkers.value, false);
    }

    // 设置地图视图
    if (cameraMarkers.value.length === 1) {
      // 单个标记，设置固定缩放级别
      mapInstance.setView(
        [cameraMarkers.value[0].latitude, cameraMarkers.value[0].longitude],
        16,
      );
    } else if (cameraMarkers.value.length > 1) {
      // 多个标记，适配所有标记的边界
      const bounds = cameraMarkers.value.map((marker: any) => [
        marker.latitude,
        marker.longitude,
      ]);
      mapInstance.fitBounds(bounds, { padding: [20, 20] });
    }
  });
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;

  // 清除所有标记
  clearCameraMarkers();
};

// 清除摄像头标记
const clearCameraMarkers = () => {
  if (cameraMarkers.value.length > 0) {
    cameraMarkers.value.forEach((marker: any) => {
      if (marker && marker.remove) {
        marker.remove();
      }
    });
    cameraMarkers.value = [];
  }
};

defineExpose({
  handleClose,
});
</script>

<style scoped lang="scss">
.location-modal-content {
  display: flex;
  flex-direction: column;
  height: 500px;

  .map-container {
    flex: 1;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 16px;
  }

  .device-info {
    background: #fafafa;
    padding: 16px;
    border-radius: 6px;
    border: 1px solid #d9d9d9;

    .info-item {
      display: flex;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 80px;
        color: #666;
        font-weight: 500;
      }

      .value {
        flex: 1;
        color: #333;

        &.status-online {
          color: #52c41a;
          font-weight: 500;
        }

        &.status-offline {
          color: #ff4d4f;
          font-weight: 500;
        }

        &.status-unknown {
          color: #faad14;
          font-weight: 500;
        }
      }
    }
  }
}

// 摄像头弹窗样式
:deep(.camera-popup) {
  min-width: 220px;

  .popup-header {
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    margin-bottom: 8px;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }

  .popup-content {
    .popup-item {
      display: flex;
      margin-bottom: 6px;
      font-size: 12px;
      line-height: 1.4;

      &:last-child {
        margin-bottom: 0;
      }

      span:first-child {
        width: 45px;
        color: #666;
        font-weight: 500;
        flex-shrink: 0;
      }

      span:last-child {
        flex: 1;
        color: #333;
        word-break: break-all;

        &.status-online {
          color: #52c41a;
          font-weight: 500;
        }

        &.status-offline {
          color: #ff4d4f;
          font-weight: 500;
        }

        &.status-unknown {
          color: #faad14;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
