@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\git\line-inspection\line-ui\node_modules\.pnpm\@commitlint+cli@19.8.0_@types+node@22.15.3_typescript@5.8.3\node_modules\@commitlint\cli\node_modules;D:\git\line-inspection\line-ui\node_modules\.pnpm\@commitlint+cli@19.8.0_@types+node@22.15.3_typescript@5.8.3\node_modules\@commitlint\node_modules;D:\git\line-inspection\line-ui\node_modules\.pnpm\@commitlint+cli@19.8.0_@types+node@22.15.3_typescript@5.8.3\node_modules;D:\git\line-inspection\line-ui\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\git\line-inspection\line-ui\node_modules\.pnpm\@commitlint+cli@19.8.0_@types+node@22.15.3_typescript@5.8.3\node_modules\@commitlint\cli\node_modules;D:\git\line-inspection\line-ui\node_modules\.pnpm\@commitlint+cli@19.8.0_@types+node@22.15.3_typescript@5.8.3\node_modules\@commitlint\node_modules;D:\git\line-inspection\line-ui\node_modules\.pnpm\@commitlint+cli@19.8.0_@types+node@22.15.3_typescript@5.8.3\node_modules;D:\git\line-inspection\line-ui\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@commitlint\cli\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@commitlint\cli\cli.js" %*
)
