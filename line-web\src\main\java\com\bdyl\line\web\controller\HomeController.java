package com.bdyl.line.web.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bdyl.boot.JsonResult;
import com.bdyl.line.web.model.response.home.*;
import com.bdyl.line.web.service.HomeStatsService;

/**
 * 首页统计管理
 *
 * <AUTHOR>
 * @since 1.0
 */
@RestController
@RequestMapping("/home")
@RequiredArgsConstructor
public class HomeController {
    /**
     * 首页统计服务
     */
    private final HomeStatsService homeStatsService;

    /**
     * 设备统计
     *
     * @return 统计数据
     */
    @GetMapping("/device-stats")
    public JsonResult<HomeDeviceStatsResponse> deviceStats() {
        return JsonResult.success(homeStatsService.getDeviceStats());
    }

    /**
     * 业务报警统计
     *
     * @return 统计数据
     */
    @GetMapping("/biz-alert-stats")
    public JsonResult<HomeBizAlertStatsResponse> bizAlertStats() {
        return JsonResult.success(homeStatsService.getBizAlertStats());
    }

    /**
     * 物联报警统计
     *
     * @return 统计数据
     */
    @GetMapping("/iot-alert-stats")
    public JsonResult<HomeIotAlertStatsResponse> iotAlertStats() {
        return JsonResult.success(homeStatsService.getIotAlertStats());
    }

    /**
     * 业务报警按年分月类型统计
     *
     * @param year 年份
     * @return 统计数据
     */
    @GetMapping("/biz-alert-month-type-stats")
    public JsonResult<List<HomeBizAlertMonthTypeCountResponse>> bizAlertMonthTypeStats(@RequestParam int year) {
        return JsonResult.success(homeStatsService.getBizAlertMonthTypeStats(year));
    }

    /**
     * 按场景统计报警数量
     *
     * @param year 年份
     * @return 统计数据
     */
    @GetMapping("/biz-alert-scene-stats")
    public JsonResult<HomeBizAlertSceneStatsResponse> bizAlertSceneStats(@RequestParam int year) {
        HomeBizAlertSceneStatsResponse result = homeStatsService.getBizAlertSceneStats(year);
        return JsonResult.success(result);
    }

    /**
     * 统计某年的巡检漏检比例
     *
     * @param year 年份
     * @return 统计数据
     */
    @GetMapping("/miss-inspection-stats")
    public JsonResult<List<MissInspectionMonthTypeCountResponse>> missInspectionStats(@RequestParam int year) {
        List<MissInspectionMonthTypeCountResponse> resultList = homeStatsService.getMissInspectionStats(year);
        return JsonResult.success(resultList);
    }
}
