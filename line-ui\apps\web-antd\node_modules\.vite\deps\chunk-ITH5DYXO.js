import {
  _extends
} from "./chunk-LHAI6UAP.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/omit.js
function omit(obj, fields) {
  const shallowCopy = _extends({}, obj);
  for (let i = 0; i < fields.length; i += 1) {
    const key = fields[i];
    delete shallowCopy[key];
  }
  return shallowCopy;
}
var omit_default = omit;

export {
  omit_default
};
//# sourceMappingURL=chunk-ITH5DYXO.js.map
