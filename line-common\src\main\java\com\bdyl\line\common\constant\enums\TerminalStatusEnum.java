package com.bdyl.line.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 终端状态枚举
 *
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@AllArgsConstructor
public enum TerminalStatusEnum {

    /**
     * 正常
     */
    NORMAL("NORMAL", "正常", "终端运行正常"),
    /**
     * 异常
     */
    ABNORMAL("ABNORMAL", "异常", "终端运行异常");

    /**
     * value
     */
    private final String value;
    /**
     * 名称
     */
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据value获取枚举
     *
     * @param value value
     * @return TerminalStatusEnum
     */
    public static TerminalStatusEnum fromValue(String value) {
        for (TerminalStatusEnum terminalStatusEnum : TerminalStatusEnum.values()) {
            if (terminalStatusEnum.getValue().equals(value)) {
                return terminalStatusEnum;
            }
        }
        return null;
    }
}
