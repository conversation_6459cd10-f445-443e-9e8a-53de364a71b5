# 线路巡检管理系统 - API文档

## 1. 接口概述

### 1.1 基本信息
- **基础URL**：`http://your-domain.com/api/line`
- **协议**：HTTP/HTTPS
- **数据格式**：JSON
- **字符编码**：UTF-8

### 1.2 认证方式
系统采用JWT Token认证，需要在请求头中携带认证信息：
```
Authorization: Bearer <your-jwt-token>
```

### 1.3 统一响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": "2025-01-01T00:00:00Z"
}
```

**响应状态码说明**：
- `200`：请求成功
- `400`：请求参数错误
- `401`：未授权访问
- `403`：权限不足
- `404`：资源不存在
- `500`：服务器内部错误

### 1.4 分页响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "page": 1,
        "size": 10,
        "total": 100,
        "records": []
    },
    "timestamp": "2025-01-01T00:00:00Z"
}
```

## 2. 摄像头管理接口

### 2.1 分页查询摄像头
**接口地址**：`GET /camera/page`

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 页大小，默认10 |
| name | String | 否 | 摄像头名称 |
| code | String | 否 | 摄像头编码 |
| status | String | 否 | 状态：ONLINE/OFFLINE |
| organId | Long | 否 | 组织ID |

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "page": 1,
        "size": 10,
        "total": 50,
        "records": [
            {
                "id": 1,
                "name": "摄像头001",
                "code": "CAM001",
                "location": "主干道路口",
                "status": "ONLINE",
                "ip": "*************",
                "port": 554,
                "longitude": 116.397128,
                "latitude": 39.916527,
                "createTime": "2025-01-01T10:00:00"
            }
        ]
    },
    "timestamp": "2025-01-01T10:00:00Z"
}
```

### 2.2 创建摄像头
**接口地址**：`POST /camera`

**请求体**：
```json
{
    "name": "摄像头001",
    "code": "CAM001",
    "location": "主干道路口",
    "ip": "*************",
    "port": 554,
    "username": "admin",
    "password": "123456",
    "longitude": 116.397128,
    "latitude": 39.916527,
    "terminalId": 1
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "创建成功",
    "data": true,
    "timestamp": "2025-01-01T10:00:00Z"
}
```

### 2.3 播放视频
**接口地址**：`POST /camera/play`

**请求体**：
```json
{
    "deviceCode": "CAM001",
    "deviceChannel": "1",
    "streamType": "main"
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "code": "CAM001",
        "name": "摄像头001",
        "streamUrls": [
            "rtsp://*************:554/stream1",
            "http://*************:8080/hls/stream1.m3u8"
        ],
        "boxList": [],
        "batteryLevel": 85
    },
    "timestamp": "2025-01-01T10:00:00Z"
}
```

### 2.4 历史回放
**接口地址**：`POST /camera/playback`

**请求体**：
```json
{
    "deviceCode": "CAM001",
    "deviceChannel": "1",
    "startTime": "2025-01-01T08:00:00",
    "endTime": "2025-01-01T10:00:00"
}
```

## 3. 巡检计划接口

### 3.1 分页查询巡检计划
**接口地址**：`GET /inspection/plan/page`

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 页大小，默认10 |
| name | String | 否 | 计划名称 |
| cycleType | String | 否 | 周期类型：HOUR/DAY/WEEK/MONTH |
| status | String | 否 | 状态：ENABLE/DISABLE |

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "page": 1,
        "size": 10,
        "total": 20,
        "records": [
            {
                "id": 1,
                "name": "日常巡检计划",
                "cycleType": "DAY",
                "cycleValue": 1,
                "startDate": "2025-01-01",
                "startTime": "08:00:00",
                "endTime": "18:00:00",
                "responsibleUserId": 1,
                "responsibleUserName": "张三",
                "status": "ENABLE",
                "cameraCount": 10,
                "createTime": "2025-01-01T10:00:00"
            }
        ]
    },
    "timestamp": "2025-01-01T10:00:00Z"
}
```

### 3.2 创建巡检计划
**接口地址**：`POST /inspection/plan`

**请求体**：
```json
{
    "name": "日常巡检计划",
    "cycleType": "DAY",
    "cycleValue": 1,
    "startDate": "2025-01-01",
    "startTime": "08:00:00",
    "endTime": "18:00:00",
    "responsibleUserId": 1,
    "cameraIds": [1, 2, 3, 4, 5],
    "description": "每日例行巡检"
}
```

**小时级计划示例**：
```json
{
    "name": "小时级巡检计划",
    "cycleType": "HOUR",
    "cycleValue": 1,
    "startDate": "2025-01-01",
    "timeSlots": [
        {
            "startTime": "09:00:00",
            "endTime": "10:00:00"
        },
        {
            "startTime": "14:00:00",
            "endTime": "15:00:00"
        }
    ],
    "responsibleUserId": 1,
    "cameraIds": [1, 2, 3]
}
```

**周级计划示例**：
```json
{
    "name": "周级巡检计划",
    "cycleType": "WEEK",
    "cycleValue": 1,
    "dayValue": 1,
    "startDate": "2025-01-01",
    "startTime": "09:00:00",
    "endTime": "17:00:00",
    "responsibleUserId": 1,
    "cameraIds": [1, 2, 3]
}
```

## 4. 巡检任务接口

### 4.1 分页查询巡检任务
**接口地址**：`GET /inspection/task/page`

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 页大小，默认10 |
| status | String | 否 | 状态：PENDING/IN_PROGRESS/COMPLETED/MISSED |
| responsibleUserId | Long | 否 | 负责人ID |
| planId | Long | 否 | 计划ID |
| startDate | Date | 否 | 开始日期 |
| endDate | Date | 否 | 结束日期 |

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "page": 1,
        "size": 10,
        "total": 30,
        "records": [
            {
                "id": 1,
                "taskName": "日常巡检计划-20250101-080000",
                "planId": 1,
                "planName": "日常巡检计划",
                "cycleType": "DAY",
                "scheduledStartTime": "2025-01-01T08:00:00",
                "scheduledEndTime": "2025-01-01T18:00:00",
                "actualStartTime": "2025-01-01T08:05:00",
                "actualEndTime": null,
                "status": "IN_PROGRESS",
                "responsibleUserId": 1,
                "responsibleUserName": "张三",
                "cameraCount": 10,
                "completedCount": 3,
                "createTime": "2025-01-01T08:00:00"
            }
        ]
    },
    "timestamp": "2025-01-01T10:00:00Z"
}
```

### 4.2 查询任务摄像头列表
**接口地址**：`GET /inspection/task/{id}/cameras`

**路径参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 任务ID |

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "taskId": 1,
            "cameraId": 1,
            "cameraName": "摄像头001",
            "cameraCode": "CAM001",
            "status": "COMPLETED",
            "sortOrder": 1,
            "cameraStatus": "ONLINE"
        },
        {
            "id": 2,
            "taskId": 1,
            "cameraId": 2,
            "cameraName": "摄像头002",
            "cameraCode": "CAM002",
            "status": "PENDING",
            "sortOrder": 2,
            "cameraStatus": "ONLINE"
        }
    ],
    "timestamp": "2025-01-01T10:00:00Z"
}
```

## 5. 巡检记录接口

### 5.1 提交巡检记录
**接口地址**：`POST /inspection/record`

**请求体**：
```json
{
    "taskId": 1,
    "cameraId": 1,
    "result": "NORMAL",
    "description": "设备运行正常",
    "imageUrls": [
        "/upload/images/20250101/camera1_001.jpg",
        "/upload/images/20250101/camera1_002.jpg"
    ],
    "longitude": 116.397128,
    "latitude": 39.916527
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "记录提交成功",
    "data": true,
    "timestamp": "2025-01-01T10:00:00Z"
}
```

### 5.2 分页查询巡检记录
**接口地址**：`GET /inspection/record/page`

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 页大小，默认10 |
| taskId | Long | 否 | 任务ID |
| cameraId | Long | 否 | 摄像头ID |
| result | String | 否 | 巡检结果：NORMAL/ABNORMAL |
| startTime | DateTime | 否 | 开始时间 |
| endTime | DateTime | 否 | 结束时间 |

## 6. 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 40001 | 参数校验失败 | 检查请求参数格式和必填项 |
| 40002 | 摄像头编码已存在 | 使用唯一的摄像头编码 |
| 40003 | 计划名称已存在 | 使用唯一的计划名称 |
| 40004 | 任务不存在 | 检查任务ID是否正确 |
| 40005 | 摄像头不在线 | 检查摄像头设备状态 |
| 50001 | 数据库连接失败 | 联系技术支持 |
| 50002 | 外部服务调用失败 | 检查外部服务状态 |

## 7. 接口调用示例

### 7.1 JavaScript示例
```javascript
// 获取摄像头列表
async function getCameraList(page = 1, size = 10) {
    const response = await fetch('/api/line/camera/page?page=' + page + '&size=' + size, {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
        }
    });
    
    const result = await response.json();
    if (result.code === 200) {
        return result.data;
    } else {
        throw new Error(result.message);
    }
}

// 创建巡检计划
async function createInspectionPlan(planData) {
    const response = await fetch('/api/line/inspection/plan', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(planData)
    });
    
    const result = await response.json();
    return result;
}
```

### 7.2 Java示例
```java
// 使用RestTemplate调用接口
@Service
public class LineApiClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public Page<CameraResponse> getCameraList(int page, int size) {
        String url = "/api/line/camera/page?page=" + page + "&size=" + size;
        
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        
        ResponseEntity<JsonResult> response = restTemplate.exchange(
            url, HttpMethod.GET, entity, JsonResult.class);
        
        return (Page<CameraResponse>) response.getBody().getData();
    }
}
```
