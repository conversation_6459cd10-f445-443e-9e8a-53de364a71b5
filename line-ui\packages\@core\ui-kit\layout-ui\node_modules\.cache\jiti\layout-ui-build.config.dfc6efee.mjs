"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0;var _unbuild = await jitiImport("unbuild");var _default = exports.default =

(0, _unbuild.defineBuildConfig)({
  clean: true,
  declaration: true,
  entries: [
  {
    builder: 'mkdist',
    input: './src',
    loaders: ['vue'],
    pattern: ['**/*.vue']
  },
  {
    builder: 'mkdist',
    format: 'esm',
    input: './src',
    loaders: ['js'],
    pattern: ['**/*.ts']
  }]

}); /* v9-39026ca3943658d8 */
