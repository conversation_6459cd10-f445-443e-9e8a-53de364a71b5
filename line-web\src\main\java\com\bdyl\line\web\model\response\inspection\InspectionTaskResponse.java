package com.bdyl.line.web.model.response.inspection;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 巡检任务响应对象
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class InspectionTaskResponse {
    /**
     * 任务ID
     */
    private Long id;

    /**
     * 计划ID
     */
    private Long planId;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 巡检周期类型
     */
    private String cycleType;

    /**
     * 周期值
     */
    private Integer cycleValue;
    /**
     * 计划开始时间
     */
    private LocalDateTime scheduledStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime scheduledEndTime;

    /**
     * 实际开始时间
     */
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    private LocalDateTime actualEndTime;

    /**
     * 巡检状态 {@link com.bdyl.line.common.constant.enums.InspectionStatusEnum}
     */
    private String status;

    /**
     * 巡检负责人ID
     */
    private Long responsibleUserId;

    /**
     * 巡检负责人姓名
     */
    private String responsibleUserName;

    /**
     * 摄像头总数
     */
    private Integer cameraCount;

    /**
     * 已完成巡检数量
     */
    private Integer completedCount;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
