---
description: 
globs: 
alwaysApply: true
---
# Java代码Javadoc规范

所有生成的Java代码必须包含标准的Javadoc注释，具体要求如下：

1. 类级别的Javadoc：
   - 必须包含类的功能描述
   - 必须包含@author标签 author xxx
   - 如果类有特殊用途，需要添加@see标签

2. 方法级别的Javadoc：
   - 必须包含方法的功能描述
   - 必须使用@param标签描述所有参数
   - 必须使用@return标签描述返回值
   - 如果方法可能抛出异常，必须使用@throws标签
   - 如果方法有特殊用途，需要添加@see标签

3. 字段级别的Javadoc：
   - 所有public和protected字段必须包含Javadoc
   - 必须包含字段的用途说明
   - 如果字段有特殊约束，需要说明

4. 格式要求：
   - 使用标准的Javadoc格式
   - 每个标签独占一行
   - 描述文字要清晰、准确
   - 使用完整的句子，以句号结尾

示例：
```java
/**
 * 用户服务类，提供用户相关的业务操作
 *
 * <AUTHOR>
 * @see User
 */
public class UserService {
    
    /**
     * 根据用户ID查询用户信息
     *
     * @param userId 用户ID
     * @return 用户信息对象
     * @throws UserNotFoundException 当用户不存在时抛出
     */
    public User getUserById(Long userId) {
        // 方法实现
    }
}
```
