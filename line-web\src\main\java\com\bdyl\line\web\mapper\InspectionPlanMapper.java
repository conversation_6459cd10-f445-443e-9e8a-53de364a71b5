package com.bdyl.line.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bdyl.line.web.entity.InspectionPlanEntity;

/**
 * 巡检计划Mapper接口，提供巡检计划相关的数据库操作。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Mapper
public interface InspectionPlanMapper extends BaseMapper<InspectionPlanEntity> {

    /**
     * 根据组织ID和计划名称查询计划
     *
     * @param organId 组织ID
     * @param name 计划名称
     * @param excludeId 排除的计划ID
     * @return 计划实体
     */
    InspectionPlanEntity selectByOrganIdAndName(@Param("organId") Long organId, @Param("name") String name,
        @Param("excludeId") Long excludeId);
}
