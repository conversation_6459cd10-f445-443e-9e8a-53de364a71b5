<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bdyl.line.web.mapper.InspectionTaskMapper">
    
    <!-- 查询超时未完成的任务 -->
    <select id="selectTimeoutTasks" resultType="com.bdyl.line.web.entity.InspectionTaskEntity">
        SELECT * FROM t_inspection_task 
        WHERE status IN ('PENDING', 'IN_PROGRESS')
        AND scheduled_time &lt; #{timeoutTime}
    </select>
    
    <!-- 批量更新任务状态为漏检 -->
    <update id="updateTasksToMissed">
        UPDATE t_inspection_task 
        SET status = 'MISSED', update_time = NOW()
        WHERE id IN 
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>
    
    <!-- 根据计划ID查询最新的任务 -->
    <select id="selectLatestByPlanId" resultType="com.bdyl.line.web.entity.InspectionTaskEntity">
        SELECT * FROM t_inspection_task
        WHERE plan_id = #{planId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 根据计划ID和时间范围查询任务 -->
    <select id="selectByPlanIdAndTimeRange" resultType="com.bdyl.line.web.entity.InspectionTaskEntity">
        SELECT * FROM t_inspection_task
        WHERE plan_id = #{planId}
        AND scheduled_start_time &gt;= #{startTime}
        AND scheduled_start_time &lt;= #{endTime}
        ORDER BY scheduled_start_time ASC
    </select>

    <!-- 查询需要检查漏检的任务 -->
    <select id="selectTasksForMissedCheck" resultType="com.bdyl.line.web.entity.InspectionTaskEntity">
        SELECT t1.* FROM t_inspection_task t1
        INNER JOIN t_inspection_plan p ON t1.plan_id = p.id
        WHERE t1.status IN ('PENDING', 'IN_PROGRESS')
        AND p.status = 'ENABLE'
    </select>

    <select id="countMonthlyMissedAndTotal" resultType="map">
        SELECT
            MONTH(create_time) AS month,
            SUM(CASE WHEN status = 'MISSED' THEN 1 ELSE 0 END) AS missed_count,
            COUNT(*) AS total_count
        FROM t_inspection_task
        WHERE YEAR(create_time) = #{year}
        GROUP BY MONTH(create_time)
        ORDER BY month
    </select>

</mapper>
