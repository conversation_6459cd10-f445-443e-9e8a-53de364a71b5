package com.bdyl.line.web.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import jakarta.servlet.http.HttpServletResponse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.bind.annotation.*;

import com.bdyl.line.web.model.response.permission.PermissionExportDTO;
import com.bdyl.line.web.utils.ExcelUtil;

/**
 * 权限控制器
 */
@Slf4j
@RestController
@RequestMapping("/permission")
@RequiredArgsConstructor
public class PermissionController {

    /**
     * spring上下文
     */
    private final ApplicationContext applicationContext;

    /**
     * 全局ID生成器
     */
    private long idGen = 3000000;

    /**
     * 导出权限数据
     *
     * @param response response
     * @throws IOException IOException
     */
    @GetMapping("/export")
    public void exportPermissions(HttpServletResponse response) throws IOException {
        // 每次导出前重置ID生成器
        idGen = 3000000;
        List<PermissionExportDTO> permissions = getAllPermissions();
        ExcelUtil.export(response, permissions, PermissionExportDTO.class, "权限数据");
    }

    private List<PermissionExportDTO> getAllPermissions() {
        Map<String, Object> controllers = applicationContext.getBeansWithAnnotation(RestController.class);
        List<PermissionExportDTO> permissions = new ArrayList<>();
        long parentId = 1L;
        for (Object bean : controllers.values()) {
            Class<?> clazz = getTargetClass(bean);
            String controllerName = getControllerName(clazz);
            long menuId = nextId();
            permissions.add(buildMenuPermission(menuId, controllerName, parentId));
            permissions.addAll(scanApiPermissions(clazz, controllerName, menuId));
        }
        return permissions;
    }

    private long nextId() {
        return idGen++;
    }

    private Class<?> getTargetClass(Object bean) {
        Class<?> clazz = bean.getClass();
        if (clazz.getName().contains("$$")) {
            clazz = clazz.getSuperclass();
        }
        return clazz;
    }

    private String getControllerName(Class<?> clazz) {
        return clazz.getSimpleName().replace("Controller", "");
    }

    private PermissionExportDTO buildMenuPermission(long menuId, String controllerName, long parentId) {
        PermissionExportDTO menuPerm = new PermissionExportDTO();
        menuPerm.setId(menuId);
        menuPerm.setName(controllerName + "管理");
        menuPerm.setCode(controllerName.toLowerCase() + ":menu");
        menuPerm.setType("MENU");
        menuPerm.setApiPath(null);
        menuPerm.setApiMethod(null);
        menuPerm.setParentId(parentId);
        menuPerm.setSortOrder(0);
        menuPerm.setStatus("ENABLED");
        menuPerm.setDescription(null);
        return menuPerm;
    }

    private List<PermissionExportDTO> scanApiPermissions(Class<?> clazz, String controllerName, long menuId) {
        List<PermissionExportDTO> apiPermissions = new ArrayList<>();
        RequestMapping classMapping = AnnotationUtils.findAnnotation(clazz, RequestMapping.class);
        String[] classPaths = classMapping != null ? classMapping.value() : new String[] {""};
        for (var method : clazz.getDeclaredMethods()) {
            Optional<PermissionExportDTO> apiPerm = buildApiPermission(method, classPaths, controllerName, menuId);
            apiPerm.ifPresent(apiPermissions::add);
        }
        return apiPermissions;
    }

    private Optional<PermissionExportDTO> buildApiPermission(java.lang.reflect.Method method, String[] classPaths,
        String controllerName, long menuId) {
        String apiPath = null;
        String apiMethod = null;
        String apiName = method.getName();
        if (method.isAnnotationPresent(GetMapping.class)) {
            GetMapping mapping = method.getAnnotation(GetMapping.class);
            apiPath = joinPath(classPaths, mapping.value());
            apiMethod = "GET";
        } else if (method.isAnnotationPresent(PostMapping.class)) {
            PostMapping mapping = method.getAnnotation(PostMapping.class);
            apiPath = joinPath(classPaths, mapping.value());
            apiMethod = "POST";
        } else if (method.isAnnotationPresent(PutMapping.class)) {
            PutMapping mapping = method.getAnnotation(PutMapping.class);
            apiPath = joinPath(classPaths, mapping.value());
            apiMethod = "PUT";
        } else if (method.isAnnotationPresent(DeleteMapping.class)) {
            DeleteMapping mapping = method.getAnnotation(DeleteMapping.class);
            apiPath = joinPath(classPaths, mapping.value());
            apiMethod = "DELETE";
        } else if (method.isAnnotationPresent(RequestMapping.class)) {
            RequestMapping mapping = method.getAnnotation(RequestMapping.class);
            apiPath = joinPath(classPaths, mapping.value());
            var methods = mapping.method();
            apiMethod = methods.length > 0 ? methods[0].name() : "GET";
        } else {
            return Optional.empty();
        }
        String apiCode = controllerName.toLowerCase() + ":api:" + apiMethod.toLowerCase() + ":"
            + apiPath.replaceAll("/", ":").replaceAll(":+", ":").replaceAll("^:|:$", "");
        PermissionExportDTO apiPerm = new PermissionExportDTO();
        apiPerm.setId(nextId());
        apiPerm.setName(apiName);
        apiPerm.setCode(apiCode);
        apiPerm.setType("API");
        apiPerm.setApiPath(apiPath);
        apiPerm.setApiMethod(apiMethod);
        apiPerm.setParentId(menuId);
        apiPerm.setSortOrder(0);
        apiPerm.setStatus("ENABLED");
        apiPerm.setDescription(null);
        return Optional.of(apiPerm);
    }

    private String joinPath(String[] classPaths, String[] methodPaths) {
        String classPath = (classPaths.length > 0) ? classPaths[0] : "";
        String methodPath = (methodPaths.length > 0) ? methodPaths[0] : "";
        if (!classPath.startsWith("/")) {
            classPath = "/" + classPath;
        }
        if (!methodPath.startsWith("/")) {
            methodPath = "/" + methodPath;
        }
        String path = (classPath + methodPath).replaceAll("//+", "/");
        String apiPath = path.replaceAll("/$", "");
        return "/api/line" + apiPath;
    }
}
