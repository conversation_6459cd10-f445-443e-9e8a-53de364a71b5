<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from 'vue';

import { Page } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import {
  getUsers_Api,
  updateUserStatus_Api,
  deleteUser_Api,
  getOrganTree_Api,
  exportUser_Api,
} from '#/api/core';
import tableComp from '#/components/TableComp/table.vue';
import BatchImport from '#/components/BatchImport';

import EditModal from './components/EditModal.vue';
import ResetPasswordModal from './components/ResetPasswordModal.vue';

import { downloadFileFromBlob, formatDate } from '@vben/utils';

// 组织树数据
const treeData = ref<any>([]);

// 查询条件
const queryParam = reactive<any>({
  name: undefined, // 姓名
  phone: undefined, // 手机号
  account: undefined, // 账号
  organId: undefined, // 组织id
  status: undefined, // 状态
  departmentId: undefined, // 部门ID
  createTime: [],
  page: 1,
  size: 10,
});

const loading = ref(false);
const modalFormRef = ref();
const resetPasswordModalRef = ref();
const selectedKeys = ref<string[]>([]);
const searchValue = ref<string>('');
const expandedKeys = ref<(string | number)[]>([]);
const importVisible = ref(false);

// 表格数据源
const tableData = reactive<any>({
  data: {},
});
// 表格相关配置
const columns = [
  { title: '姓名', dataIndex: 'name' },
  { title: '账号', dataIndex: 'account' },
  { title: '手机号', dataIndex: 'phone', width: '130px' },
  { title: '角色', dataIndex: 'roleIds' },
  { title: '所属组织', dataIndex: 'organName' },
  { title: '部门', dataIndex: 'departmentName' },
  { title: '状态', dataIndex: 'status', width: '100px' },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: '180px',
    customRender: ({ record }: { record: any }) => {
      return formatDate(record.createTime, 'YYYY-MM-DD HH:mm:ss');
    },
  },
  { title: '操作', dataIndex: 'operation', width: '250px' },
];

// 加载用户列表
const loadUserList = async () => {
  loading.value = true;
  try {
    queryParam.startTime = queryParam.createTime
      ? queryParam.createTime[0]?.format('YYYY-MM-DD HH:mm:ss')
      : undefined;
    queryParam.endTime = queryParam.createTime
      ? queryParam.createTime[1]?.format('YYYY-MM-DD HH:mm:ss')
      : undefined;
    // queryParam.departmentId = selectedKeys.value[0] || undefined;

    const res = await getUsers_Api(queryParam);

    // 更新表格数据源
    const temp = {
      data: res.data || res.records || [],
      page: res.page || res.current || 1,
      size: res.size || 10,
      total: res.total || 0,
    };
    tableData.data = temp;
  } catch (error) {
    console.error('获取用户列表失败', error);
    message.error('获取用户列表失败');
  } finally {
    loading.value = false;
  }
};

const success = (data: any) => {
  queryParam.page = data.pi;
  queryParam.size = data.ps;
  loadUserList();
};

// 查询
const searchTable = () => {
  queryParam.page = 1;
  loadUserList();
};

// 重置
const resetTable = () => {
  queryParam.name = undefined;
  queryParam.phone = undefined;
  queryParam.account = undefined;
  queryParam.organId = undefined;
  queryParam.status = undefined;
  queryParam.createTime = [];
  queryParam.page = 1;
  queryParam.size = 10;
  loadUserList();
};

// 添加用户
const handleAdd = () => {
  modalFormRef.value.openModal('create');
};

// 编辑用户
const handleEdit = (record: any) => {
  modalFormRef.value.openModal('update', record);
};

// 重置密码
const handleResetPassword = (record: any) => {
  resetPasswordModalRef.value.openModal(record);
};

// 删除用户
const handleDelete = async (record: any) => {
  try {
    await deleteUser_Api(record.id);
    message.success('操作成功');
    loadUserList();
  } catch (error) {
    console.error('删除用户失败', error);
  }
};

// 点击树获取用户数据
// const getUserByOrgan = (id: any) => {
//   queryParam.organId = id;
//   loadUserList();
// };

// 监听树选择变化
watch(selectedKeys, () => {
  queryParam.organId = selectedKeys.value[0] || undefined;
  if (Number(queryParam.organId)) {
    loadUserList(); // 当选择的部门变化时重新加载用户列表
  }
});

// 递归处理树形数据，设置id为null时使用name作为id
const processTreeData = (nodes: any[]): any[] => {
  if (!Array.isArray(nodes)) return [];

  return nodes.map((node) => {
    // 如果id为null，使用name作为id
    if (node.id === null || node.id === undefined) {
      node.id = node.name;
    }

    // 如果有子节点，递归处理
    if (node.children && Array.isArray(node.children)) {
      node.children = processTreeData(node.children);
    }

    return node;
  });
};

// 获取组织树
const getTreeData = async () => {
  const res = await getOrganTree_Api({});
  // 处理树形数据，确保所有节点都有有效的id
  const processedData = processTreeData([res]);
  treeData.value = processedData;
};

// 修改部门状态
const handleStatusChange = (record: any) => {
  const temp = {
    id: record.id,
    status: record.status,
  };

  updateUserStatus_Api(temp)
    .then(() => {
      message.success('操作成功');
      loadUserList();
    })
    .catch((error: any) => {
      console.error('更新部门状态失败', error);
      loadUserList(); // 刷新数据，恢复原状态
    });
};

// 用户保存成功回调
const handleSaveSuccess = () => {
  loadUserList();
};

const autoExpandParent = ref<boolean>(true);
const onExpand = (keys: string[]) => {
  expandedKeys.value = keys;
  autoExpandParent.value = false;
};

// 递归搜索树节点
const searchTreeNode = (
  nodes: any[],
  searchText: string,
  parentIds: string[] = [],
) => {
  for (const node of nodes) {
    if (node.name.includes(searchText)) {
      selectedKeys.value = [node.id];
      expandedKeys.value = [...parentIds];
      return true;
    }
    if (node.children && node.children.length) {
      const found = searchTreeNode(node.children, searchText, [
        ...parentIds,
        node.id,
      ]);
      if (found) return true;
    }
  }
  return false;
};

// 组织搜索
const handleSearch = () => {
  if (!searchValue.value.trim()) {
    selectedKeys.value = [];
    expandedKeys.value = [];
    return;
  }
  searchTreeNode(treeData.value, searchValue.value);
  autoExpandParent.value = true;
};

// 用户导出
const handleExport = async () => {
  const params = {
    name: queryParam.name,
    phone: queryParam.phone,
    account: queryParam.account,
    organId: queryParam.organId,
    status: queryParam.status,
    startTime: queryParam.createTime
      ? queryParam.createTime[0]?.format('YYYY-MM-DD HH:mm:ss')
      : undefined,
    endTime: queryParam.createTime
      ? queryParam.createTime[1]?.format('YYYY-MM-DD HH:mm:ss')
      : undefined,
  };
  const res = await exportUser_Api(params);
  downloadFileFromBlob({ source: res, fileName: '用户数据.xlsx' });
};

const handleImportSuccess = () => {
  console.log('上传成功');
  loadUserList();
};
const handleImportError = () => {
  console.log('上传失败');
};

// 获取弹窗带回来的角色数据
const roleOptions = ref<any>([]);
const handleGetRoleData = (options: any) => {
  roleOptions.value = options;
};

onMounted(() => {
  loadUserList();

  getTreeData(); // 获取组织树
});
</script>

<template>
  <Page>
    <div class="wrap_con">
      <div class="left_cont">
        <a-card>
          <a-input-search
            v-model:value="searchValue"
            placeholder="搜索区域名称"
            class="mb-2 mt-2"
            @search="handleSearch"
          />
          <a-tree
            v-model:selectedKeys="selectedKeys"
            :expanded-keys="expandedKeys"
            :tree-data="treeData"
            @expand="onExpand"
            :field-names="{ children: 'children', title: 'name', key: 'id' }"
          >
            <template #title="data">
              <div class="flex items-center gap-[5px] pl-[2px] pr-[5px]">
                <span :class="{ noOrgan: !data.hasOrgan }"
                  >{{ data.name }} ({{ data.userCount }})</span
                >
              </div>
            </template>
          </a-tree>
        </a-card>
      </div>
      <div class="right_cont">
        <a-card class="table_header_search mb-5">
          <a-row :gutter="20">
            <a-col :span="6">
              <label>姓名：</label>
              <div class="table_header_wrp_cont">
                <a-input
                  v-model:value="queryParam.name"
                  allow-clear
                  placeholder="请输入姓名"
                />
              </div>
            </a-col>
            <a-col :span="7">
              <label>手机号：</label>
              <div class="table_header_wrp_cont">
                <a-input
                  v-model:value="queryParam.phone"
                  allow-clear
                  placeholder="请输入手机号"
                />
              </div>
            </a-col>
            <a-col :span="6">
              <label>账号：</label>
              <div class="table_header_wrp_cont">
                <a-input
                  v-model:value="queryParam.account"
                  allow-clear
                  placeholder="请输入账号"
                />
              </div>
            </a-col>
            <a-col :span="8">
              <label>创建时间：</label>
              <div class="table_header_wrp_cont">
                <a-range-picker
                  v-model:value="queryParam.createTime"
                  :placeholder="['开始时间', '结束时间']"
                  format="YYYY-MM-DD HH:mm:ss"
                  show-time
                  style="width: 100%"
                />
              </div>
            </a-col>
            <a-col :span="6">
              <label>状态：</label>
              <div class="table_header_wrp_cont">
                <a-select
                  v-model:value="queryParam.status"
                  allow-clear
                  placeholder="请选择状态"
                  style="width: 100%"
                >
                  <a-select-option value="ENABLE">启用</a-select-option>
                  <a-select-option value="DISABLE">禁用</a-select-option>
                </a-select>
              </div>
            </a-col>

            <a-col :span="4">
              <a-space>
                <a-button type="primary" class="searchBtn" @click="searchTable">
                  查询
                </a-button>
                <a-button class="refBtn" @click="resetTable">重置</a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-card>

        <a-card size="small">
          <div class="table_action_btn_wrp">
            <a-button class="addBtn" type="primary" @click="handleAdd">
              新建
            </a-button>

            <a-button class="addBtn" @click="importVisible = true"
              >导入</a-button
            >

            <BatchImport
              v-model="importVisible"
              :upload-url="'/api/line/user/import'"
              :template-url="'/api/line/user/template'"
              title-prefix="用户"
              @success="handleImportSuccess"
              @error="handleImportError"
            />

            <a-button class="addBtn" @click="handleExport"> 导出 </a-button>
          </div>

          <table-Comp
            :columns="columns"
            :data-source="tableData.data"
            :loading="loading"
            :scroll="{ x: 1200 }"
            @is-loading-fuc="(e) => (loading = e)"
            @success="success"
          >
            <!-- 状态列 -->
            <template #status="{ record }">
              <a-switch
                v-model:checked="record.status"
                checkedValue="ENABLE"
                unCheckedValue="DISABLE"
                @change="handleStatusChange(record)"
              />
            </template>

            <!-- 角色列 -->
            <template #roleIds="{ record }">
              <span v-if="record.roleIds && record.roleIds.length">
                {{
                  record.roleIds
                    .map((id: any) => {
                      const role = roleOptions.find(
                        (item: any) => item.value === id,
                      );
                      return role ? role.label : '';
                    })
                    .join(',')
                }}
              </span>
            </template>

            <!-- 操作列 -->
            <template #operation="{ record }">
              <a-button type="link" @click="handleEdit(record)">编辑</a-button>
              <a-button type="link" @click="handleResetPassword(record)"
                >重置密码</a-button
              >
              <a-popconfirm
                title="数据删除后不可恢复，请确认后继续?"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" danger>删除</a-button>
              </a-popconfirm>
            </template>
          </table-Comp>
        </a-card>
      </div>
    </div>
    <!-- 用户弹窗 -->
    <EditModal
      ref="modalFormRef"
      @success="handleSaveSuccess"
      @getRoleData="handleGetRoleData"
    />

    <!-- 重置密码弹窗 -->
    <ResetPasswordModal
      ref="resetPasswordModalRef"
      @success="handleSaveSuccess"
    />
  </Page>
</template>
<style scoped lang="scss">
.wrap_con {
  position: relative;
}
.left_cont {
  width: 260px;
  max-height: 90vh;
  overflow: auto;
  :deep(.ant-card-body) {
    padding: 10px !important;
  }
}
.right_cont {
  width: calc(100% - 270px);
  max-height: 90vh;
  overflow-y: scroll;
  position: absolute;
  top: 0;
  right: 0;
}

.noOrgan {
  color: #928f8f;
}
</style>
