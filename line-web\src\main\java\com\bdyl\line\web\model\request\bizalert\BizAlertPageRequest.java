package com.bdyl.line.web.model.request.bizalert;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import com.bdyl.boot.data.query.PageRequest;

/**
 * 业务报警分页查询请求
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class BizAlertPageRequest extends PageRequest {
    /**
     * 报警类型 {@link com.bdyl.line.common.constant.enums.BizAlertTypeEnum}
     */
    private String type;

    /**
     * 区域
     */
    private String region;

    /**
     * 场景ID
     */
    private Long sceneId;

    /**
     * 核实状态 {@link com.bdyl.line.common.constant.enums.VerifyStatusEnum}
     */
    private String verifyStatus;

    /**
     * 摄像头名称
     */
    private String cameraName;

    /**
     * 报警开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 报警结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
