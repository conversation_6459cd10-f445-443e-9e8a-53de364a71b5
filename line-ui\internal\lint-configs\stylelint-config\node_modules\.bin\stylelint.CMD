@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\git\line-inspection\line-ui\node_modules\.pnpm\stylelint@16.19.1_typescript@5.8.3\node_modules\stylelint\bin\node_modules;D:\git\line-inspection\line-ui\node_modules\.pnpm\stylelint@16.19.1_typescript@5.8.3\node_modules\stylelint\node_modules;D:\git\line-inspection\line-ui\node_modules\.pnpm\stylelint@16.19.1_typescript@5.8.3\node_modules;D:\git\line-inspection\line-ui\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\git\line-inspection\line-ui\node_modules\.pnpm\stylelint@16.19.1_typescript@5.8.3\node_modules\stylelint\bin\node_modules;D:\git\line-inspection\line-ui\node_modules\.pnpm\stylelint@16.19.1_typescript@5.8.3\node_modules\stylelint\node_modules;D:\git\line-inspection\line-ui\node_modules\.pnpm\stylelint@16.19.1_typescript@5.8.3\node_modules;D:\git\line-inspection\line-ui\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\stylelint\bin\stylelint.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\stylelint\bin\stylelint.mjs" %*
)
