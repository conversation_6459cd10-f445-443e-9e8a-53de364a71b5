package com.bdyl.line.web.model.request.camera;

import java.time.LocalDateTime;

import jakarta.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 视频回放请求DTO - 获取m3u8文件
 */
@Data
public class GBPlaybackRequest {

    /**
     * 流ID
     */
    private String streamId;

    /**
     * 设备编码
     */
    @NotBlank(message = "设备编码不能为空")
    private String deviceId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
