# 线路巡检管理系统 - 开发文档

## 1. 开发环境搭建

### 1.1 环境要求

#### 1.1.1 基础环境
- **JDK**：OpenJDK 21 或 Oracle JDK 21
- **Node.js**：18.x 或更高版本
- **pnpm**：9.12.0 或更高版本
- **Maven**：3.8.x 或更高版本
- **Git**：2.x 或更高版本

#### 1.1.2 数据库环境
- **MySQL**：8.0 或更高版本
- **Redis**：6.x 或更高版本（可选）

#### 1.1.3 开发工具
- **IDE**：IntelliJ IDEA 2023.x 或 VS Code
- **数据库工具**：Navicat、DBeaver 或 MySQL Workbench
- **API测试工具**：Postman 或 Apifox

### 1.2 项目克隆和配置

#### 1.2.1 克隆项目
```bash
git clone <repository-url>
cd line-inspection
```

#### 1.2.2 数据库配置
```sql
-- 创建数据库
CREATE DATABASE line CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'line_user'@'localhost' IDENTIFIED BY 'line_password';
GRANT ALL PRIVILEGES ON line.* TO 'line_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 1.2.3 后端配置
```yaml
# application-dev.yml
spring:
  datasource:
    url: *****************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver

line:
  upload-dir: /Users/<USER>/srv/upload
  relative-upload-dir: /srv/upload
  uaa-url: http://127.0.0.1:8180/api/uaa
  platform-url: http://************:8180/api/iot
  gb-url: http://*************:8280/api/gb28181/v1
```

#### 1.2.4 前端配置
```bash
cd line-ui
pnpm install
```

### 1.3 启动项目

#### 1.3.1 启动后端
```bash
cd line-web
mvn spring-boot:run
```

#### 1.3.2 启动前端
```bash
cd line-ui
pnpm dev:antd
```

## 2. 项目结构说明

### 2.1 后端项目结构
```
line-web/
├── src/main/java/com/bdyl/line/web/
│   ├── controller/          # 控制器层
│   │   ├── CameraController.java
│   │   ├── InspectionPlanController.java
│   │   └── InspectionTaskController.java
│   ├── service/            # 服务层
│   │   ├── CameraService.java
│   │   ├── InspectionPlanService.java
│   │   └── InspectionTaskService.java
│   ├── mapper/             # 数据访问层
│   │   ├── CameraMapper.java
│   │   └── InspectionPlanMapper.java
│   ├── entity/             # 实体类
│   │   ├── CameraEntity.java
│   │   └── InspectionPlanEntity.java
│   ├── model/              # 数据传输对象
│   │   ├── request/        # 请求对象
│   │   └── response/       # 响应对象
│   ├── config/             # 配置类
│   ├── remote/             # 远程服务调用
│   ├── timer/              # 定时任务
│   └── utils/              # 工具类
└── src/main/resources/
    ├── application.yml     # 配置文件
    ├── mapper/             # MyBatis映射文件
    └── db/changelog/       # 数据库变更脚本
```

### 2.2 前端项目结构
```
line-ui/
├── apps/web-antd/          # 主应用
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── api/            # API接口
│   │   ├── components/     # 公共组件
│   │   ├── router/         # 路由配置
│   │   └── stores/         # 状态管理
│   └── package.json
├── packages/               # 公共包
└── internal/               # 内部工具
```

## 3. 开发规范

### 3.1 代码规范

#### 3.1.1 Java代码规范
- **命名规范**：使用驼峰命名法
- **注释规范**：类和方法必须有JavaDoc注释
- **异常处理**：统一使用BizException处理业务异常
- **日志规范**：使用SLF4J，合理设置日志级别

```java
/**
 * 摄像头服务接口
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface CameraService {
    /**
     * 创建摄像头
     *
     * @param request 摄像头请求对象
     * @param organId 组织ID
     * @return 是否创建成功
     */
    boolean createCamera(CameraRequest request, Long organId);
}
```

#### 3.1.2 TypeScript代码规范
- **命名规范**：使用驼峰命名法
- **类型定义**：严格使用TypeScript类型
- **组件规范**：使用Vue 3 Composition API
- **样式规范**：使用Tailwind CSS

```typescript
// API接口定义
export interface CameraApi {
  /**
   * 分页查询摄像头
   */
  pageCameras: (params: CameraPageRequest) => Promise<Page<CameraResponse>>;
  
  /**
   * 创建摄像头
   */
  createCamera: (data: CameraRequest) => Promise<boolean>;
}
```

### 3.2 数据库规范

#### 3.2.1 表命名规范
- **表名**：使用`t_`前缀，如`t_camera`
- **视图名**：使用`v_`前缀，如`v_camera_info`
- **字段名**：使用下划线分隔，如`create_time`
- **索引名**：使用`idx_`前缀，如`idx_camera_code`

#### 3.2.2 字段规范
- **主键**：统一使用`id BIGINT`
- **公共字段**：所有表包含`tenant_id`、`organ_id`、`creator_id`、`updater_id`、`create_time`、`update_time`
- **状态字段**：使用`VARCHAR(32)`存储枚举值
- **时间字段**：使用`DATETIME`类型

### 3.3 API接口规范

#### 3.3.1 URL规范
- **基础路径**：`/api/line`
- **资源路径**：使用复数名词，如`/cameras`
- **操作路径**：使用动词，如`/cameras/{id}/play`

#### 3.3.2 请求响应规范
```java
// 统一响应格式
@Data
public class JsonResult<T> {
    private Integer code;
    private String message;
    private T data;
    private Long timestamp;
}

// 分页响应格式
@Data
public class Page<T> {
    private Integer page;
    private Integer size;
    private Long total;
    private List<T> records;
}
```

## 4. 核心功能开发指南

### 4.1 新增实体类开发

#### 4.1.1 创建实体类
```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_example", autoResultMap = true)
public class ExampleEntity extends BaseEntity {
    /**
     * 名称
     */
    private String name;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * JSON字段示例
     */
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> itemIds;
}
```

#### 4.1.2 创建Mapper接口
```java
@Mapper
public interface ExampleMapper extends BaseMapper<ExampleEntity> {
    /**
     * 自定义查询方法
     */
    List<ExampleEntity> selectByCustomCondition(@Param("condition") String condition);
}
```

#### 4.1.3 创建Mapper XML
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bdyl.line.web.mapper.ExampleMapper">
    <select id="selectByCustomCondition" resultType="com.bdyl.line.web.entity.ExampleEntity">
        SELECT * FROM t_example WHERE condition = #{condition}
    </select>
</mapper>
```

### 4.2 服务层开发

#### 4.2.1 创建服务接口
```java
public interface ExampleService extends IService<ExampleEntity> {
    /**
     * 分页查询
     */
    Page<ExampleResponse> pageExamples(ExamplePageRequest request);
    
    /**
     * 创建
     */
    boolean createExample(ExampleRequest request);
}
```

#### 4.2.2 创建服务实现
```java
@Service
@RequiredArgsConstructor
public class ExampleServiceImpl extends ServiceImpl<ExampleMapper, ExampleEntity> 
    implements ExampleService {
    
    private final ExampleMapper exampleMapper;
    
    @Override
    public Page<ExampleResponse> pageExamples(ExamplePageRequest request) {
        // 构建查询条件
        LambdaQueryWrapper<ExampleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExampleEntity::getOrganId, CurrentUserBizHelper.getCurrentOrganId());
        
        // 分页查询
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ExampleEntity> pageRequest =
            new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(request.getPage(), request.getSize());
        
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ExampleEntity> pageResult =
            exampleMapper.selectPage(pageRequest, wrapper);
        
        // 转换响应对象
        List<ExampleResponse> resultList = pageResult.getRecords().stream()
            .map(this::convertToResponse)
            .toList();
        
        return Page.of(request.getPage(), request.getSize(), pageResult.getTotal(), 
            Collections.emptyList(), resultList);
    }
    
    private ExampleResponse convertToResponse(ExampleEntity entity) {
        ExampleResponse response = new ExampleResponse();
        BeanUtils.copyProperties(entity, response);
        return response;
    }
}
```

### 4.3 控制器开发

```java
@RestController
@RequestMapping("/example")
@RequiredArgsConstructor
@Validated
public class ExampleController {
    
    private final ExampleService exampleService;
    
    /**
     * 分页查询
     */
    @GetMapping("/page")
    public JsonResult<Page<ExampleResponse>> pageExamples(@Valid ExamplePageRequest request) {
        return JsonResult.success(exampleService.pageExamples(request));
    }
    
    /**
     * 创建
     */
    @PostMapping
    public JsonResult<Boolean> createExample(@Valid @RequestBody ExampleRequest request) {
        return JsonResult.success(exampleService.createExample(request));
    }
}
```

## 5. 测试指南

### 5.1 单元测试
```java
@ExtendWith(MockitoExtension.class)
class ExampleServiceImplTest {
    
    @Mock
    private ExampleMapper exampleMapper;
    
    @InjectMocks
    private ExampleServiceImpl exampleService;
    
    @Test
    void testCreateExample() {
        // Given
        ExampleRequest request = new ExampleRequest();
        request.setName("测试");
        
        // When
        boolean result = exampleService.createExample(request);
        
        // Then
        assertTrue(result);
    }
}
```

### 5.2 集成测试
```java
@SpringBootTest
@Transactional
class ExampleControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testCreateExample() {
        // Given
        ExampleRequest request = new ExampleRequest();
        request.setName("测试");
        
        // When
        ResponseEntity<JsonResult> response = restTemplate.postForEntity(
            "/api/line/example", request, JsonResult.class);
        
        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }
}
```

## 6. 部署指南

### 6.1 开发环境部署
```bash
# 后端启动
cd line-web
mvn clean spring-boot:run

# 前端启动
cd line-ui
pnpm dev:antd
```

### 6.2 生产环境部署
```bash
# 后端打包
cd line-web
mvn clean package -Pprod

# 前端打包
cd line-ui
pnpm build:antd

# Docker部署（可选）
docker build -t line-inspection:latest .
docker run -d -p 8080:8080 line-inspection:latest
```

## 7. 常见问题

### 7.1 开发环境问题
- **数据库连接失败**：检查数据库配置和网络连接
- **端口冲突**：修改application.yml中的端口配置
- **依赖下载失败**：配置Maven镜像源

### 7.2 代码问题
- **循环依赖**：检查Service之间的依赖关系
- **事务失效**：确保方法是public且被Spring管理
- **权限问题**：检查数据权限配置
