package com.bdyl.line.web.model.request.camera;

import java.time.LocalDateTime;

import jakarta.validation.constraints.NotBlank;

import lombok.Data;

import org.springframework.format.annotation.DateTimeFormat;

/**
 * 视频回放请求DTO - 获取m3u8文件
 */
@Data
public class GBPlayTSRequest {
    /**
     * 设备编码
     */
    @NotBlank(message = "设备编码不能为空")
    private String deviceCode;

    /**
     * 开始时间 格式：yyyy-MM-dd HH:mm:ss
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dateTime;

    /**
     * 前后时间段（单位/秒）
     */
    private Integer durationSecond;

    /**
     * 分段时长（单位/秒）
     */
    private Integer tsSegmentSecond;
}
