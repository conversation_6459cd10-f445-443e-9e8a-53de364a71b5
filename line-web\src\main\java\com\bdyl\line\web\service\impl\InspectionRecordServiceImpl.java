package com.bdyl.line.web.service.impl;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.bdyl.boot.data.query.Page;
import com.bdyl.boot.exception.BizException;
import com.bdyl.line.common.constant.enums.InspectionCycleEnum;
import com.bdyl.line.common.constant.enums.InspectionTaskCameraStatusEnum;
import com.bdyl.line.common.constant.error.LineError;
import com.bdyl.line.web.config.LineProperties;
import com.bdyl.line.web.entity.InspectionRecordEntity;
import com.bdyl.line.web.entity.InspectionTaskCameraEntity;
import com.bdyl.line.web.entity.InspectionTaskEntity;
import com.bdyl.line.web.mapper.InspectionRecordMapper;
import com.bdyl.line.web.mapper.InspectionTaskCameraMapper;
import com.bdyl.line.web.mapper.InspectionTaskMapper;
import com.bdyl.line.web.model.request.inspection.InspectionRecordPageRequest;
import com.bdyl.line.web.model.request.inspection.InspectionRecordRequest;
import com.bdyl.line.web.model.response.inspection.InspectionRecordExportDTO;
import com.bdyl.line.web.model.response.inspection.InspectionRecordResponse;
import com.bdyl.line.web.model.response.user.UserResponse;
import com.bdyl.line.web.service.InspectionRecordService;
import com.bdyl.line.web.service.InspectionTaskService;
import com.bdyl.line.web.utils.CurrentUserBizHelper;

/**
 * 巡检记录服务实现类
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionRecordServiceImpl extends ServiceImpl<InspectionRecordMapper, InspectionRecordEntity>
    implements InspectionRecordService {

    /**
     * 巡检记录Mapper
     */
    private final InspectionRecordMapper inspectionRecordMapper;
    /**
     * 巡检任务摄像头Mapper
     */
    private final InspectionTaskCameraMapper inspectionTaskCameraMapper;
    /**
     * 巡检任务Mapper
     */
    private final InspectionTaskMapper inspectionTaskMapper;
    /**
     * 巡检任务service
     */
    private final InspectionTaskService inspectionTaskService;
    /**
     * 系统配置
     */
    private final LineProperties lineProperties;

    /**
     * @param request 请求对象
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InspectionRecordResponse submitRecord(InspectionRecordRequest request) {
        // 验证任务摄像头关联是否存在
        InspectionTaskCameraEntity taskCamera = inspectionTaskCameraMapper.selectById(request.getTaskCameraId());
        if (taskCamera == null) {
            throw new BizException(LineError.INSPECTION_TASK_CAMERA_NOT_EXIST);
        }

        // 验证任务状态
        InspectionTaskEntity task = inspectionTaskMapper.selectById(taskCamera.getTaskId());
        if (task == null) {
            throw new BizException(LineError.INSPECTION_TASK_NOT_EXIST);
        }

        // 创建巡检记录
        InspectionRecordEntity record = new InspectionRecordEntity();
        record.setTenantId(taskCamera.getTenantId());
        record.setOrganId(taskCamera.getOrganId());
        record.setTaskId(taskCamera.getTaskId());
        record.setTaskCameraId(request.getTaskCameraId());
        record.setCameraId(taskCamera.getCameraId());
        record.setCameraName(taskCamera.getCameraName());
        record.setInspectionImage(request.getImage());
        record.setIsNormal(request.getIsNormal());
        record.setRemarks(request.getRemarks());
        record.setInspectionTime(LocalDateTime.now());

        record.setResponsibleUserId(task.getResponsibleUserId());
        record.setResponsibleUserName(task.getResponsibleUserName());
        record.setCycleType(task.getCycleType());

        UserResponse currentUserResponse = CurrentUserBizHelper.getCurrentUserResponse();
        if (currentUserResponse != null) {
            record.setInspectorId(currentUserResponse.getId());
            record.setInspectorName(currentUserResponse.getName());
        }
        save(record);

        // 更新任务摄像头状态
        taskCamera.setStatus(InspectionTaskCameraStatusEnum.COMPLETED.getValue());
        inspectionTaskCameraMapper.updateById(taskCamera);

        // 更新任务进度
        inspectionTaskService.updateTaskProgress(taskCamera.getTaskId());

        return convertToResponse(record);
    }

    @Override
    public Page<InspectionRecordResponse> pageRecords(InspectionRecordPageRequest request) {
        LambdaQueryWrapper<InspectionRecordEntity> wrapper = buildConditions(request);
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<InspectionRecordEntity> pageRequest =
            new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(request.getPage(), request.getSize());

        com.baomidou.mybatisplus.extension.plugins.pagination.Page<InspectionRecordEntity> pageResult =
            inspectionRecordMapper.selectPage(pageRequest, wrapper);

        List<InspectionRecordResponse> resultList =
            pageResult.getRecords().stream().map(this::convertToResponse).toList();

        return Page.of(request.getPage(), request.getSize(), pageResult.getTotal(), Collections.emptyList(),
            resultList);

    }

    private LambdaQueryWrapper<InspectionRecordEntity> buildConditions(InspectionRecordPageRequest request) {
        LambdaQueryWrapper<InspectionRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspectionRecordEntity::getOrganId, CurrentUserBizHelper.getCurrentOrganId());

        if (request.getTaskId() != null) {
            wrapper.eq(InspectionRecordEntity::getTaskId, request.getTaskId());
        }
        if (request.getCameraId() != null) {
            wrapper.eq(InspectionRecordEntity::getCameraId, request.getCameraId());
        }
        if (request.getCameraName() != null) {
            wrapper.eq(InspectionRecordEntity::getCameraName, request.getCameraName());
        }
        if (request.getIsNormal() != null) {
            wrapper.eq(InspectionRecordEntity::getIsNormal, request.getIsNormal());
        }
        if (request.getInspectorId() != null) {
            wrapper.eq(InspectionRecordEntity::getInspectorId, request.getInspectorId());
        }
        if (request.getStartDate() != null) {
            wrapper.ge(InspectionRecordEntity::getInspectionTime, request.getStartDate().atStartOfDay());
        }
        if (request.getEndDate() != null) {
            wrapper.le(InspectionRecordEntity::getInspectionTime, request.getEndDate().atTime(23, 59, 59));
        }
        if (request.getResponsibleUserId() != null) {
            wrapper.eq(InspectionRecordEntity::getResponsibleUserId, request.getResponsibleUserId());
        }
        if (request.getCycleType() != null) {
            wrapper.eq(InspectionRecordEntity::getCycleType, request.getCycleType());
        }

        wrapper.orderByDesc(InspectionRecordEntity::getInspectionTime);

        return wrapper;
    }

    @Override
    public InspectionRecordResponse getTaskRecordByTaskCameraId(Long taskCameraId) {

        InspectionRecordEntity result = inspectionRecordMapper.selectByTaskCameraId(taskCameraId);
        return convertToResponse(result);
    }

    @Override
    public List<InspectionRecordExportDTO> export(InspectionRecordPageRequest request) {

        LambdaQueryWrapper<InspectionRecordEntity> wrapper = buildConditions(request);
        List<InspectionRecordEntity> records = inspectionRecordMapper.selectList(wrapper);

        return records.stream().map(item -> {
            InspectionRecordExportDTO exportDTO = new InspectionRecordExportDTO();
            exportDTO.setCameraName(item.getCameraName());
            if (StringUtils.isNotBlank(item.getInspectionImage())) {
                String domain = lineProperties.getDomain();
                exportDTO.setInspectionImage(domain + item.getInspectionImage());
            }
            exportDTO.setIsNormal(item.getIsNormal() ? "正常" : "异常");
            exportDTO.setRemarks(item.getRemarks());
            exportDTO.setInspectionTime(item.getInspectionTime());
            exportDTO.setInspectorName(item.getInspectorName());

            // 获取该记录属于哪个任务
            InspectionTaskEntity task = inspectionTaskMapper.selectById(item.getTaskId());
            if (task != null) {
                exportDTO.setTaskName(task.getTaskName());
                exportDTO.setInspectionCycle(InspectionCycleEnum.fromValue(task.getCycleType()).getName());
                exportDTO.setScheduledStartTime(task.getScheduledStartTime());
                exportDTO.setScheduledEndTime(task.getScheduledEndTime());
            }

            return exportDTO;
        }).toList();
    }

    private InspectionRecordResponse convertToResponse(InspectionRecordEntity entity) {
        InspectionRecordResponse response = new InspectionRecordResponse();
        if (entity == null) {
            return response;
        }
        BeanUtils.copyProperties(entity, response);

        // 查询任务名称
        if (entity.getTaskId() != null) {
            InspectionTaskEntity task = inspectionTaskMapper.selectById(entity.getTaskId());
            if (task != null) {
                response.setTaskName(task.getTaskName());
                response.setInspectionCycle(task.getCycleType());
                response.setScheduledStartTime(task.getScheduledStartTime());
                response.setScheduledEndTime(task.getScheduledEndTime());
            }
        }

        return response;
    }
}
