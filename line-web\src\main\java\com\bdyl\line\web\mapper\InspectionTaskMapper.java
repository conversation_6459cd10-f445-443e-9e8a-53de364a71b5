package com.bdyl.line.web.mapper;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bdyl.line.web.entity.InspectionTaskEntity;

/**
 * 巡检任务Mapper接口，提供巡检任务相关的数据库操作。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Mapper
public interface InspectionTaskMapper extends BaseMapper<InspectionTaskEntity> {

    /**
     * 查询超时未完成的任务
     *
     * @param timeoutTime 超时时间
     * @return 任务列表
     */
    List<InspectionTaskEntity> selectTimeoutTasks(@Param("timeoutTime") LocalDateTime timeoutTime);

    /**
     * 批量更新任务状态为漏检
     *
     * @param taskIds 任务ID列表
     * @return 更新数量
     */
    int updateTasksToMissed(@Param("taskIds") List<Long> taskIds);

    /**
     * 根据计划ID查询最新的任务
     *
     * @param planId 计划ID
     * @return 任务实体
     */
    InspectionTaskEntity selectLatestByPlanId(@Param("planId") Long planId);

    /**
     * 根据计划ID和时间范围查询任务
     *
     * @param planId 计划ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 任务列表
     */
    List<InspectionTaskEntity> selectByPlanIdAndTimeRange(@Param("planId") Long planId,
        @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询需要检查漏检的任务 状态为PENDING或IN_PROGRESS，且下一个周期的任务已经生成
     *
     * @return 任务列表
     */
    List<InspectionTaskEntity> selectTasksForMissedCheck();

    /**
     * 统计某年每月漏检和总任务数
     *
     * @param year 年份
     * @return 每月漏检数和总数
     */
    List<java.util.Map<String, Object>> countMonthlyMissedAndTotal(@Param("year") int year);
}
