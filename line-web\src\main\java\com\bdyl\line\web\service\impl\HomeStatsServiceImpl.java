package com.bdyl.line.web.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import com.bdyl.line.common.constant.enums.CameraStatusEnum;
import com.bdyl.line.web.entity.CameraEntity;
import com.bdyl.line.web.entity.SceneEntity;
import com.bdyl.line.web.mapper.*;
import com.bdyl.line.web.model.response.home.*;
import com.bdyl.line.web.service.HomeStatsService;
import com.bdyl.line.web.service.SceneService;

/**
 * 首页统计服务实现类，实现首页统计相关方法。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
@RequiredArgsConstructor
public class HomeStatsServiceImpl implements HomeStatsService {
    /**
     * 终端mapper
     */
    private final TerminalMapper terminalMapper;

    /**
     * 摄像头mapper
     */
    private final CameraMapper cameraMapper;
    /**
     * 业务报警mapper
     */
    private final BizAlertMapper bizAlertMapper;
    /**
     * 物联报警mapper
     */
    private final IotAlertMapper iotAlertMapper;
    /**
     * 场景服务
     */
    private final SceneService sceneService;
    /**
     * 巡检任务mapper
     */
    private final InspectionTaskMapper inspectionTaskMapper;

    @Override
    public HomeDeviceStatsResponse getDeviceStats() {
        // 设备总数、在线数、离线数、在线率
        long total = cameraMapper.selectCount(null);
        long online = cameraMapper.selectCount(
            new LambdaQueryWrapper<CameraEntity>().eq(CameraEntity::getStatus, CameraStatusEnum.ONLINE.getValue()));
        long offline = total - online;
        double onlineRate = total == 0 ? 0.0 : (online * 100.0 / total);
        HomeDeviceStatsResponse resp = new HomeDeviceStatsResponse();
        resp.setTotal((int) total);
        resp.setOnline((int) online);
        resp.setOffline((int) offline);
        resp.setOnlineRate(onlineRate);
        return resp;
    }

    @Override
    public HomeBizAlertStatsResponse getBizAlertStats() {
        int total = bizAlertMapper.countAll();
        List<Map<String, Object>> typeList = bizAlertMapper.countByType();
        Map<String, Integer> typeCountMap = typeList.stream()
            .collect(Collectors.toMap(m -> Objects.toString(m.get("type")), m -> ((Number) m.get("count")).intValue()));
        HomeBizAlertStatsResponse resp = new HomeBizAlertStatsResponse();
        resp.setTotal(total);
        resp.setTypeCountMap(typeCountMap);
        return resp;
    }

    @Override
    public HomeIotAlertStatsResponse getIotAlertStats() {
        int total = iotAlertMapper.countAll();
        List<Map<String, Object>> typeList = iotAlertMapper.countByType();
        Map<String, Integer> typeCountMap = typeList.stream()
            .collect(Collectors.toMap(m -> Objects.toString(m.get("type")), m -> ((Number) m.get("count")).intValue()));
        HomeIotAlertStatsResponse resp = new HomeIotAlertStatsResponse();
        resp.setTotal(total);
        resp.setTypeCountMap(typeCountMap);
        return resp;
    }

    @Override
    public List<HomeBizAlertMonthTypeCountResponse> getBizAlertMonthTypeStats(int year) {
        List<Map<String, Object>> rows = bizAlertMapper.countByMonthAndType(year);
        // month -> (type -> count)
        Map<Integer, List<HomeBizAlertMonthTypeCountResponse.AlertTypeCount>> monthTypeMap = new HashMap<>();
        for (Map<String, Object> row : rows) {
            Integer month = ((Number) row.get("month")).intValue();
            String type = Objects.toString(row.get("type"));
            Integer count = ((Number) row.get("count")).intValue();
            HomeBizAlertMonthTypeCountResponse.AlertTypeCount typeCount =
                new HomeBizAlertMonthTypeCountResponse.AlertTypeCount();
            typeCount.setType(type);
            typeCount.setCount(count);
            monthTypeMap.computeIfAbsent(month, k -> new ArrayList<>()).add(typeCount);
        }
        List<HomeBizAlertMonthTypeCountResponse> result = new ArrayList<>();
        for (int m = 1; m <= 12; m++) {
            HomeBizAlertMonthTypeCountResponse resp = new HomeBizAlertMonthTypeCountResponse();
            resp.setMonth(m);
            resp.setTypeCounts(monthTypeMap.getOrDefault(m, new ArrayList<>()));
            result.add(resp);
        }
        return result;
    }

    @Override
    public HomeBizAlertSceneStatsResponse getBizAlertSceneStats(int year) {

        // 查询系统中所有的场景
        List<SceneEntity> allScenes = sceneService.list();
        // 查询所有报警的scene_ids
        List<String> alertSceneIdsList = bizAlertMapper.selectSceneIdsByYear(year);
        Map<Long, Integer> sceneCountMap = new HashMap<>();
        ObjectMapper objectMapper = new ObjectMapper();
        for (String sceneIdsStr : alertSceneIdsList) {
            if (sceneIdsStr == null || sceneIdsStr.isBlank()) {
                continue;
            }
            try {
                List<Long> sceneIds = objectMapper.readValue(sceneIdsStr, new TypeReference<List<Long>>() {});
                for (Long sceneId : sceneIds) {
                    sceneCountMap.merge(sceneId, 1, Integer::sum);
                }
            } catch (Exception e) {
                // 可加日志
            }
        }

        // 组装结果
        Map<String, Integer> typeCountMap = new HashMap<>();
        int total = 0;

        for (SceneEntity scene : allScenes) {
            Integer count = sceneCountMap.getOrDefault(scene.getId(), 0);
            String sceneName = scene.getName();
            typeCountMap.put(sceneName, count);
            total += count;
        }
        HomeBizAlertSceneStatsResponse resp = new HomeBizAlertSceneStatsResponse();
        resp.setTotal(total);
        resp.setTypeCountMap(typeCountMap);
        return resp;
    }

    @Override
    public List<MissInspectionMonthTypeCountResponse> getMissInspectionStats(int year) {
        List<Map<String, Object>> rows = inspectionTaskMapper.countMonthlyMissedAndTotal(year);
        List<MissInspectionMonthTypeCountResponse> result = new ArrayList<>();
        for (Map<String, Object> row : rows) {
            Integer month = ((Number) row.get("month")).intValue();
            Integer missedCount = ((Number) row.get("missed_count")).intValue();
            Integer totalCount = ((Number) row.get("total_count")).intValue();
            double percent = totalCount == 0 ? 0.0 : (missedCount * 100.0 / totalCount);
            MissInspectionMonthTypeCountResponse resp = new MissInspectionMonthTypeCountResponse();
            resp.setMonth(month);
            resp.setMissedCount(missedCount);
            resp.setTotalCount(totalCount);
            resp.setMissedPercent(percent);
            result.add(resp);
        }
        // 补全没有任务的月份
        for (int m = 1; m <= 12; m++) {
            final int monthValue = m;
            boolean exists = result.stream().anyMatch(r -> r.getMonth() == monthValue);
            if (!exists) {
                MissInspectionMonthTypeCountResponse resp = new MissInspectionMonthTypeCountResponse();
                resp.setMonth(monthValue);
                resp.setMissedCount(0);
                resp.setTotalCount(0);
                resp.setMissedPercent(0.0);
                result.add(resp);
            }
        }
        // 按月份排序
        result.sort(Comparator.comparingInt(MissInspectionMonthTypeCountResponse::getMonth));
        return result;
    }
}
