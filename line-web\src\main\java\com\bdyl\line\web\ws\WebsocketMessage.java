package com.bdyl.line.web.ws;

import java.util.List;

import lombok.Data;

import com.bdyl.line.web.model.response.terminal.DataProperty;

/**
 * 给websocket发送的实体类
 */
@Data
public class WebsocketMessage {

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 时间戳(毫秒级)
     */
    private Long timestamp;

    /**
     * 消息内容
     */
    private List<DataProperty> message;

    /**
     * 消息类型枚举
     */
    public enum MessageType {
        /**
         * 算法识别的框
         */
        RECOGNIZE_BOX,
        /**
         * iot设备的数据
         */
        DEVICE_DATA

    }
}
