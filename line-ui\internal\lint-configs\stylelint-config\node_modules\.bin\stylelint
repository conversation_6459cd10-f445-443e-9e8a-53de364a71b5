#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/git/line-inspection/line-ui/node_modules/.pnpm/stylelint@16.19.1_typescript@5.8.3/node_modules/stylelint/bin/node_modules:/mnt/d/git/line-inspection/line-ui/node_modules/.pnpm/stylelint@16.19.1_typescript@5.8.3/node_modules/stylelint/node_modules:/mnt/d/git/line-inspection/line-ui/node_modules/.pnpm/stylelint@16.19.1_typescript@5.8.3/node_modules:/mnt/d/git/line-inspection/line-ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/git/line-inspection/line-ui/node_modules/.pnpm/stylelint@16.19.1_typescript@5.8.3/node_modules/stylelint/bin/node_modules:/mnt/d/git/line-inspection/line-ui/node_modules/.pnpm/stylelint@16.19.1_typescript@5.8.3/node_modules/stylelint/node_modules:/mnt/d/git/line-inspection/line-ui/node_modules/.pnpm/stylelint@16.19.1_typescript@5.8.3/node_modules:/mnt/d/git/line-inspection/line-ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../stylelint/bin/stylelint.mjs" "$@"
else
  exec node  "$basedir/../stylelint/bin/stylelint.mjs" "$@"
fi
