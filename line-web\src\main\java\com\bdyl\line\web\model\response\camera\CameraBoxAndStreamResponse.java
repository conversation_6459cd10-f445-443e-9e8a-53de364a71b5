package com.bdyl.line.web.model.response.camera;

import java.util.List;

import lombok.Data;

import com.bdyl.line.web.model.dto.PointDTO;

/**
 * 摄像头响应对象，用于摄像头相关接口的返回。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class CameraBoxAndStreamResponse {
    /**
     * 摄像头ID
     */
    private Long id;
    /**
     * 摄像头名称
     */
    private String name;
    /**
     * 摄像头编码
     */
    private String code;

    /**
     * 摄像头流地址
     */
    private List<String> streamUrls;

    /**
     * 摄像头框
     */
    private List<CameraRegion> cameraRegions;

    /**
     * 电量
     */
    private Integer batteryLevel;

    /**
     * 坐标点对象
     */
    @Data
    public static class CameraRegion {

        /**
         * 算法模型编码
         */
        private String modelCode;
        /**
         * 点集
         */
        List<PointDTO> points;
    }

}
