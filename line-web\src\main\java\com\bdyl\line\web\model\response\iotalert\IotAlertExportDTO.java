package com.bdyl.line.web.model.response.iotalert;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 物联设备报警响应对象，用于物联设备报警相关接口的返回。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class IotAlertExportDTO {
    /**
     * 报警类型 {@link com.bdyl.line.common.constant.enums.IotAlertTypeEnum}
     */
    @ExcelProperty("报警类型")
    private String type;
    /**
     * 报警内容
     */
    @ExcelProperty("报警内容")
    private String content;
    /**
     * 终端名称
     */
    @ExcelProperty("终端名称")
    private String terminalName;
    /**
     * 区域
     */
    @ExcelProperty("区域")
    private String region;
    /**
     * 地址
     */
    @ExcelProperty("地址")
    private String address;
    /**
     * 状态 {@link com.bdyl.line.common.constant.enums.IotAlertTypeEnum}
     */
    @ExcelProperty("状态")
    private String status;
    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
    /**
     * 组织ID
     */
    @ExcelProperty("组织ID")
    private Long organId;
}
