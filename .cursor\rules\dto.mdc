---
description: Data transfer object rules
globs: **/model/**/*.java,,src/**/model/*.java
alwaysApply: false

---

Data Transfer Object(DTO)
- The "response" folder contains the classes returned to the front end for display.
- The "request" folder contains the classes passed from the front end to the back end.
- The entity queried by the back end needs to be converted into a response and then sent to the front end

