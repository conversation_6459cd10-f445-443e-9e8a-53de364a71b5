<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from 'vue';

import { Page } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { message } from 'ant-design-vue';

import {
  getCameraList_Api,
  getCameraCountByOrganNew_Api,
} from '#/api/core/camera';
import { getSceneCameraCount_Api } from '#/api/core/scene';
import tableComp from '#/components/TableComp/table.vue';

import VideoEditModal from './components/VideoEditModal.vue';

import { formatDate } from '@vben/utils';

import { useDictStore } from '#/store';

const dictStore = useDictStore();

// 树数据
const treeData = ref<any[]>([]);
const treeLoading = ref(false);
// 树类型
const treeType = ref<string>('organ');

// 查询条件
const queryParam = reactive({
  name: undefined as string | undefined, // 视频服务名称
  code: undefined as string | undefined, // 设备编码
  organId: undefined as string | undefined, // 组织id
  sceneId: undefined as string | undefined, // 场景id
  page: 1,
  size: 10,
});

const loading = ref(false);
const modalFormRef = ref();

// 表格数据源
const tableData = reactive<any>({
  data: {},
});

// 搜索相关
const searchValue = ref<string>('');
const selectedKeys = ref<string[]>([]);
const expandedKeys = ref<(string | number)[]>([]);
const autoExpandParent = ref<boolean>(true);

// 表格相关配置
const columns = [
  { title: '视频服务名称', dataIndex: 'name' },
  { title: '设备编码', dataIndex: 'code' },
  { title: '位置', dataIndex: 'location' },
  { title: '识别分析', dataIndex: 'modelCodes' },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: '180px',
    customRender: ({ record }: { record: any }) => {
      return formatDate(record.createTime, 'YYYY-MM-DD HH:mm:ss');
    },
  },
  { title: '操作', dataIndex: 'operation', width: '100px' },
];

// 获取组织摄像头树数据
const fetchOrganTreeData = async () => {
  try {
    treeLoading.value = true;
    const response = await getCameraCountByOrganNew_Api();

    // 转换数据格式
    const convertedData = convertOrganToTreeData(response);
    treeData.value = convertedData;
  } catch (error) {
    console.error('获取组织树数据失败:', error);
    treeData.value = [];
  } finally {
    treeLoading.value = false;
  }
};

// 获取场景摄像头树数据
const fetchSceneTreeData = async () => {
  try {
    treeLoading.value = true;
    const response = await getSceneCameraCount_Api();

    // 转换数据格式
    const convertedData = convertSceneToTreeData(response);
    treeData.value = convertedData;
  } catch (error) {
    console.error('获取场景树数据失败:', error);
    treeData.value = [];
  } finally {
    treeLoading.value = false;
  }
};

// 根据树类型获取数据
const fetchTreeData = async () => {
  if (treeType.value === 'organ') {
    await fetchOrganTreeData();
  } else if (treeType.value === 'scene') {
    await fetchSceneTreeData();
  }
};

// 转换组织数据格式为Tree组件需要的格式
const convertOrganToTreeData = (data: any): any[] => {
  if (!data) return [];

  // 如果是单个对象，转换为数组
  const dataArray = Array.isArray(data) ? data : [data];

  return dataArray.map((item: any) => convertOrganNodeToTreeNode(item));
};

// 转换组织单个节点
const convertOrganNodeToTreeNode = (node: any): any => {
  const treeNode: any = {
    id: node.organId || node.organName,
    key: node.organId || node.organName,
    title: `${node.organName} (${node.cameraCount})`,
    organName: node.organName,
    organId: node.organId,
    cameraCount: node.cameraCount,
    cameraList: node.cameraList || [],
    isOrgan: true,
    children: [],
  };

  // 递归处理子组织
  if (node.children && node.children.length > 0) {
    const childNodes = node.children.map((child: any) =>
      convertOrganNodeToTreeNode(child),
    );
    treeNode.children.push(...childNodes);
  }

  return treeNode;
};

// 转换场景数据格式为Tree组件需要的格式
const convertSceneToTreeData = (data: any): any[] => {
  if (!data) return [];

  // 如果是单个对象，转换为数组
  const dataArray = Array.isArray(data) ? data : [data];

  return dataArray.map((item: any) => convertSceneNodeToTreeNode(item));
};

// 转换场景单个节点
const convertSceneNodeToTreeNode = (scene: any): any => {
  const treeNode: any = {
    id: scene.sceneId,
    key: scene.sceneId,
    title: `${scene.sceneName} (${scene.cameraCount})`,
    sceneName: scene.sceneName,
    sceneId: scene.sceneId,
    sceneDesc: scene.sceneDesc,
    cameraCount: scene.cameraCount,
    cameraList: scene.cameraList || [],
    isScene: true,
    children: [],
  };

  return treeNode;
};

// 加载摄像头列表
const loadCameraList = async () => {
  loading.value = true;
  try {
    const res = await getCameraList_Api(queryParam);
    // 更新表格数据源
    const temp = {
      data: res?.data || [],
      page: res?.page || 1,
      size: res?.size || 10,
      total: res?.total || 0,
    };
    tableData.data = temp;
  } catch (error) {
    console.error('获取摄像头列表失败', error);
    message.error('获取摄像头列表失败');
  } finally {
    loading.value = false;
  }
};

const success = (data: any) => {
  queryParam.page = data.pi;
  queryParam.size = data.ps;
  loadCameraList();
};

// 查询
const searchTable = () => {
  queryParam.page = 1;
  loadCameraList();
};

// 重置
const resetTable = () => {
  queryParam.name = undefined;
  queryParam.code = undefined;
  queryParam.page = 1;
  loadCameraList();
};

// 编辑摄像头
const handleEdit = (record: any) => {
  modalFormRef.value.openModal(record);
};

// 摄像头保存成功回调
const handleSaveSuccess = () => {
  loadCameraList();
};

// 监听树选择变化
watch(selectedKeys, () => {
  if (treeType.value === 'organ') {
    queryParam.organId = selectedKeys.value[0] || undefined;
    queryParam.sceneId = undefined;
  } else if (treeType.value === 'scene') {
    queryParam.sceneId = selectedKeys.value[0] || undefined;
    queryParam.organId = undefined;
  }

  if (selectedKeys.value.length > 0) {
    loadCameraList(); // 当选择的组织或场景变化时重新加载摄像头列表
  }
});

// 监听树类型变化，重新获取数据
watch(treeType, () => {
  selectedKeys.value = [];
  queryParam.organId = undefined;
  queryParam.sceneId = undefined;
  fetchTreeData();
  loadCameraList();
});

const onExpand = (keys: string[]) => {
  expandedKeys.value = keys;
  autoExpandParent.value = false;
};

// 递归搜索树节点
const searchTreeNode = (
  nodes: any[],
  searchText: string,
  parentIds: string[] = [],
) => {
  for (const node of nodes) {
    const nodeName =
      node.organName || node.sceneName || node.name || node.title;
    if (nodeName && nodeName.includes(searchText)) {
      selectedKeys.value = [node.id];
      expandedKeys.value = [...parentIds];
      return true;
    }
    if (node.children && node.children.length) {
      const found = searchTreeNode(node.children, searchText, [
        ...parentIds,
        node.id,
      ]);
      if (found) return true;
    }
  }
  return false;
};

// 组织/场景搜索
const handleSearch = () => {
  if (!searchValue.value.trim()) {
    selectedKeys.value = [];
    expandedKeys.value = [];
    return;
  }
  searchTreeNode(treeData.value, searchValue.value);
  autoExpandParent.value = true;
};

// 初始化加载数据
onMounted(() => {
  fetchTreeData();
  loadCameraList();
});
</script>

<template>
  <Page>
    <div class="wrap_con">
      <div class="left_cont">
        <a-card>
          <a-radio-group
            v-model:value="treeType"
            button-style="solid"
            class="mb-2"
          >
            <a-radio-button value="organ">按组织</a-radio-button>
            <a-radio-button value="scene">按场景</a-radio-button>
          </a-radio-group>

          <a-input-search
            v-model:value="searchValue"
            placeholder="搜索区域名称"
            class="mb-2 mt-2"
            @search="handleSearch"
          />

          <a-spin :spinning="treeLoading">
            <a-tree
              v-model:selectedKeys="selectedKeys"
              :expanded-keys="expandedKeys"
              :tree-data="treeData"
              :auto-expand-parent="autoExpandParent"
              @expand="onExpand"
              :field-names="{
                children: 'children',
                title: 'title',
                key: 'key',
              }"
            >
              <template
                #title="{
                  organName,
                  sceneName,
                  organId,
                  cameraCount,
                  isOrgan,
                  isScene,
                }"
              >
                <div class="flex items-center gap-[5px] pl-[2px] pr-[5px]">
                  <!-- 组织图标 -->
                  <IconifyIcon
                    v-if="isOrgan && organId"
                    icon="mdi:office-building"
                    class="text-[18px] text-blue-500"
                  />
                  <!-- 场景图标 -->
                  <IconifyIcon
                    v-else-if="isScene"
                    icon="mdi:view-dashboard"
                    class="text-[18px] text-purple-500"
                  />
                  <a-tooltip
                    :title="`${organName || sceneName} (${cameraCount})`"
                    v-if="(organName || sceneName).length > 10"
                  >
                    <span :class="{ noOrgan: isOrgan && !organId }">
                      {{ (organName || sceneName).substring(0, 10) }} ({{
                        cameraCount
                      }})
                    </span>
                  </a-tooltip>
                  <span v-else :class="{ noOrgan: isOrgan && !organId }">
                    {{ organName || sceneName }} ({{ cameraCount }})
                  </span>
                </div>
              </template>
            </a-tree>
          </a-spin>
        </a-card>
      </div>

      <div class="right_cont">
        <a-card class="table_header_search mb-5">
          <a-row :gutter="20">
            <a-col :span="8">
              <label>视频服务名称：</label>
              <div class="table_header_wrp_cont">
                <a-input
                  v-model:value="queryParam.name"
                  allow-clear
                  placeholder="请输入视频服务名称"
                />
              </div>
            </a-col>
            <a-col :span="6">
              <label>设备编码：</label>
              <div class="table_header_wrp_cont">
                <a-input
                  v-model:value="queryParam.code"
                  allow-clear
                  placeholder="请输入设备编码"
                />
              </div>
            </a-col>
            <a-col :span="6">
              <a-space>
                <a-button type="primary" class="searchBtn" @click="searchTable">
                  查询
                </a-button>
                <a-button class="refBtn" @click="resetTable">重置</a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-card>

        <a-card size="small">
          <table-Comp
            :columns="columns"
            :data-source="tableData.data"
            :loading="loading"
            :scroll="{ x: 1200 }"
            @is-loading-fuc="(e) => (loading = e)"
            @success="success"
          >
            <!-- 视频服务名称列 -->
            <template #name="{ record }">
              <a-tooltip
                v-if="record.name && record.name.length > 20"
                :title="record.name"
              >
                <span>{{ record.name.substring(0, 20) }}...</span>
              </a-tooltip>
              <span v-else>{{ record.name || '-' }}</span>
            </template>

            <!-- 位置列 -->
            <template #location="{ record }">
              <a-tooltip
                v-if="record.location && record.location.length > 20"
                :title="record.location"
              >
                <span>{{ record.location.substring(0, 20) }}...</span>
              </a-tooltip>
              <span v-else>{{ record.location || '-' }}</span>
            </template>

            <!-- 识别分析列 -->
            <template #modelCodes="{ record }">
              <a-tag
                color="success"
                v-for="item in record.modelCodes"
                :key="item"
              >
                {{ dictStore.getDictLable('BizAlertTypeEnum', item) }}
              </a-tag>
            </template>

            <!-- 操作列 -->
            <template #operation="{ record }">
              <a-button type="link" @click="handleEdit(record)">编辑</a-button>
            </template>
          </table-Comp>
        </a-card>
      </div>
    </div>

    <!-- 视频编辑弹窗 -->
    <VideoEditModal ref="modalFormRef" @success="handleSaveSuccess" />
  </Page>
</template>

<style scoped lang="scss">
.wrap_con {
  position: relative;
}

.left_cont {
  width: 260px;
  max-height: 90vh;
  overflow: auto;

  :deep(.ant-card-body) {
    padding: 10px !important;
  }
}

.right_cont {
  width: calc(100% - 270px);
  max-height: 90vh;
  overflow-y: scroll;
  position: absolute;
  top: 0;
  right: 0;
}

.table_header_search {
  .searchBtn {
    margin-right: 10px;
  }
}

.noOrgan {
  color: #928f8f;
}
</style>
