<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bdyl.line.web.mapper.BizAlertMapper">
    <!-- 统计业务报警总数 -->
    <select id="countAll" resultType="int">
        SELECT COUNT(*) FROM t_biz_alert
    </select>

    <!-- 统计各类型业务报警数量 -->
    <select id="countByType" resultType="map">
        SELECT type, COUNT(*) AS count FROM t_biz_alert GROUP BY type
    </select>

    <!-- 按年统计各类型业务报警数量 -->
    <select id="countByYearAndType" resultType="map">
        SELECT YEAR(create_time) AS year, type, COUNT(*) AS count
        FROM t_biz_alert
        GROUP BY YEAR(create_time), type
        ORDER BY year ASC
    </select>

    <!-- 按年分月分类型统计业务报警数量 -->
    <select id="countByMonthAndType" resultType="map">
        SELECT MONTH(create_time) AS month, type, COUNT(*) AS count
        FROM t_biz_alert
        WHERE YEAR(create_time) = #{year}
        GROUP BY MONTH(create_time), type
        ORDER BY month ASC
    </select>

    <select id="selectSceneIdsByYear" resultType="java.lang.String">
        SELECT scene_ids
        FROM t_biz_alert
        WHERE YEAR(create_time) = #{year}
    </select>


    <select id="getLatestAlertByCameraId" resultType="com.bdyl.line.web.entity.BizAlertEntity">

        SELECT *
        FROM t_biz_alert
        WHERE camera_id = #{cameraId}
        ORDER BY create_time DESC LIMIT 1
    </select>


    <select id="getLatestForbidAlertByCameraId" resultType="com.bdyl.line.web.entity.BizAlertEntity">

        SELECT *
        FROM t_biz_alert
        WHERE camera_id = #{cameraId}
          AND forbid_alarm_time IS NOT NULL
        ORDER BY create_time DESC LIMIT 1
    </select>
</mapper> 