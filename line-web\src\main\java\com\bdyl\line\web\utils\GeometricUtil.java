package com.bdyl.line.web.utils;

import java.awt.geom.*;
import java.util.List;

import lombok.Data;

import org.springframework.util.CollectionUtils;

/**
 * @description: 几何工具
 * @author: star
 * @create: 2025-02-19 11:22
 **/
public final class GeometricUtil {

    /**
     * 私有构造函数，防止实例化。
     */
    private GeometricUtil() {}

    /**
     * 判断点位是否在多边形区域内 使用Java AWT的Path2D类，内部实现了高效的点在多边形判断算法
     *
     * @param point 待判断的点
     * @param area 多边形顶点列表（按顺序排列）
     * @return 是否在区域内
     */
    public static Boolean isPointInPolygon(Point point, List<Point> area) {
        if (point == null || CollectionUtils.isEmpty(area) || area.size() < 3) {
            return false;
        }

        // 创建Path2D对象来表示多边形
        Path2D.Double polygon = new Path2D.Double();

        // 移动到第一个点
        Point firstPoint = area.getFirst();
        polygon.moveTo(firstPoint.x, firstPoint.y);

        // 连接其余的点
        for (int i = 1; i < area.size(); i++) {
            Point currentPoint = area.get(i);
            polygon.lineTo(currentPoint.x, currentPoint.y);
        }

        // 闭合多边形
        polygon.closePath();

        // 使用Path2D的contains方法判断点是否在多边形内
        return polygon.contains(point.x, point.y);
    }

    /**
     * 点对象
     */
    @Data
    public static class Point {
        /**
         * x坐标
         */
        double x;
        /**
         * y坐标
         */
        double y;

        /**
         * 构造函数
         *
         * @param x x坐标
         * @param y y坐标
         */
        public Point(double x, double y) {
            this.x = x;
            this.y = y;
        }
    }

}
