<script lang="ts" setup>
import { computed, nextTick, onMounted, reactive, ref } from 'vue';

import { message } from 'ant-design-vue';

import { addDepartment_Api, updateDepartment_Api } from '#/api/core/department';
// import { getUsers_Api } from '#/api/core/user';

// import { useUserStore } from '@vben/stores';

// const userStore = useUserStore();

// import { useDictStore } from '#/store';

// const dictStore = useDictStore();

// 状态选项
// const statusOptions = dictStore.getDictOptions('StatusEnum');

const emit = defineEmits(['success']);

const open = ref<boolean>(false);
const modalTitle = ref<string>('');
const formRef = ref();
const loading = ref(false);
const labelCol = { span: 6 };
const wrapperCol = { span: 16 };

// 用户列表
// const userList = ref<any[]>([]);

// 表单数据
const formData = reactive<any>({
  id: undefined,
  name: undefined,
  // leaderId: undefined,
  remark: undefined,
  status: 'ENABLE',
});

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
  // leaderId: [
  //   { required: true, message: '请选择部门负责人', trigger: 'change' },
  // ],
  remark: [{ required: false, message: '请输入备注', trigger: 'blur' }],
};

// 是否为编辑模式
const isEdit = computed(() => !!formData.id);
// 重置表单
const resetForm = async () => {
  await formRef.value?.resetFields();
  Object.assign(formData, {
    id: undefined,
    name: undefined,
    // leaderId: undefined,
    remark: undefined,
    status: 'ENABLE',
  });
};

// 打开弹窗
const openModal = async (type: string, record?: any) => {
  open.value = true;
  modalTitle.value = type === 'create' ? '新增部门' : '编辑部门';
  await resetForm();

  if (type === 'update' && record) {
    nextTick(() => {
      // 编辑模式，只填充formData中已定义的字段
      Object.keys(formData).forEach((key: any) => {
        if (record[key] !== undefined) {
          formData[key] = record[key];
        }
      });
    });
  }
};

// 关闭弹窗
const closeModal = () => {
  open.value = false;
  resetForm();
};

// 保存部门
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;

    if (isEdit.value) {
      const temp = { ...formData };
      delete temp.id;
      // 更新部门
      await updateDepartment_Api(formData.id, temp);
      message.success('更新成功');
    } else {
      // 新增部门
      await addDepartment_Api(formData);
      message.success('添加成功');
    }

    closeModal();
    emit('success');
  } catch (error) {
    console.error('保存部门失败', error);
  } finally {
    loading.value = false;
  }
};

// 获取用户列表
// const loadUserList = async () => {
//   try {
//     const res = await getUsers_Api({
//       page: 1,
//       size: 1000, // 获取足够多的用户
//       organId: userStore.userInfo?.organId,
//     });
//     userList.value = res.data;
//   } catch (error) {
//     console.error('获取用户列表失败', error);
//     message.error('获取用户列表失败');
//   }
// };

// 暴露组件方法
defineExpose({
  openModal,
});

// 组件挂载时获取用户列表
onMounted(() => {
  // loadUserList();
});
</script>
<template>
  <a-modal
    v-model:open="open"
    :title="modalTitle"
    :confirm-loading="loading"
    :mask-closable="false"
    width="600px"
    @cancel="closeModal"
  >
    <a-form
      ref="formRef"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :model="formData"
      :rules="formRules"
    >
      <a-form-item label="部门名称" name="name">
        <a-input v-model:value="formData.name" placeholder="请输入部门名称" />
      </a-form-item>

      <!-- <a-form-item label="部门负责人" name="leaderId">
        <a-select
          v-model:value="formData.leaderId"
          allow-clear
          placeholder="请选择部门负责人"
          style="width: 100%"
          show-search
          :filter-option="
            (input, option) =>
              option?.label?.toLowerCase().includes(input.toLowerCase())
          "
        >
          <a-select-option
            v-for="user in userList"
            :key="user.id"
            :value="user.id"
            :label="user.name"
          >
            {{ user.name }}
          </a-select-option>
        </a-select>
      </a-form-item> -->

      <!-- <a-form-item label="状态" name="status">
        <a-radio-group v-model:value="formData.status">
          <a-radio
            v-for="item in statusOptions"
            :key="item.dictValue"
            :value="item.dictValue"
          >
            {{ item.dictLabel }}
          </a-radio>
        </a-radio-group>
      </a-form-item> -->

      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="formData.remark"
          placeholder="请输入备注"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>
