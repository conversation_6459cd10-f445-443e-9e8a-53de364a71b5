package com.bdyl.line.web.service.impl;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.bdyl.boot.data.query.Page;
import com.bdyl.boot.exception.BizException;
import com.bdyl.line.common.constant.enums.InspectionCycleEnum;
import com.bdyl.line.common.constant.enums.InspectionTaskCameraStatusEnum;
import com.bdyl.line.common.constant.enums.InspectionTaskStatusEnum;
import com.bdyl.line.common.constant.enums.StatusEnum;
import com.bdyl.line.web.entity.CameraEntity;
import com.bdyl.line.web.entity.InspectionPlanEntity;
import com.bdyl.line.web.entity.InspectionTaskCameraEntity;
import com.bdyl.line.web.entity.InspectionTaskEntity;
import com.bdyl.line.web.mapper.CameraMapper;
import com.bdyl.line.web.mapper.InspectionPlanMapper;
import com.bdyl.line.web.mapper.InspectionTaskCameraMapper;
import com.bdyl.line.web.mapper.InspectionTaskMapper;
import com.bdyl.line.web.model.dto.TimeSlotDTO;
import com.bdyl.line.web.model.request.inspection.InspectionTaskPageRequest;
import com.bdyl.line.web.model.response.inspection.InspectionTaskCameraResponse;
import com.bdyl.line.web.model.response.inspection.InspectionTaskExportDTO;
import com.bdyl.line.web.model.response.inspection.InspectionTaskResponse;
import com.bdyl.line.web.service.InspectionTaskService;

/**
 * 巡检任务服务实现类
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionTaskServiceImpl extends ServiceImpl<InspectionTaskMapper, InspectionTaskEntity>
    implements InspectionTaskService {

    /**
     * 巡检任务Mapper
     */
    private final InspectionTaskMapper inspectionTaskMapper;
    /**
     * 巡检计划Mapper
     */
    private final InspectionPlanMapper inspectionPlanMapper;
    /**
     * 巡检任务摄像头Mapper
     */
    private final InspectionTaskCameraMapper inspectionTaskCameraMapper;

    /**
     * 摄像头Mapper
     */
    private final CameraMapper cameraMapper;

    @Override
    public Page<InspectionTaskResponse> pageTasks(InspectionTaskPageRequest request) {
        LambdaQueryWrapper<InspectionTaskEntity> wrapper = buildConditions(request);

        com.baomidou.mybatisplus.extension.plugins.pagination.Page<InspectionTaskEntity> pageRequest =
            new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(request.getPage(), request.getSize());

        com.baomidou.mybatisplus.extension.plugins.pagination.Page<InspectionTaskEntity> pageResult =
            inspectionTaskMapper.selectPage(pageRequest, wrapper);

        List<InspectionTaskEntity> records = pageResult.getRecords();
        List<InspectionTaskResponse> resultList = records.stream().map(this::convertToResponse).toList();

        return Page.of(request.getPage(), request.getSize(), pageResult.getTotal(), Collections.emptyList(),
            resultList);

    }

    private LambdaQueryWrapper<InspectionTaskEntity> buildConditions(InspectionTaskPageRequest request) {
        LambdaQueryWrapper<InspectionTaskEntity> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isNotBlank(request.getCycleType())) {
            wrapper.eq(InspectionTaskEntity::getCycleType, request.getCycleType());
        }
        if (StringUtils.isNotBlank(request.getStatus())) {
            wrapper.eq(InspectionTaskEntity::getStatus, request.getStatus());
        }
        if (request.getResponsibleUserId() != null) {
            wrapper.eq(InspectionTaskEntity::getResponsibleUserId, request.getResponsibleUserId());
        }
        if (request.getPlanId() != null) {
            wrapper.eq(InspectionTaskEntity::getPlanId, request.getPlanId());
        }
        if (request.getStartDate() != null) {
            wrapper.ge(InspectionTaskEntity::getScheduledStartTime, request.getStartDate().atStartOfDay());
        }
        if (request.getEndDate() != null) {
            wrapper.le(InspectionTaskEntity::getScheduledStartTime, request.getEndDate().atTime(23, 59, 59));
        }

        wrapper.orderByDesc(InspectionTaskEntity::getScheduledStartTime);

        return wrapper;
    }

    @Override
    public List<InspectionTaskCameraResponse> getTaskCameras(Long id) {
        List<InspectionTaskCameraEntity> taskCameras = inspectionTaskCameraMapper.selectByTaskId(id);

        return taskCameras.stream().map(entity -> {
            InspectionTaskCameraResponse response = new InspectionTaskCameraResponse();
            BeanUtils.copyProperties(entity, response);

            // 获取摄像头状态
            CameraEntity camera = cameraMapper.selectById(entity.getCameraId());
            if (camera != null) {
                response.setCameraStatus(camera.getStatus());
                response.setCameraCode(camera.getCode());
            }

            return response;
        }).toList();
    }

    @Override
    public void updateTaskProgress(Long taskId) {
        Integer completedCount = inspectionTaskCameraMapper.countCompletedByTaskId(taskId);
        Integer totalCount = inspectionTaskCameraMapper.countByTaskId(taskId);

        InspectionTaskEntity task = getById(taskId);
        if (task != null) {
            task.setCompletedCount(completedCount);

            // 如果全部完成，自动完成任务
            if (task.getActualStartTime() == null) {
                task.setActualStartTime(LocalDateTime.now());
            }
            if (completedCount.equals(totalCount)) {
                task.setStatus(InspectionTaskStatusEnum.COMPLETED.getValue());
                task.setActualEndTime(LocalDateTime.now());
            } else {
                task.setStatus(InspectionTaskStatusEnum.IN_PROGRESS.getValue());
            }
            updateById(task);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateTasks() {
        log.info("开始生成巡检任务...");

        LocalDate currentDate = LocalDate.now();
        List<InspectionPlanEntity> plans = selectPlansForTaskGeneration(currentDate);

        for (InspectionPlanEntity plan : plans) {
            try {
                generateTaskForPlan(plan);
            } catch (Exception e) {
                log.error("为计划[{}]生成任务失败", plan.getName(), e);
            }
        }

        log.info("巡检任务生成完成，共处理{}个计划", plans.size());
    }

    private List<InspectionPlanEntity> selectPlansForTaskGeneration(LocalDate currentDate) {

        LambdaQueryWrapper<InspectionPlanEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.le(InspectionPlanEntity::getStartDate, currentDate);
        wrapper.eq(InspectionPlanEntity::getStatus, StatusEnum.ENABLE.getValue());

        return inspectionPlanMapper.selectList(wrapper);
    }

    private void generateTaskForPlan(InspectionPlanEntity plan) {

        // 查看摄像头是否还存在，如果不存在则将计划禁用掉
        List<Long> cameraIds = plan.getCameraIds();
        List<CameraEntity> cameraEntities = cameraMapper.selectByIds(cameraIds);
        if (CollectionUtils.isEmpty(cameraEntities) || cameraEntities.size() != cameraIds.size()) {
            plan.setStatus(StatusEnum.DISABLE.getValue());
            inspectionPlanMapper.updateById(plan);
        }

        LocalDateTime currentTime = LocalDateTime.now();
        // 计算应该生成的任务时间点
        List<LocalDateTime> taskTimes = calculateTaskTimes(plan, currentTime);

        for (LocalDateTime taskTime : taskTimes) {
            // 检查该时间点是否已经生成过任务
            if (isTaskAlreadyGenerated(plan.getId(), taskTime)) {
                continue;
            }

            // 创建任务
            InspectionTaskEntity task = new InspectionTaskEntity();
            task.setTenantId(plan.getTenantId());
            task.setOrganId(plan.getOrganId());
            task.setPlanId(plan.getId());
            task.setTaskName(generateTaskName(plan.getName(), taskTime));
            task.setCycleType(plan.getCycleType());
            task.setCycleValue(plan.getCycleValue());
            task.setStatus(InspectionTaskStatusEnum.PENDING.getValue());
            task.setResponsibleUserId(plan.getResponsibleUserId());
            task.setResponsibleUserName(plan.getResponsibleUserName());
            task.setCameraCount(plan.getCameraIds() != null ? plan.getCameraIds().size() : 0);
            task.setCompletedCount(0);

            // 设置任务时间段信息
            setTaskScheduledTime(task, plan, taskTime);

            save(task);

            // 创建任务摄像头关联
            createTaskCameras(task, plan.getCameraIds());

            log.info("为计划[{}]生成任务[{}]", plan.getName(), task.getTaskName());
        }
    }

    /**
     * 计算需要生成的任务时间点
     */
    private List<LocalDateTime> calculateTaskTimes(InspectionPlanEntity plan, LocalDateTime currentTime) {
        List<LocalDateTime> taskTimes = new ArrayList<>();
        LocalDateTime planStartTime = plan.getStartDate().atStartOfDay();

        // 如果计划还未开始，返回空列表
        if (currentTime.isBefore(planStartTime)) {
            return taskTimes;
        }

        // 根据周期类型计算需要生成的任务
        switch (plan.getCycleType()) {
            case "HOUR":
                taskTimes.addAll(calculateHourlyTasks(plan, currentTime));
                break;
            case "DAY":
                taskTimes.addAll(calculateNewDailyTasks(plan, currentTime));
                break;
            case "WEEK":
                taskTimes.addAll(calculateNewWeeklyTasks(plan, currentTime));
                break;
            case "MONTH":
                taskTimes.addAll(calculateNewMonthlyTasks(plan, currentTime));
                break;
            default:
                throw new BizException("不支持的周期类型: " + plan.getCycleType());
        }

        return taskTimes;
    }

    /**
     * 检查指定时间点是否已经生成过任务
     */
    private boolean isTaskAlreadyGenerated(Long planId, LocalDateTime taskTime) {
        // 检查前后5分钟内是否已有任务
        LocalDateTime startTime = taskTime.minusMinutes(5);
        LocalDateTime endTime = taskTime.plusMinutes(5);

        List<InspectionTaskEntity> existingTasks =
            inspectionTaskMapper.selectByPlanIdAndTimeRange(planId, startTime, endTime);
        return !existingTasks.isEmpty();
    }

    private String generateTaskName(String planName, LocalDateTime scheduledTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss");
        return planName + "-" + scheduledTime.format(formatter);
    }

    private void createTaskCameras(InspectionTaskEntity task, List<Long> cameraIds) {
        if (cameraIds == null || cameraIds.isEmpty()) {
            return;
        }

        List<CameraEntity> cameras = cameraMapper.selectByIds(cameraIds);
        List<InspectionTaskCameraEntity> taskCameras = new ArrayList<>();

        for (int i = 0; i < cameras.size(); i++) {
            CameraEntity camera = cameras.get(i);
            InspectionTaskCameraEntity taskCamera = new InspectionTaskCameraEntity();
            taskCamera.setTenantId(task.getTenantId());
            taskCamera.setOrganId(task.getOrganId());
            taskCamera.setTaskId(task.getId());
            taskCamera.setCameraId(camera.getId());
            taskCamera.setCameraName(camera.getName());
            taskCamera.setStatus(InspectionTaskCameraStatusEnum.PENDING.getValue());
            taskCamera.setSortOrder(i + 1);
            taskCameras.add(taskCamera);
        }

        inspectionTaskCameraMapper.insert(taskCameras);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleMissedTasks() {
        log.info("开始处理漏检任务...");

        // 查询需要检查漏检的任务
        List<InspectionTaskEntity> tasksToCheck = inspectionTaskMapper.selectTasksForMissedCheck();

        List<Long> missedTaskIds = new ArrayList<>();

        for (InspectionTaskEntity task : tasksToCheck) {
            if (shouldMarkAsMissed(task)) {
                missedTaskIds.add(task.getId());
            }
        }

        if (!missedTaskIds.isEmpty()) {
            inspectionTaskMapper.updateTasksToMissed(missedTaskIds);
            log.info("标记漏检任务{}个", missedTaskIds.size());
        } else {
            log.info("没有需要标记为漏检的任务");
        }
    }

    @Override
    public List<InspectionTaskExportDTO> export(InspectionTaskPageRequest request) {

        LambdaQueryWrapper<InspectionTaskEntity> wrapper = buildConditions(request);

        List<InspectionTaskEntity> entities = inspectionTaskMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }
        return entities.stream().map(item -> {
            InspectionTaskExportDTO dto = new InspectionTaskExportDTO();
            dto.setTaskName(item.getTaskName());
            dto.setCycleType(InspectionCycleEnum.fromValue(item.getCycleType()).getName());
            dto.setCycleValue(item.getCycleValue());
            dto.setScheduledStartTime(item.getScheduledStartTime());
            dto.setScheduledEndTime(item.getScheduledEndTime());
            dto.setActualStartTime(item.getActualStartTime());
            dto.setActualEndTime(item.getActualEndTime());
            dto.setStatus(InspectionTaskStatusEnum.fromValue(item.getStatus()).getName());
            dto.setResponsibleUserName(item.getResponsibleUserName());
            dto.setCameraCount(item.getCameraCount());
            dto.setCompletedCount(item.getCompletedCount());
            dto.setRemarks(item.getRemarks());

            return dto;
        }).toList();

    }

    /**
     * 判断任务是否应该标记为漏检
     */
    private boolean shouldMarkAsMissed(InspectionTaskEntity task) {
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime scheduledEndTime = task.getScheduledEndTime();
        Integer cameraCount = task.getCameraCount();
        Integer completedCount = task.getCompletedCount();

        // 检查必要字段
        if (scheduledEndTime == null || cameraCount == null || completedCount == null) {
            log.warn("任务[{}]缺少必要字段，无法判断漏检状态", task.getId());
            return false;
        }

        // 漏检判断逻辑：当前时间超过计划结束时间，且还有摄像头未完成巡检
        return currentTime.isAfter(scheduledEndTime) && completedCount < cameraCount;
    }

    private InspectionTaskResponse convertToResponse(InspectionTaskEntity entity) {
        InspectionTaskResponse response = new InspectionTaskResponse();
        BeanUtils.copyProperties(entity, response);

        // 查询计划名称
        InspectionPlanEntity plan = inspectionPlanMapper.selectById(entity.getPlanId());
        if (plan != null) {
            response.setPlanName(plan.getName());
        }

        return response;
    }

    /**
     * 计算小时级任务
     */
    private List<LocalDateTime> calculateHourlyTasks(InspectionPlanEntity plan, LocalDateTime currentTime) {
        List<LocalDateTime> taskTimes = new ArrayList<>();
        LocalDate currentDate = currentTime.toLocalDate();

        // 检查时间段配置
        List<TimeSlotDTO> timeSlots = plan.getTimeSlots();
        if (timeSlots == null || timeSlots.isEmpty()) {
            log.warn("计划[{}]的时间段配置为空", plan.getName());
            return taskTimes;
        }

        // 根据时间段配置生成今天的任务
        for (TimeSlotDTO timeSlot : timeSlots) {
            LocalDateTime taskTime = currentDate.atTime(timeSlot.getStartTime());

            // 只生成当前时间前10分钟到当前时间之间的任务
            if (taskTime.isAfter(currentTime.minusMinutes(10))
                && (taskTime.isBefore(currentTime) || taskTime.isEqual(currentTime))) {
                taskTimes.add(taskTime);
            }
        }

        return taskTimes;
    }

    /**
     * 计算新的日级任务（使用开始时间和结束时间）
     */
    private List<LocalDateTime> calculateNewDailyTasks(InspectionPlanEntity plan, LocalDateTime currentTime) {
        List<LocalDateTime> taskTimes = new ArrayList<>();
        LocalDate currentDate = currentTime.toLocalDate();
        LocalDate planStartDate = plan.getStartDate();

        // 检查配置
        LocalTime startTime = plan.getStartTime();
        Integer cycleValue = plan.getCycleValue();
        if (startTime == null) {
            log.warn("计划[{}]的开始时间配置为空", plan.getName());
            return taskTimes;
        }
        if (cycleValue == null || cycleValue <= 0) {
            log.warn("计划[{}]的周期值配置无效: {}", plan.getName(), cycleValue);
            return taskTimes;
        }

        // 计算今天是否应该生成任务（根据周期间隔）
        long daysBetween = ChronoUnit.DAYS.between(planStartDate, currentDate);
        if (daysBetween >= 0 && daysBetween % cycleValue == 0) {
            // 生成今天的任务
            LocalDateTime taskTime = currentDate.atTime(startTime);

            // 只生成今天的任务
            if (!taskTime.isBefore(currentTime)) {
                taskTimes.add(taskTime);
            }
        }

        return taskTimes;
    }

    /**
     * 计算新的周级任务（支持间隔周数和星期几配置）
     */
    private List<LocalDateTime> calculateNewWeeklyTasks(InspectionPlanEntity plan, LocalDateTime currentTime) {
        List<LocalDateTime> taskTimes = new ArrayList<>();
        LocalDate currentDate = currentTime.toLocalDate();
        LocalDate planStartDate = plan.getStartDate();

        // 检查配置
        Integer cycleValue = plan.getCycleValue();
        Integer dayValue = plan.getDayValue();
        LocalTime startTime = plan.getStartTime();

        if (cycleValue == null || cycleValue <= 0 || dayValue == null || dayValue < 1 || dayValue > 7
            || startTime == null) {
            log.warn("计划[{}]的周级配置无效: cycleValue={}, dayValue={}, startTime={}", plan.getName(), cycleValue, dayValue,
                startTime);
            return taskTimes;
        }

        // 计算指定星期几的日期
        DayOfWeek targetDayOfWeek = DayOfWeek.of(dayValue);
        LocalDate taskDate = currentDate.with(targetDayOfWeek);

        // 如果指定的星期几在当前日期之前，说明是本周已过的日期，不生成任务
        if (taskDate.isBefore(currentDate)) {
            return taskTimes;
        }

        // 如果指定的星期几是今天，检查是否应该在本周期生成任务
        if (taskDate.isEqual(currentDate)) {
            // 计算从计划开始到当前周的周数
            LocalDate planStartWeekStart = planStartDate.with(DayOfWeek.MONDAY);
            LocalDate currentWeekStart = currentDate.with(DayOfWeek.MONDAY);
            long weeksBetween = ChronoUnit.WEEKS.between(planStartWeekStart, currentWeekStart);

            if (weeksBetween >= 0 && weeksBetween % cycleValue == 0) {
                LocalDateTime taskTime = taskDate.atTime(startTime);
                if (!taskTime.isBefore(currentTime)) {
                    taskTimes.add(taskTime);
                }
            }
        }

        return taskTimes;
    }

    /**
     * 计算新的月级任务（支持间隔月数和每月几号配置）
     */
    private List<LocalDateTime> calculateNewMonthlyTasks(InspectionPlanEntity plan, LocalDateTime currentTime) {
        List<LocalDateTime> taskTimes = new ArrayList<>();
        LocalDate currentDate = currentTime.toLocalDate();
        LocalDate planStartDate = plan.getStartDate();

        // 检查配置
        Integer cycleValue = plan.getCycleValue();
        Integer dayValue = plan.getDayValue();
        LocalTime startTime = plan.getStartTime();

        if (cycleValue == null || cycleValue <= 0 || dayValue == null || dayValue < 1 || dayValue > 31
            || startTime == null) {
            log.warn("计划[{}]的月级配置无效: cycleValue={}, dayValue={}, startTime={}", plan.getName(), cycleValue, dayValue,
                startTime);
            return taskTimes;
        }

        // 计算本月指定日期，处理月末边界情况
        int actualDay = Math.min(dayValue, currentDate.lengthOfMonth());
        LocalDate taskDate = currentDate.withDayOfMonth(actualDay);

        // 如果指定的日期在当前日期之前，说明是本月已过的日期，不生成任务
        if (taskDate.isBefore(currentDate)) {
            return taskTimes;
        }

        // 如果指定的日期是今天，检查是否应该在本月期生成任务
        if (taskDate.isEqual(currentDate)) {
            // 计算从计划开始到当前月的月数
            LocalDate planStartMonth = planStartDate.withDayOfMonth(1);
            LocalDate currentMonth = currentDate.withDayOfMonth(1);
            long monthsBetween = ChronoUnit.MONTHS.between(planStartMonth, currentMonth);

            if (monthsBetween >= 0 && monthsBetween % cycleValue == 0) {
                LocalDateTime taskTime = taskDate.atTime(startTime);
                if (!taskTime.isBefore(currentTime) && !isTaskAlreadyGenerated(plan.getId(), taskTime)) {
                    taskTimes.add(taskTime);
                }
            }
        }

        return taskTimes;
    }

    /**
     * 设置任务的计划时间信息
     */
    private void setTaskScheduledTime(InspectionTaskEntity task, InspectionPlanEntity plan, LocalDateTime taskTime) {
        switch (plan.getCycleType()) {
            case "HOUR":
                // 小时类型：从时间段配置中找到对应的时间段
                List<TimeSlotDTO> timeSlots = plan.getTimeSlots();
                LocalTime taskTimeOnly = taskTime.toLocalTime();
                TimeSlotDTO matchedTimeSlot = null;

                // 查找匹配的时间段
                for (TimeSlotDTO timeSlot : timeSlots) {
                    if (timeSlot.getStartTime().equals(taskTimeOnly)) {
                        matchedTimeSlot = timeSlot;
                        break;
                    }
                }

                if (matchedTimeSlot != null) {
                    // 设置任务的开始和结束时间
                    LocalDate taskDate = taskTime.toLocalDate();
                    task.setScheduledStartTime(taskDate.atTime(matchedTimeSlot.getStartTime()));
                    task.setScheduledEndTime(taskDate.atTime(matchedTimeSlot.getEndTime()));
                } else {
                    // 如果没有找到匹配的时间段，使用taskTime作为开始时间，结束时间设为开始时间+1小时
                    log.warn("计划[{}]中未找到匹配的时间段，taskTime: {}", plan.getName(), taskTimeOnly);
                    task.setScheduledStartTime(taskTime);
                    task.setScheduledEndTime(taskTime.plusHours(1));
                }
                break;
            case "DAY":
            case "WEEK":
            case "MONTH":
                // 天、周、月类型：使用计划配置的开始和结束时间
                LocalDate taskDate = taskTime.toLocalDate();
                task.setScheduledStartTime(taskDate.atTime(plan.getStartTime()));
                task.setScheduledEndTime(taskDate.atTime(plan.getEndTime()));
                break;
            default:
                // 其他类型保持原有逻辑，使用原来的 scheduledTime
                task.setScheduledStartTime(taskTime);
                task.setScheduledEndTime(taskTime.plusHours(1));
                break;
        }
    }
}
