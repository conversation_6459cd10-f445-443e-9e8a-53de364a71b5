<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import * as InspectionPlanApi from '#/api/core/inspection-plan';
import { getUsers_Api } from '#/api/core/user';
import tableComp from '#/components/TableComp/table.vue';
import { useDictStore } from '#/store/dict';
import { formatDate } from '@vben/utils';

import { downloadFileFromBlob } from '@vben/utils';

import ScreenshotModal from './components/ScreenshotModal.vue';

// 查询条件
const queryParam = reactive({
  responsibleUserId: undefined as string | number | undefined, // 负责人ID
  cycleType: undefined as string | undefined, // 巡检周期类型
  cameraName: undefined as string | undefined, // 异常监控名称
  startDate: undefined as string | undefined, // 实际巡查开始时间
  endDate: undefined as string | undefined, // 实际巡查结束时间
  isNormal: false,
  page: 1,
  size: 10,
});

const loading = ref(false);
const screenshotModalRef = ref();

const dictStore = useDictStore();

// 巡检周期选项
const cycleOptions = dictStore.getDictOptions('InspectionCycleEnum');

// 用户选项
const userOptions = ref<any[]>([]);

// 表格数据源
const tableData = reactive<any>({
  data: {},
});

// 获取用户列表
const getUserList = async () => {
  try {
    const res = await getUsers_Api({ page: 1, size: 1000 });
    userOptions.value = res?.data || [];
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
};

// 表格相关配置
const columns = [
  { title: '任务名称', dataIndex: 'taskName', width: 200 },
  {
    title: '巡检周期',
    dataIndex: 'inspectionCycle',
    width: 120,
    customRender: ({ text }: { text: string }) => {
      const option = cycleOptions.find((item: any) => item.dictValue === text);
      return option?.dictLabel || '-';
    },
  },
  {
    title: '应巡检时间',
    dataIndex: 'scheduledStartTime',
    width: 220,
    customRender: ({ text, record }: { text: string; record: any }) => {
      if (!text) {
        return '-';
      }
      return (
        formatDate(text, 'YYYY-MM-DD HH:mm') +
        '~' +
        formatDate(record.scheduledEndTime, 'YYYY-MM-DD HH:mm')
      );
    },
  },
  {
    title: '实际巡查时间',
    dataIndex: 'inspectionTime',
    width: 180,
    customRender: ({ text }: { text: string }) => {
      return text ? formatDate(text, 'YYYY-MM-DD HH:mm') : '-';
    },
  },
  { title: '巡检负责人', dataIndex: 'responsibleUserName', width: 120 },
  { title: '异常监控名称', dataIndex: 'cameraName', width: 150 },
  { title: '操作', dataIndex: 'operation', width: 120, fixed: 'right' },
];

// 加载巡检异常记录列表
const loadInspectionAbnormalRecords = async () => {
  loading.value = true;
  try {
    const res = await InspectionPlanApi.getInspectionRecordList_Api(queryParam);
    // 更新表格数据源
    const temp = {
      data: res.data || [],
      page: queryParam.page,
      size: queryParam.size,
      total: res.total || 0,
    };
    tableData.data = temp;
  } catch (error) {
    console.error('获取巡检异常记录列表失败', error);
    message.error('获取巡检异常记录列表失败');
  } finally {
    loading.value = false;
  }
};

const success = (data: any) => {
  queryParam.page = data.pi;
  queryParam.size = data.ps;
  loadInspectionAbnormalRecords();
};

// 查询
const searchTable = () => {
  queryParam.page = 1;
  loadInspectionAbnormalRecords();
};

// 重置
const resetTable = () => {
  queryParam.responsibleUserId = undefined;
  queryParam.cycleType = undefined;
  queryParam.cameraName = undefined;
  queryParam.startDate = undefined;
  queryParam.endDate = undefined;
  queryParam.isNormal = false;
  queryParam.page = 1;
  queryParam.size = 10;
  loadInspectionAbnormalRecords();
};

// 查看截图
const handleViewScreenshot = (record: any) => {
  screenshotModalRef.value?.openModal(record);
};

// 用户选择处理
const handleUserChange = (userId: string) => {
  queryParam.responsibleUserId = userId;
  // 触发查询
  searchTable();
};

// 时间范围选择处理
const handleDateRangeChange = (dates: any) => {
  if (dates && dates.length === 2) {
    queryParam.startDate = dates[0];
    queryParam.endDate = dates[1];
  } else {
    queryParam.startDate = undefined;
    queryParam.endDate = undefined;
  }
};

// 导出
const handleExport = async () => {
  const params = {
    responsibleUserId: queryParam.responsibleUserId,
    cycleType: queryParam.cycleType,
    cameraName: queryParam.cameraName,
    startDate: queryParam.startDate,
    endDate: queryParam.endDate,
    isNormal: queryParam.isNormal,
  };
  const res = await InspectionPlanApi.exportInspectionRecord_Api(params);
  downloadFileFromBlob({ source: res, fileName: '巡检记录.xlsx' });
};

onMounted(() => {
  getUserList();
  loadInspectionAbnormalRecords();
});
</script>

<template>
  <Page>
    <a-card class="table_header_search mb-5" size="small">
      <a-row :gutter="20">
        <a-col :span="4" flex>
          <label>负责人 ：</label>
          <div class="table_header_wrp_cont">
            <a-select
              :value="queryParam.responsibleUserId"
              allow-clear
              placeholder="请选择负责人"
              style="width: 100%"
              show-search
              :filter-option="false"
              @change="handleUserChange"
            >
              <a-select-option
                v-for="user in userOptions"
                :key="user.id"
                :value="user.id"
              >
                {{ user.realName || user.username || user.name }}
              </a-select-option>
            </a-select>
          </div>
        </a-col>
        <a-col :span="5">
          <label>巡检周期：</label>
          <div class="table_header_wrp_cont">
            <a-select
              v-model:value="queryParam.cycleType"
              allow-clear
              placeholder="请选择巡检周期"
              style="width: 100%"
            >
              <a-select-option
                v-for="option in cycleOptions"
                :key="option.dictValue"
                :value="option.dictValue"
              >
                {{ option.dictLabel }}
              </a-select-option>
            </a-select>
          </div>
        </a-col>
        <a-col :span="7">
          <label>异常监控名称：</label>
          <div class="table_header_wrp_cont">
            <a-input
              v-model:value="queryParam.cameraName"
              allow-clear
              placeholder="请输入异常监控名称"
              style="width: 100%"
            />
          </div>
        </a-col>
        <a-col :span="7">
          <label>实际巡查时间：</label>
          <div class="table_header_wrp_cont">
            <a-range-picker
              :value="[queryParam.startDate, queryParam.endDate]"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
            />
          </div>
        </a-col>
        <a-col :span="4">
          <a-button type="primary" class="searchBtn" @click="searchTable">
            查询
          </a-button>
          <a-button class="refBtn" @click="resetTable">重置</a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- 表格 -->
    <a-card size="small">
      <div class="table_action_btn_wrp">
        <a-button class="addBtn" @click="handleExport"> 导出 </a-button>
      </div>
      <tableComp
        :columns="columns"
        :data-source="tableData.data"
        :loading="loading"
        @success="success"
      >
        <!-- 巡检任务名称 -->
        <template #taskName="{ record }">
          <a-tooltip
            v-if="record.taskName && record.taskName.length > 20"
            :title="record.taskName"
          >
            <span>{{ record.taskName.substring(0, 20) }}...</span>
          </a-tooltip>
          <span v-else>{{ record.taskName || '-' }}</span>
        </template>
        <!-- 应巡检时间列 -->
        <template #scheduledStartTime="{ record }">
          {{
            record.scheduledStartTime
              ? formatDate(record.scheduledStartTime, 'YYYY-MM-DD HH:mm') +
                '~' +
                formatDate(record.scheduledEndTime, 'YYYY-MM-DD HH:mm')
              : '-'
          }}
        </template>

        <!-- 实际巡查时间列 -->
        <template #inspectionTime="{ record }">
          {{
            record.inspectionTime
              ? formatDate(record.inspectionTime, 'YYYY-MM-DD HH:mm')
              : '-'
          }}
        </template>

        <!-- 操作列  -->
        <template #operation="{ record }">
          <a-button
            type="link"
            size="small"
            @click="handleViewScreenshot(record)"
          >
            查看截图
          </a-button>
        </template>
      </tableComp>
    </a-card>

    <!-- 截图查看弹窗 -->
    <ScreenshotModal ref="screenshotModalRef" />
  </Page>
</template>

<style lang="scss" scoped>
.table_header_search {
  .table_header_wrp_cont {
    margin-top: 8px;
  }

  label {
    font-weight: 500;
    color: #333;
  }

  .searchBtn {
    margin-right: 8px;
  }

  .refBtn {
    background: #f5f5f5;
    border-color: #d9d9d9;
    color: #666;

    &:hover {
      background: #e6f7ff;
      border-color: #1890ff;
      color: #1890ff;
    }
  }
}
</style>
