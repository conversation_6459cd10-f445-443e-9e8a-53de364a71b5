<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import {
  getOrganList_Api,
  getRegion_Api,
  deleteOrgan_Api,
  exportOrgan_Api,
} from '#/api/core';
import tableComp from '#/components/TableComp/table.vue';

import EditModal from './components/EditModal.vue';
import DeviceManageModal from './components/DeviceManageModal.vue';
import BatchImport from '#/components/BatchImport';

import { useDictStore } from '#/store';

import { downloadFileFromBlob, formatDate } from '@vben/utils';

const dictStore = useDictStore();

// 查询条件
const queryParam = reactive<any>({
  regionCode: undefined, // 名称
  orgType: undefined, // 类型
  status: undefined, // 状态
  createTime: [],
  page: 1,
  size: 10,
});

const loading = ref(false);
const modalFormRef = ref();
const deviceManageModalRef = ref();
const importVisible = ref<boolean>(false);

// 区域选项
const areaOptions = ref<any[]>([]);
// 区域加载状态
const regionLoading = ref(false);

// 组织类型选项
const organTypeOptions = dictStore.getDictOptions('RegionLevelEnum');

// 表格数据源
const tableData = reactive<any>({
  data: {},
});

// 表格相关配置
const columns = [
  {
    title: '组织类型',
    dataIndex: 'orgType',
    customRender: ({ record }: { record: any }) => {
      return dictStore.getDictLable('RegionLevelEnum', record.orgType);
    },
  },
  { title: '区域', dataIndex: 'fullRegionName' },
  { title: '平台名称', dataIndex: 'platformName' },
  { title: '视频数量', dataIndex: 'cameraCount' },
  { title: '管理员手机号', dataIndex: 'adminPhone' },
  { title: '备注', dataIndex: 'remark' },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    customRender: ({ record }: { record: any }) => {
      return formatDate(record.createTime, 'YYYY-MM-DD HH:mm:ss');
    },
  },
  { title: '操作', dataIndex: 'operation', width: '250px' },
];

// 加载组织列表
const loadOrganList = async () => {
  loading.value = true;
  try {
    queryParam.startTime = queryParam.createTime
      ? queryParam.createTime[0]?.format('YYYY-MM-DD HH:mm:ss')
      : undefined;
    queryParam.endTime = queryParam.createTime
      ? queryParam.createTime[1]?.format('YYYY-MM-DD HH:mm:ss')
      : undefined;

    const searchParams = {
      ...queryParam,
      regionCode:
        queryParam.regionCode && queryParam.regionCode.length
          ? queryParam.regionCode[queryParam.regionCode.length - 1]
          : null,
    };

    const resData = await getOrganList_Api(searchParams);

    // 更新表格数据源
    const temp = {
      data: resData.data,
      page: resData.page,
      size: resData.size,
      total: resData.total,
    };
    tableData.data = temp;
  } catch (error) {
    console.error('获取组织列表失败', error);
  } finally {
    loading.value = false;
  }
};

const success = (data: any) => {
  queryParam.page = data.pi;
  queryParam.size = data.ps;
  loadOrganList();
};

// 查询
const searchTable = () => {
  queryParam.page = 1;
  loadOrganList();
};

// 重置
const resetTable = () => {
  queryParam.regionCode = undefined;
  queryParam.orgType = undefined;
  queryParam.status = undefined;
  queryParam.createTime = [];
  queryParam.page = 1;
  queryParam.size = 10;
  loadOrganList();
};

// 添加组织
const handleAdd = () => {
  modalFormRef.value.openModal('create');
};

// 编辑组织
const handleEdit = (record: any) => {
  modalFormRef.value.openModal('update', record);
};

// 设备管理
const handleDeviceManage = (record: any) => {
  deviceManageModalRef.value.openModal(record);
};

// 删除组织
const handleDelete = async (record: any) => {
  try {
    await deleteOrgan_Api(record.id);
    message.success('操作成功');
    loadOrganList();
  } catch (error) {
    console.error('删除组织失败', error);
  }
};

// 动态加载区域数据
const loadRegionData = async (selectedOptions: any[]) => {
  const targetOption = selectedOptions[selectedOptions.length - 1];
  targetOption.loading = true;

  try {
    const params = {
      parentCode: targetOption.code,
    };
    const children = await getRegion_Api(params);

    targetOption.loading = false;
    if (children && children.length > 0) {
      // 为子区域设置isLeaf属性
      const childrenWithLeaf = children.map((item: any) => ({
        ...item,
        isLeaf: false, // 默认都不是叶子节点，加载时再判断
        value: item.code,
        label: item.name,
        code: item.code,
      }));
      targetOption.children = childrenWithLeaf;
    } else {
      targetOption.isLeaf = true;
    }
  } catch (error) {
    targetOption.loading = false;
    console.error('加载区域数据失败', error);
    message.error('加载区域数据失败');
  }
};

// 加载初始区域数据
const loadInitialRegions = async () => {
  try {
    regionLoading.value = true;
    const params = { parentCode: undefined }; // parentCode为空查询顶级
    const regions = await getRegion_Api(params);

    // 转换区域数据格式，适配cascader组件
    return regions.map((item: any) => ({
      ...item,
      isLeaf: false, // 默认一级区域都不是叶子节点
      value: item.code,
      label: item.name,
      code: item.code,
    }));
  } catch (error) {
    console.error('加载区域数据失败', error);
    message.error('加载区域数据失败');
    return [];
  } finally {
    regionLoading.value = false;
  }
};

// 组织保存成功回调
const handleSaveSuccess = () => {
  loadOrganList();
};

// 导出
const handleExport = async () => {
  const startTime = queryParam.createTime
    ? queryParam.createTime[0]?.format('YYYY-MM-DD HH:mm:ss')
    : undefined;
  const endTime = queryParam.createTime
    ? queryParam.createTime[1]?.format('YYYY-MM-DD HH:mm:ss')
    : undefined;
  const params = {
    orgType: queryParam.orgType,
    regionCode: queryParam.regionCode && queryParam.regionCode.length
      ? queryParam.regionCode[queryParam.regionCode.length - 1]
      : undefined,
    startTime,
    endTime,
  };
  const res = await exportOrgan_Api(params);

  downloadFileFromBlob({ source: res, fileName: '组织数据.xlsx' });
};

const handleImportSuccess = () => {
  console.log('上传成功');
  loadOrganList();
};
const handleImportError = () => {
  console.log('上传失败');
};

onMounted(async () => {
  // 加载初始区域数据
  areaOptions.value = await loadInitialRegions();
  // 加载组织列表
  loadOrganList();
});
</script>

<template>
  <Page>
    <a-card class="table_header_search mb-5">
      <a-row :gutter="20">
        <a-col :span="7">
          <label>组织类型：</label>
          <div class="table_header_wrp_cont">
            <a-select
              v-model:value="queryParam.orgType"
              allow-clear
              placeholder="请选择组织类型"
              style="width: 90%"
              :field-names="{
                label: 'dictLabel',
                value: 'dictValue',
              }"
              :options="organTypeOptions"
            >
            </a-select>
          </div>
        </a-col>
        <a-col :span="6">
          <label>区域：</label>
          <div class="table_header_wrp_cont">
            <a-cascader
              v-model:value="queryParam.regionCode"
              :options="areaOptions"
              :load-data="loadRegionData"
              placeholder="请选择区域"
              style="width: 100%"
              change-on-select
              :loading="regionLoading"
            />
          </div>
        </a-col>
        <a-col :span="9">
          <label>创建时间：</label>
          <div class="table_header_wrp_cont">
            <a-range-picker
              :placeholder="['开始时间', '结束时间']"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              v-model:value="queryParam.createTime"
            />
          </div>
        </a-col>
        <a-col :span="4">
          <a-space>
            <a-button type="primary" class="searchBtn" @click="searchTable">
              查询
            </a-button>
            <a-button class="refBtn" @click="resetTable">重置</a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <a-card size="small">
      <div class="table_action_btn_wrp">
        <a-button class="addBtn" type="primary" @click="handleAdd">
          新建
        </a-button>
        <a-button class="addBtn" @click="importVisible = true">导入</a-button>

        <BatchImport
          v-model="importVisible"
          :upload-url="'/api/line/organ/import'"
          :template-url="'/api/line/organ/template'"
          title-prefix="组织"
          @success="handleImportSuccess"
          @error="handleImportError"
        />
        <a-button class="addBtn" @click="handleExport"> 导出 </a-button>
      </div>

      <table-Comp
        :columns="columns"
        :data-source="tableData.data"
        :loading="loading"
        :scroll="{ x: 1200 }"
        @is-loading-fuc="(e) => (loading = e)"
        @success="success"
      >
        <!-- 备注列 -->
        <template #remark="{ record }">
          <a-tooltip
            v-if="record.remark && record.remark.length > 20"
            :title="record.remark"
          >
            <span>{{ record.remark.substring(0, 20) }}...</span>
          </a-tooltip>
          <span v-else>{{ record.remark || '-' }}</span>
        </template>

        <!-- 操作列 -->
        <template #operation="{ record }">
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-button type="link" @click="handleDeviceManage(record)"
            >设备管理</a-button
          >
          <a-popconfirm
            title="数据删除后不可恢复，请确认后继续?"
            @confirm="handleDelete(record)"
          >
            <a-button type="link" danger>删除</a-button>
          </a-popconfirm>
        </template>
      </table-Comp>
    </a-card>

    <!-- 组织弹窗 -->
    <EditModal ref="modalFormRef" @success="handleSaveSuccess" />

    <!-- 设备管理弹窗 -->
    <DeviceManageModal
      ref="deviceManageModalRef"
      @success="handleSaveSuccess"
    />
  </Page>
</template>
<style scoped lang="scss"></style>
