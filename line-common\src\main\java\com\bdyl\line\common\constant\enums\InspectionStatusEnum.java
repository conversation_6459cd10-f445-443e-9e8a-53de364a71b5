package com.bdyl.line.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 巡检状态枚举
 *
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@AllArgsConstructor
public enum InspectionStatusEnum {

    /**
     * 待巡检
     */
    PENDING("PENDING", "待巡检", "等待巡检"),
    /**
     * 已巡检
     */
    COMPLETED("COMPLETED", "已巡检", "巡检已完成"),
    /**
     * 漏检
     */
    MISSED("MISSED", "漏检", "巡检漏检");

    /**
     * value
     */
    private final String value;
    /**
     * 名称
     */
    private final String name;
    /**
     * 描述
     */
    private final String desc;

}
