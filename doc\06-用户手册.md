# 线路巡检管理系统 - 用户手册

## 1. 系统概述

### 1.1 系统简介
线路巡检管理系统是一个专业的巡检管理平台，支持摄像头设备管理、巡检计划制定、任务自动生成、现场巡检记录等功能，帮助企业实现智能化、规范化的巡检管理。

### 1.2 主要功能
- **设备管理**：摄像头设备的注册、配置和监控
- **计划管理**：制定灵活的巡检计划和时间安排
- **任务管理**：自动生成巡检任务并跟踪执行进度
- **记录管理**：现场巡检记录和历史数据查询
- **报警管理**：设备故障和异常情况的实时报警
- **统计分析**：巡检数据的统计分析和报表生成

### 1.3 用户角色
- **系统管理员**：负责系统配置、用户管理、权限分配
- **巡检管理员**：负责制定巡检计划、分配任务、监控进度
- **巡检人员**：负责执行巡检任务、记录巡检结果
- **设备维护人员**：负责设备故障处理和维护保养

## 2. 系统登录

### 2.1 登录步骤
1. 打开浏览器，访问系统地址
2. 输入用户名和密码
3. 点击"登录"按钮
4. 系统验证成功后进入主界面

### 2.2 密码管理
- **修改密码**：点击右上角用户头像 → 个人设置 → 修改密码
- **忘记密码**：联系系统管理员重置密码

## 3. 摄像头管理

### 3.1 摄像头列表
**功能说明**：查看和管理所有摄像头设备

**操作步骤**：
1. 点击左侧菜单"设备管理" → "摄像头管理"
2. 查看摄像头列表，包含设备编码、名称、位置、状态等信息
3. 使用搜索框可按名称、编码等条件筛选设备
4. 点击"查看"可查看设备详细信息

### 3.2 添加摄像头
**功能说明**：注册新的摄像头设备

**操作步骤**：
1. 在摄像头列表页面点击"新增"按钮
2. 填写设备信息：
   - **设备编码**：唯一标识符（必填）
   - **设备名称**：便于识别的名称（必填）
   - **安装位置**：设备安装的具体位置
   - **IP地址**：设备的网络地址
   - **端口号**：设备的网络端口
   - **经纬度**：设备的地理位置坐标
3. 点击"保存"完成添加

### 3.3 视频播放
**功能说明**：查看摄像头的实时视频流

**操作步骤**：
1. 在摄像头列表中点击"播放"按钮
2. 系统自动获取视频流地址并播放
3. 支持全屏播放、截图等功能
4. 点击"停止"结束播放

### 3.4 历史回放
**功能说明**：查看摄像头的历史录像

**操作步骤**：
1. 在摄像头列表中点击"回放"按钮
2. 选择回放的日期和时间范围
3. 点击"开始回放"
4. 使用播放控制条调整播放进度

## 4. 巡检计划管理

### 4.1 计划列表
**功能说明**：查看和管理所有巡检计划

**操作步骤**：
1. 点击左侧菜单"巡检管理" → "巡检计划"
2. 查看计划列表，包含计划名称、周期类型、负责人、状态等信息
3. 使用筛选条件可按周期类型、状态等条件查询
4. 点击"查看"可查看计划详细信息

### 4.2 创建巡检计划
**功能说明**：制定新的巡检计划

**操作步骤**：
1. 在计划列表页面点击"新增"按钮
2. 填写基本信息：
   - **计划名称**：便于识别的计划名称（必填）
   - **计划描述**：计划的详细说明
   - **负责人**：选择巡检负责人（必填）
   - **启动日期**：计划开始执行的日期（必填）

3. 配置巡检周期：
   - **小时级**：选择具体的时间段，如"09:00-10:00"、"14:00-15:00"
   - **日级**：设置间隔天数和执行时间，如"每2天，08:00-18:00"
   - **周级**：设置间隔周数和星期几，如"每周，星期一，09:00-17:00"
   - **月级**：设置间隔月数和每月几号，如"每月，15号，10:00-16:00"

4. 选择摄像头：
   - 从摄像头列表中选择需要巡检的设备
   - 支持批量选择和搜索功能
   - 已选择的摄像头会显示在右侧列表中

5. 点击"保存"完成创建

### 4.3 编辑巡检计划
**功能说明**：修改已有的巡检计划

**操作步骤**：
1. 在计划列表中点击"编辑"按钮
2. 修改需要调整的信息
3. 点击"保存"完成修改

### 4.4 启用/禁用计划
**功能说明**：控制巡检计划的执行状态

**操作步骤**：
1. 在计划列表中找到目标计划
2. 点击状态开关按钮
3. 确认操作后计划状态即时生效

## 5. 巡检任务管理

### 5.1 任务列表
**功能说明**：查看系统自动生成的巡检任务

**操作步骤**：
1. 点击左侧菜单"巡检管理" → "巡检任务"
2. 查看任务列表，包含任务名称、计划时间、状态、进度等信息
3. 使用筛选条件可按状态、负责人、日期范围等条件查询
4. 任务状态说明：
   - **待巡检**：任务已生成，等待执行
   - **进行中**：任务正在执行
   - **已完成**：任务已完成
   - **漏检**：任务超时未完成

### 5.2 执行巡检任务
**功能说明**：巡检人员执行分配的巡检任务

**操作步骤**：
1. 在任务列表中找到分配给自己的任务
2. 点击"开始巡检"按钮
3. 系统显示任务中的摄像头列表
4. 逐一对每个摄像头进行巡检：
   - 点击摄像头名称查看实时画面
   - 拍照记录现场情况
   - 选择巡检结果（正常/异常）
   - 填写巡检备注
   - 点击"完成"确认该摄像头巡检完成
5. 完成所有摄像头巡检后，任务自动标记为已完成

### 5.3 查看任务详情
**功能说明**：查看任务的详细信息和执行情况

**操作步骤**：
1. 在任务列表中点击"查看"按钮
2. 查看任务基本信息：计划时间、实际时间、负责人等
3. 查看摄像头巡检情况：每个摄像头的巡检状态和结果
4. 查看巡检进度：已完成数量/总数量

## 6. 巡检记录管理

### 6.1 记录查询
**功能说明**：查询和查看历史巡检记录

**操作步骤**：
1. 点击左侧菜单"巡检管理" → "巡检记录"
2. 设置查询条件：
   - **时间范围**：选择查询的起止日期
   - **摄像头**：选择特定的摄像头设备
   - **巡检结果**：筛选正常或异常记录
   - **巡检人员**：选择特定的巡检人员
3. 点击"查询"获取符合条件的记录
4. 点击"查看"可查看记录详情

### 6.2 记录详情
**功能说明**：查看单条巡检记录的详细信息

**记录内容包括**：
- **基本信息**：巡检时间、巡检人员、摄像头信息
- **巡检结果**：正常或异常状态
- **现场照片**：巡检时拍摄的照片
- **巡检备注**：巡检人员填写的说明
- **位置信息**：巡检时的GPS坐标

### 6.3 记录导出
**功能说明**：将巡检记录导出为Excel文件

**操作步骤**：
1. 在记录查询页面设置筛选条件
2. 点击"导出"按钮
3. 选择导出格式和字段
4. 系统生成Excel文件并自动下载

## 7. 报警管理

### 7.1 报警监控
**功能说明**：实时监控系统报警信息

**操作步骤**：
1. 点击左侧菜单"报警管理" → "报警监控"
2. 查看当前未处理的报警列表
3. 报警类型包括：
   - **设备离线**：摄像头设备失去连接
   - **设备故障**：摄像头设备出现故障
   - **巡检超时**：巡检任务超过预定时间未完成
   - **异常检测**：AI算法检测到的异常情况

### 7.2 报警处理
**功能说明**：处理和确认报警信息

**操作步骤**：
1. 在报警列表中点击"处理"按钮
2. 查看报警详细信息
3. 填写处理结果和备注
4. 点击"确认处理"完成报警处理

### 7.3 报警统计
**功能说明**：查看报警的统计分析数据

**操作步骤**：
1. 点击左侧菜单"报警管理" → "报警统计"
2. 选择统计时间范围
3. 查看报警趋势图表和统计数据
4. 支持按报警类型、设备等维度分析

## 8. 系统设置

### 8.1 个人设置
**功能说明**：管理个人账户信息

**操作步骤**：
1. 点击右上角用户头像 → "个人设置"
2. 修改个人信息：姓名、邮箱、手机号等
3. 修改登录密码
4. 设置消息通知偏好

### 8.2 组织管理（管理员功能）
**功能说明**：管理组织架构和用户权限

**操作步骤**：
1. 点击左侧菜单"系统管理" → "组织管理"
2. 查看组织树结构
3. 添加、编辑、删除组织节点
4. 为组织分配用户和权限

### 8.3 用户管理（管理员功能）
**功能说明**：管理系统用户账户

**操作步骤**：
1. 点击左侧菜单"系统管理" → "用户管理"
2. 查看用户列表
3. 添加新用户：填写用户信息、分配角色和权限
4. 编辑用户：修改用户信息、调整权限
5. 重置密码：为用户重置登录密码

## 9. 常见问题

### 9.1 登录问题
**Q：忘记密码怎么办？**
A：联系系统管理员重置密码，或使用密码找回功能。

**Q：登录后页面显示异常？**
A：清除浏览器缓存，或尝试使用其他浏览器。

### 9.2 功能使用问题
**Q：为什么看不到某些菜单？**
A：可能是权限不足，联系管理员分配相应权限。

**Q：视频播放失败怎么办？**
A：检查摄像头设备是否在线，网络连接是否正常。

**Q：巡检任务没有自动生成？**
A：检查巡检计划是否已启用，时间配置是否正确。

### 9.3 性能问题
**Q：系统响应速度慢？**
A：检查网络连接，或联系技术支持优化系统性能。

**Q：文件上传失败？**
A：检查文件大小是否超过限制，网络连接是否稳定。
