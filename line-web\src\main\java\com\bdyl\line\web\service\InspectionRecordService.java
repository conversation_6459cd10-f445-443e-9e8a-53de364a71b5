package com.bdyl.line.web.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

import com.bdyl.boot.data.query.Page;
import com.bdyl.line.web.entity.InspectionRecordEntity;
import com.bdyl.line.web.model.request.inspection.InspectionRecordPageRequest;
import com.bdyl.line.web.model.request.inspection.InspectionRecordRequest;
import com.bdyl.line.web.model.response.inspection.InspectionRecordExportDTO;
import com.bdyl.line.web.model.response.inspection.InspectionRecordResponse;

/**
 * 巡检记录服务接口
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface InspectionRecordService extends IService<InspectionRecordEntity> {

    /**
     * 提交巡检记录
     *
     * @param request 请求对象
     * @return 巡检记录响应
     */
    InspectionRecordResponse submitRecord(InspectionRecordRequest request);

    /**
     * 分页查询巡检记录
     *
     * @param request 分页请求
     * @return 分页结果
     */
    Page<InspectionRecordResponse> pageRecords(InspectionRecordPageRequest request);

    /**
     * 根据任务摄像头ID查询巡检记录
     *
     * @param taskCameraId 任务摄像头ID
     * @return 巡检记录响应
     */
    InspectionRecordResponse getTaskRecordByTaskCameraId(Long taskCameraId);

    /**
     * 导出巡检记录
     *
     * @param request 请求对象
     * @return 巡检记录导出对象
     */
    List<InspectionRecordExportDTO> export(InspectionRecordPageRequest request);
}
