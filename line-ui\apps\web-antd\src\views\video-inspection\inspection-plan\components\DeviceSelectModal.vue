<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { message } from 'ant-design-vue';

import { getCameras_Api } from '#/api/core/camera';
import LeaFletMap from '#/components/LeaFletMap/index.vue';
import videoPng from '#/assets/images/video_sxt.png';

import { useUserStore } from '@vben/stores';

const emit = defineEmits(['confirm']);

const userStore = useUserStore();

// 弹窗状态
const open = ref(false);

// 地图实例和引用
let map: any = null;
const mapRef = ref();

// 摄像头数据
const cameras = ref<any[]>([]);

// 操作模式状态
const operationMode = ref<'none' | 'batch' | 'single'>('none');
const isDrawing = ref(false);

// 摄像头标记和选中状态
const cameraMarkers = ref<any[]>([]);
const selectedCameras = ref<any[]>([]);
const drawnPolygon = ref<any>(null);

// 摄像头图标配置
const CAMERA_ICON_URL = videoPng;

// 获取所有摄像头
const getCameras = async () => {
  try {
    const res = await getCameras_Api({ organId: userStore.userInfo?.organId });
    cameras.value = res || [];

    // 如果地图已经初始化，立即渲染摄像头
    if (map) {
      renderCameras();
    }
  } catch (error) {
    console.error('获取摄像头列表失败', error);
  }
};

// 地图初始化
const onMapInit = (mapInstance: any) => {
  map = mapInstance;
  renderCameras();
};

// 渲染摄像头标记
const renderCameras = () => {
  if (!map || !mapRef.value || !cameras.value.length) return;

  // 清除现有标记
  clearCameraMarkers();

  // 准备摄像头标记数据
  const markerArray = cameras.value.map((camera: any) => {
    // 检查是否已选中
    const isSelected = selectedCameras.value.some((c) => c.id === camera.id);

    return {
      ...camera,
      latitude: camera.latitude,
      longitude: camera.longitude,
      popup: camera.name || '未命名摄像头',
      markerOptions: {
        icon: {
          iconUrl: CAMERA_ICON_URL,
          iconSize: [24, 24],
          iconAnchor: [12, 12],
          className: `camera-marker ${camera.status === 'ONLINE' ? 'online' : 'offline'} ${isSelected ? 'selected' : ''}`,
        },
      },
      popupOptions: {
        maxWidth: '200px',
      },
    };
  });

  // 创建标记
  const markers = mapRef.value.createMarker(markerArray, false);

  // 为每个标记添加点击事件
  markers.forEach((marker: any, index: number) => {
    marker.on('click', () => {
      if (operationMode.value === 'single') {
        handleCameraClick(cameras.value[index], marker);
      }
    });
  });

  cameraMarkers.value = markers;
};

// 清除摄像头标记
const clearCameraMarkers = () => {
  if (cameraMarkers.value.length > 0) {
    cameraMarkers.value.forEach((marker) => {
      if (map && marker) {
        marker.remove();
      }
    });
    cameraMarkers.value = [];
  }
};

// 处理摄像头点击
const handleCameraClick = (camera: any, marker: any) => {
  if (operationMode.value !== 'single') return;

  const index = selectedCameras.value.findIndex((c) => c.id === camera.id);

  if (index > -1) {
    // 取消选中
    selectedCameras.value.splice(index, 1);
    updateMarkerClass(
      marker,
      camera.status === 'ONLINE' ? 'online' : 'offline',
      false,
    );
  } else {
    // 选中
    selectedCameras.value.push(camera);
    updateMarkerClass(
      marker,
      camera.status === 'ONLINE' ? 'online' : 'offline',
      true,
    );
  }
};

// 更新标记样式类
const updateMarkerClass = (
  marker: any,
  statusClass: string,
  isSelected: boolean,
) => {
  if (marker && marker._icon) {
    const iconElement = marker._icon;
    iconElement.className = iconElement.className.replace(
      /\b(online|offline|selected)\b/g,
      '',
    );
    iconElement.classList.add(statusClass);
    if (isSelected) {
      iconElement.classList.add('selected');
    }
  }
};

// 批量框选摄像头
const handleBatchSelect = () => {
  if (operationMode.value === 'batch') {
    exitBatchMode();
    return;
  }

  operationMode.value = 'batch';
  selectedCameras.value = [];
  startDrawing();
};

// 单个摄像头添加
const handleSingleSelect = () => {
  if (operationMode.value === 'single') {
    exitSingleMode();
    return;
  }

  operationMode.value = 'single';
  selectedCameras.value = [];
  renderCameras();
};

// 启动绘制
const startDrawing = () => {
  if (!mapRef.value) return;

  isDrawing.value = true;

  mapRef.value.createDrawControl({
    options: {
      position: 'topleft',
      draw: {
        marker: false,
        circlemarker: false,
        circle: false,
        rectangle: false,
        polyline: false,
        polygon: {
          allowIntersection: false,
          showArea: true,
          drawError: {
            color: '#e1e100',
            message: '<strong>绘制错误!</strong> 多边形不能自相交!',
          },
          shapeOptions: {
            color: '#ff9900',
            weight: 3,
            opacity: 0.8,
            fillColor: '#ff9900',
            fillOpacity: 0.3,
          },
        },
      },
      edit: {
        remove: true,
      },
    },
    multiple: false,
  });
};

// 绘制完成处理
const onDrawCreated = (e: any) => {
  if (operationMode.value !== 'batch') return;

  drawnPolygon.value = e.layer;
  isDrawing.value = false;

  const latLngs = e.layer.getLatLngs()[0];
  const polygonCoords = latLngs.map((latLng: any) => [latLng.lat, latLng.lng]);

  const camerasInPolygon = cameras.value.filter((camera: any) => {
    return mapRef.value.IsPtInPoly(
      [camera.latitude, camera.longitude],
      polygonCoords,
    );
  });

  selectedCameras.value = camerasInPolygon;

  if (selectedCameras.value.length === 0) {
    message.warning('未选中任何摄像头');
    clearDrawing(true);
  }
};

// 清空绘制
const clearDrawing = (notClearDrawControl?: Boolean) => {
  if (drawnPolygon.value && map) {
    map.removeLayer(drawnPolygon.value);
    drawnPolygon.value = null;
  }

  if (mapRef.value && mapRef.value.clearDrawControl && !notClearDrawControl) {
    mapRef.value.clearDrawControl();
  }
};

// 退出批量模式
const exitBatchMode = () => {
  operationMode.value = 'none';
  isDrawing.value = false;
  selectedCameras.value = [];
  clearDrawing();
};

// 退出单个模式
const exitSingleMode = () => {
  operationMode.value = 'none';
  selectedCameras.value = [];
  renderCameras();
};

// 确定选择（单个模式）
const confirmSingleSelection = () => {
  if (selectedCameras.value.length === 0) {
    message.warning('请至少选中1个摄像头');
    return;
  }
  handleConfirm();
};

// 确认选择
const handleConfirm = () => {
  if (selectedCameras.value.length === 0) {
    message.warning('请选择摄像头');
    return;
  }

  const cameraIds = selectedCameras.value.map((camera) => camera.id);
  emit('confirm', cameraIds, selectedCameras.value);
  closeModal();
};

// 打开弹窗
const openModal = (record: any) => {
  open.value = true;
  resetState();
  // getCameras();
  if (record) {
    selectedCameras.value = record;
  }
};

// 关闭弹窗
const closeModal = () => {
  open.value = false;
  resetState();
};

// 重置状态
const resetState = () => {
  operationMode.value = 'none';
  isDrawing.value = false;
  selectedCameras.value = [];
  clearDrawing();
};

// 暴露方法
defineExpose({
  openModal,
});

onMounted(() => {
  getCameras();
});
</script>

<template>
  <a-modal
    v-model:open="open"
    title="选择设备"
    width="1200px"
    :mask-closable="false"
    @cancel="closeModal"
    :destroyOnClose="true"
  >
    <!-- 操作按钮区域 -->
    <div class="btn-box mb-4">
      <a-space v-if="operationMode === 'none'">
        <a-button type="primary" @click="handleBatchSelect">
          批量框选摄像头
        </a-button>
        <a-button type="primary" @click="handleSingleSelect">
          单个摄像头添加
        </a-button>
      </a-space>

      <!-- 批量模式操作按钮 -->
      <a-space v-if="operationMode === 'batch'">
        <a-button @click="clearDrawing" :disabled="!drawnPolygon">
          清空画线
        </a-button>
        <a-button @click="exitBatchMode"> 退出 </a-button>
      </a-space>

      <!-- 单个模式操作按钮 -->
      <a-space v-if="operationMode === 'single'">
        <a-button type="primary" @click="confirmSingleSelection">
          确定选择
        </a-button>
        <a-button @click="exitSingleMode"> 退出 </a-button>
      </a-space>

      <!-- 选中数量显示 -->
      <div v-if="selectedCameras.length > 0" class="selected-info">
        已选中 <strong>{{ selectedCameras.length }}</strong> 个摄像头
      </div>
    </div>

    <div class="device-select-content">
      <div class="map-container">
        <LeaFletMap
          ref="mapRef"
          @mapInited="onMapInit"
          @draw-created="onDrawCreated"
        />

        <div class="legend-box">
          <div class="legend-item">
            <div class="legend-icon online">
              <img class="img-box" :src="videoPng" alt="" />
            </div>
            <div class="legend-label">在线设备</div>
          </div>
          <div class="legend-item">
            <div class="legend-icon offline">
              <img class="img-box" :src="videoPng" alt="" />
            </div>
            <div class="legend-label">离线设备</div>
          </div>
          <div class="legend-item">
            <div class="legend-icon selected">
              <img class="img-box" :src="videoPng" alt="" />
            </div>
            <div class="legend-label">已选设备</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button
        type="primary"
        @click="handleConfirm"
        :disabled="selectedCameras.length === 0"
      >
        确定（{{ selectedCameras.length }}）
      </a-button>
    </template>
  </a-modal>
</template>

<style lang="scss" scoped>
.device-select-content {
  height: 500px;

  .map-container {
    height: 100%;
    position: relative;
    border-radius: 8px;
    overflow: hidden;

    .legend-box {
      position: absolute;
      z-index: 9999;
      bottom: 20px;
      right: 10px;
      background: rgba(255, 255, 255, 0.9);
      padding: 10px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }

        .legend-icon {
          width: 20px;
          height: 20px;
          margin-right: 8px;

          .img-box {
            width: 100%;
            height: 100%;
          }

          &.offline {
            filter: grayscale(100%);
          }

          &.selected {
            filter: hue-rotate(130deg) saturate(1.5);
          }
        }

        .legend-label {
          font-size: 12px;
          color: #666;
        }
      }
    }
  }
}

.btn-box {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .selected-info {
    color: #1890ff;
    font-size: 14px;
  }
}

/* 摄像头标记样式 */
:deep(.leaflet-marker-icon) {
  transition: filter 0.3s ease;

  &.online {
    // 在线状态保持原色
  }

  &.offline {
    filter: grayscale(100%); /* 灰色 */
  }

  &.selected {
    filter: hue-rotate(130deg) saturate(1.5); /* 紫色 */
  }
}
</style>
