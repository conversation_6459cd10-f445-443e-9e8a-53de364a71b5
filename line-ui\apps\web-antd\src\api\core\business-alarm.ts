import { requestClient } from '#/api/request';

enum Api {
  businessAlarm = '/api/line/biz-alerts',
}

export namespace BusinessAlarmApi {
  export interface AlarmListParams {
    page: number;
    size: number;
    type?: string;
    tenantId?: string;
  }

  export interface Records {
    id: number;
    tenantId: number;
    type: string;
    cameraName: string;
    region: string;
    address: string;
    sceneName: string;
    verifyStatus: string;
    image: string;
    video: string;
    creatorId: number;
    updaterId: number;
    createTime: string;
    updateTime: string;
    organId: number;
  }

  export interface AlarmListResult {
    data: Records[];
    orders: any[];
    page: number;
    pages: number;
    size: number;
    total: number;
  }

  export interface AlarmCheckParams {
    id: string;
    checkResult: string;
    remark?: string;
  }
}

// 分页获取业务报警列表
export const getBusinessAlarmList_Api = (
  params: BusinessAlarmApi.AlarmListParams,
) => {
  return requestClient.get<BusinessAlarmApi.AlarmListResult>(
    `${Api.businessAlarm}/page`,
    { params },
  );
};

// 获取业务报警详情
export const getBusinessAlarm_Api = (id: string) => {
  return requestClient.get<BusinessAlarmApi.Records>(
    `${Api.businessAlarm}/${id}`,
  );
};

// 更新业务报警
export const updateBusinessAlarm_Api = (id: any, data: any) => {
  return requestClient.put(`${Api.businessAlarm}/${id}`, data);
};
