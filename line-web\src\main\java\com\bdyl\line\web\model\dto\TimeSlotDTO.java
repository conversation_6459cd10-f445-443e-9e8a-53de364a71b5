package com.bdyl.line.web.model.dto;

import java.time.LocalTime;

import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 时间段配置数据传输对象
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TimeSlotDTO {

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    private LocalTime startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private LocalTime endTime;

    /**
     * 验证时间段是否有效
     *
     * @return true-有效，false-无效
     */
    @JsonIgnore
    public boolean isValid() {
        if (startTime == null || endTime == null) {
            return false;
        }
        // 支持跨天的时间段，如 22:00-02:00
        return true;
    }

    /**
     * 获取时间段描述
     *
     * @return 时间段描述
     */
    @JsonIgnore
    public String getDescription() {
        if (startTime == null || endTime == null) {
            return "无效时间段";
        }
        return startTime.toString() + "-" + endTime.toString();
    }
}
