package com.bdyl.line.web.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bdyl.line.web.entity.CameraModelConfigEntity;

/**
 * 摄像头模型配置mapper
 */
@Mapper
public interface CameraModelConfigMapper extends BaseMapper<CameraModelConfigEntity> {
    /**
     * 获取某个摄像头所有模型的code
     *
     * @param cameraId 摄像头ID
     * @return 模型code列表
     */
    List<String> selectModelCodesByCameraId(@Param("cameraId") Long cameraId);

    /**
     * 通过摄像头ID和模型code删除配置
     *
     * @param cameraId 摄像头ID
     * @param modelCode 模型code
     */
    void deleteByCameraIdAndModelCode(@Param("cameraId") Long cameraId, @Param("modelCode") String modelCode);
}
