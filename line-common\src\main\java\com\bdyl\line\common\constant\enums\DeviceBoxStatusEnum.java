package com.bdyl.line.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备箱状态枚举
 *
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@AllArgsConstructor
public enum DeviceBoxStatusEnum {

    /**
     * 正常
     */
    NORMAL("NORMAL", "正常", "设备箱状态正常"),
    /**
     * 倾斜
     */
    TILT("TILT", "倾斜", "设备箱发生倾斜"),
    /**
     * 开箱
     */
    OPEN("OPEN", "开箱", "设备箱被打开"),
    /**
     * 倒放
     */
    INVERTED("INVERTED", "倒放", "设备箱倒放");

    /**
     * value
     */
    private final String value;
    /**
     * 名称
     */
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据value获取枚举
     *
     * @param boxStatus boxStatus
     * @return DeviceBoxStatusEnum
     */

    public static DeviceBoxStatusEnum fromValue(String boxStatus) {
        for (DeviceBoxStatusEnum status : values()) {
            if (status.getValue().equals(boxStatus)) {
                return status;
            }
        }
        return null;

    }
}
