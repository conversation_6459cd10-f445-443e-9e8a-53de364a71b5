package com.bdyl.line.web.model.response.user;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 用户响应对象
 */
@Data
public class UserExportDTO {
    /**
     * 组织名称
     */
    @ExcelProperty("组织名称")
    private String organName;
    /**
     * 组织类型
     */
    @ExcelProperty("组织类型")
    private String organType;
    /**
     * 用户姓名
     */
    @ExcelProperty("姓名")
    private String name;
    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    private String phone;
    /**
     * 账号
     */
    @ExcelProperty("账号")
    private String account;
    /**
     * 角色列表
     */
    @ExcelProperty("角色列表")
    private String roleNames;
    /**
     * 部门名称
     */
    @ExcelProperty("部门名称")
    private String departmentName;

    /**
     * 用户状态（ENABLE=启用，DISABLE=关闭） {@link com.bdyl.line.common.constant.enums.StatusEnum}
     */
    @ExcelProperty("用户状态")
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
