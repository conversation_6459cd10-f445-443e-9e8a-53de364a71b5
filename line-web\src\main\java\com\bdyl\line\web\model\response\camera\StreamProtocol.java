package com.bdyl.line.web.model.response.camera;

/**
 * 媒体流协议类型枚举
 */
public enum StreamProtocol {
    /**
     * Real-Time Messaging Protocol
     */
    RTMP,

    /**
     * Real Time Streaming Protocol
     */
    RTSP,

    /**
     * Flash Video over HTTP
     */
    FLV,

    /**
     * HTTP Live Streaming (.m3u8)
     */
    HLS,

    /**
     * MPEG Transport Stream over HTTP
     */
    TS,

    /**
     * Fragmented MP4 over HTTP
     */
    FMP4,

    /**
     * Web Real-Time Communication
     */
    WEBRTC
}
