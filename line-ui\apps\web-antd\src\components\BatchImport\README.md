# BatchImport 批量导入组件

基于Ant Design Vue的批量导入组件，用于处理Excel数据导入，提供模板下载、数据上传和结果展示功能。

## 功能特点

1. 集成对话框
   - 组件内置Dialog对话框，通过v-model控制显示/隐藏
   - 自动管理对话框关闭时的状态重置

2. 下载导入模板
   - 点击"下载模板"按钮直接触发下载
   - 模板文件名称默认为"xxx导入模板.xlsx"

3. 导入数据
   - 点击"导入数据"按钮触发文件选择
   - 只能上传1个.xls或.xlsx格式的Excel文件
   - 上传过程中显示进度条

4. 导入结果
   - 展示导入成功和失败的数据数量
   - 提供"下载异常数据"按钮导出失败数据

## 使用方法

```vue
<template>
  <a-button @click="showImport">批量导入</a-button>

  <BatchImport
    v-model="importVisible"
    :upload-url="'/api/system/dept/import'"
    :template-url="'/api/system/dept/download-template'"
    :upload-data="{ type: 1 }"
    title-prefix="部门"
    @success="handleImportSuccess"
    @error="handleImportError"
  />
</template>

<script setup>
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import BatchImport from '@/components/BatchImport';

const importVisible = ref(false);

const showImport = () => {
  importVisible.value = true;
};

const handleImportSuccess = (result) => {
  console.log('导入成功:', result);
  message.success('数据导入成功');
  // 刷新列表数据或执行其他操作
};

const handleImportError = (error) => {
  console.log('导入失败:', error);
  message.error('数据导入失败');
};
</script>
```

## 属性说明

| 属性名 | 类型 | 是否必填 | 默认值 | 说明 |
| --- | --- | --- | --- | --- |
| modelValue | Boolean | 是 | false | 是否显示导入对话框（支持v-model双向绑定） |
| uploadUrl | String | 是 | - | 上传文件的API地址 |
| templateUrl | String | 是 | - | 下载模板的API地址 |
| uploadData | Object | 否 | {} | 上传时附带的额外参数 |
| titlePrefix | String | 否 | "数据" | 标题前缀，例如"部门"、"设备"等 |
| errorHeaderMap | Object | 否 | {rowIndex: "行号", errorMsg: "错误信息"} | 异常数据表头映射，用于下载异常数据的Excel文件 |

## 事件说明

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| success | 导入成功时触发 | result: 导入结果对象 |
| error | 导入失败时触发 | error: 错误信息对象 |

## 方法说明

通过ref引用可以调用组件内部方法:

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| resetUpload | 重置上传状态 | 无 |

## 服务端接口要求

### 上传接口响应格式

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalCount": 100,
    "successCount": 95,
    "failureCount": 5,
    "failureFileUrl": "/api/system/dept/download-error/20210615123456"
  }
}
```

### 错误数据下载

服务端需要提供错误数据下载接口，并在上传接口的响应中返回下载地址。

## 技术特点

1. **Ant Design Vue集成**：使用a-modal、a-upload、a-button、a-progress等组件
2. **统一设计语言**：与项目整体UI风格保持一致
3. **完整功能**：支持模板下载、文件上传、结果展示、异常数据导出
4. **类型安全**：完整的TypeScript类型支持
5. **错误处理**：使用message组件提供用户友好的错误提示

## 注意事项

1. 确保后端接口返回的数据格式符合组件要求
2. 异常数据导出功能需要xlsx和file-saver库支持
3. 组件会自动处理文件类型校验，只允许上传Excel文件
4. 上传过程中会显示进度条，提升用户体验