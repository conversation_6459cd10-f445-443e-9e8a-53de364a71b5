package com.bdyl.line.web.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 太阳能电池板实体类，对应t_solar_panel表。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_solar_panel")
public class SolarPanelEntity extends BaseEntity {
    /**
     * 终端ID
     */
    private Long terminalId;

    /**
     * 所属组织ID
     */
    private Long organId;
    /**
     * 蓄电池温度(℃)
     */
    private Double batteryTemperature;
    /**
     * 蓄电池电压(V)
     */
    private Double batteryVoltage;
    /**
     * 蓄电池电流(A)
     */
    private Double batteryCurrent;
    /**
     * 蓄电池剩余电量(%)
     */
    private Double batteryLevel;
    /**
     * 直流负载电压(V)
     */
    private Double dcLoadVoltage;
    /**
     * 直流负载电流(A)
     */
    private Double dcLoadCurrent;
    /**
     * 直流负载功率(W)
     */
    private Double dcLoadPower;
    /**
     * 当日用电量(kWh)
     */
    private Double dcLoadEnergyToday;
    /**
     * PV组件电压(V)
     */
    private Double pvVoltage;
    /**
     * PV组件电流(A)
     */
    private Double pvCurrent;
    /**
     * PV组件发电功率(W)
     */
    private Double pvPower;
}
