package com.bdyl.line.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用状态枚举
 */
@Getter
@AllArgsConstructor
public enum IotAlertStatusEnum {

    /**
     * 未恢复
     */
    NOT_RECOVERED("NOT_RECOVERED", "未恢复", "未恢复"),
    /**
     * 已恢复
     */
    RECOVERED("RECOVERED", "已恢复", "已恢复");

    /**
     * value
     */
    private final String value;
    /**
     * 名称
     */
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据枚举值获取枚举类
     *
     * @param status 枚举值
     * @return 枚举类
     */
    public static IotAlertStatusEnum fromValue(String status) {
        return IotAlertStatusEnum.valueOf(status);
    }
}
