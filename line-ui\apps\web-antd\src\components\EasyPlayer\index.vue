<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue';

import { Spin } from 'ant-design-vue';
import { IconifyIcon } from '@vben/icons';
import { useFullscreen } from '@vueuse/core';
import * as fabricJS from 'fabric';
import LocationMapModal from '../LocationMapModal/index.vue';

interface ModelRegion {
  modelCode: string;
  points: Array<{ x: number; y: number }>;
}

interface Props {
  // eslint-disable-next-line vue/no-required-prop-with-default
  id: string;
  isLive?: boolean;
  videoUrl?: string;
  showBattery?: boolean;
  batteryLevel?: number;
  points?: ModelRegion[];
  showKbps?: boolean;
  showLocation?: boolean;
  longitude?: number;
  latitude?: number;
  deviceId?: string;
  controlPosition?: string; // top  bottom
  showSpeedSelectorButton?: boolean; // 是否显示倍速选择按钮
  showPlayPauseButton?: boolean; // 是否显示播放暂停按钮
}

const props = withDefaults(defineProps<Props>(), {
  id: '',
  isLive: true,
  videoUrl: '',
  showBattery: false,
  batteryLevel: 0,
  points: () => [],
  showKbps: false,
  showLocation: true,
  longitude: 0,
  latitude: 0,
  deviceId: '',
  controlPosition: 'bottom',
  showSpeedSelectorButton: false,
  showPlayPauseButton: false,
});

const isLoading = ref(true);
const easyplayer = ref();

// 自定义控制栏相关状态
const playState = ref<boolean>(true);
const kbps = ref('');
const showControls = ref(false);
const domRef = ref<HTMLElement>();
const isRecording = ref<boolean>(false);
const showLocationModal = ref(false);
const showSpeedSelector = ref(false);
const currentSpeed = ref(1.0);
const speedOptions = [0.5, 1.0, 1.5, 2.0, 2.5, 3.0];
let hideControlsTimer: any = null;

// 全屏功能
const { isFullscreen: isDomFullscreen, enter, exit } = useFullscreen(domRef);

// 播放器配置
const config = reactive({
  isLive: props.isLive,
  bufferTime: 0.2, // 缓存时长
  stretch: false, // 是否拉伸
  hasAudio: true,
  // WCS: true,
  hls: {
    config: {
      maxBufferLength: 600,
      maxMaxBufferLength: 6000,
      enableWorker: true,
      backBufferLength: 15, // 优化回退跳转
      debug: true,
    },
  },
});

// 初始化播放器
const playCreate = () => {
  const container = document.querySelector(`#easy-player-box-${props.id}`);
  const player = new EasyPlayerPro(container, config);

  player.on('play', () => {
    // console.log('play');
    isLoading.value = false;
  });

  player.on('pause', () => {
    // console.log('pause');
  });

  player.on('timeout', () => {
    // console.log('loading timeout');
  });

  player.on('error', (e: any) => {
    console.error('player error:', e);
  });

  player.on('kBps', (e: any) => {
    handleKbps(e);
  });

  easyplayer.value = player;

  if (props.videoUrl) {
    play({
      playUrl: props.videoUrl,
    });
  }

  // setTimeout(() => {
  //   easyplayer.value && easyplayer.value.seekTime(60);
  // }, 20000);
};

// 播放
const play = ({ playUrl }: any) => {
  easyplayer.value && easyplayer.value.play(playUrl);
};

// 暂停
const pause = () => {
  easyplayer.value && easyplayer.value.pause();
};

watch(
  () => props.videoUrl,
  (newV) => {
    if (newV) {
      play({
        playUrl: newV,
      });
    }
  },
);

// 监听points变化，重新绘制线框
watch(
  () => props.points,
  () => {
    if (canvas.value) {
      drawLineCanvas();
    }
  },
  { deep: true },
);

// 原生截屏
const screenshot = () => {
  easyplayer.value && easyplayer.value.screenshot();
};

// 自定义截屏（包含线框）
const customScreenshot = async () => {
  try {
    if (!easyplayer.value || !canvas.value) {
      console.error('播放器或Canvas未初始化');
      return;
    }

    // 获取视频元素
    const videoContainer = document.querySelector(
      `#easy-player-box-${props.id}`,
    );
    const videoElement = videoContainer?.querySelector('video');

    if (!videoElement) {
      console.error('未找到视频元素');
      return;
    }

    // 创建临时canvas用于合成
    const tempCanvas = document.createElement('canvas');
    const ctx = tempCanvas.getContext('2d');

    if (!ctx) {
      console.error('无法获取Canvas上下文');
      return;
    }

    // 设置canvas尺寸为1920*1080
    tempCanvas.width = 1920;
    tempCanvas.height = 1080;

    // 绘制视频帧到canvas，缩放到1920*1080
    ctx.drawImage(videoElement, 0, 0, 1920, 1080);

    // 获取Fabric.js canvas的数据URL，设置multiplier使其适应1920*1080
    const scaleX = 1920 / containerWidth.value;
    const scaleY = 1080 / containerHeight.value;

    const fabricCanvasDataURL = canvas.value.toDataURL({
      format: 'png',
      quality: 1,
      multiplier: Math.max(scaleX, scaleY),
    });

    // 创建图片对象加载Fabric canvas内容
    const fabricImage = new Image();

    return new Promise<void>((resolve, reject) => {
      fabricImage.onload = () => {
        try {
          // 将Fabric canvas内容绘制到临时canvas上
          ctx.drawImage(fabricImage, 0, 0, 1920, 1080);

          // 转换为blob并下载
          tempCanvas.toBlob(
            (blob) => {
              if (blob) {
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `screenshot_${new Date().getTime()}.png`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                console.log('自定义截屏完成 (1920x1080)');
                resolve();
              } else {
                reject(new Error('生成截屏失败'));
              }
            },
            'image/png',
            1.0,
          );
        } catch (error) {
          console.error('合成截屏失败:', error);
          reject(error);
        }
      };

      fabricImage.onerror = () => {
        reject(new Error('加载Fabric canvas图像失败'));
      };

      fabricImage.src = fabricCanvasDataURL;
    });
  } catch (error) {
    console.error('自定义截屏失败:', error);
  }
};

// 获取流量
const handleKbps = (e: any) => {
  if (!e) {
    kbps.value = '';
    return;
  }

  if (e < 1000) {
    kbps.value = `${e} KB/s`;
  } else {
    kbps.value = `${(e / 1000).toFixed(2)} MB/s`;
  }
};

// 显示控制栏
const handleMouseMove = () => {
  showControls.value = true;
  if (hideControlsTimer) clearTimeout(hideControlsTimer);
  hideControlsTimer = setTimeout(() => {
    showControls.value = false;
  }, 5000);
};

// 点击视频区域关闭倍速选择器
const handleVideoClick = (event: MouseEvent) => {
  // 检查点击是否在倍速选择器外部
  const target = event.target as HTMLElement;
  const speedSelector = target.closest('.speed-selector-container');
  if (!speedSelector && showSpeedSelector.value) {
    showSpeedSelector.value = false;
  }
};

// 点击截屏
const handlePicture = () => {
  // 使用自定义截屏功能，包含线框
  customScreenshot();
};

// 录制
const handleRecord = () => {
  if (isRecording.value) {
    // 停止录制
    easyplayer.value && easyplayer.value.stopRecordAndSave();
    isRecording.value = false;
    console.log('录制已停止');
  } else {
    // 开始录制
    easyplayer.value && easyplayer.value.startRecord();
    isRecording.value = true;
    console.log('录制已开始');
  }
};

// 视频回放的播放暂停
const handlePlay = (type: string) => {
  if (type === 'play') {
    playState.value = true;
    if (props.videoUrl) {
      easyplayer.value && easyplayer.value.playbackResume();
    }
  } else {
    playState.value = false;
    easyplayer.value && easyplayer.value.playbackPause();
  }
};

// 打开定位弹窗
const handleLocation = () => {
  if (props.longitude && props.latitude) {
    showLocationModal.value = true;
  } else {
    console.warn('设备经纬度信息不完整，无法显示位置');
  }
};

// 切换倍速选择器显示状态
const toggleSpeedSelector = () => {
  showSpeedSelector.value = !showSpeedSelector.value;
};

// 设置播放倍速
const setPlaybackSpeed = (speed: number) => {
  currentSpeed.value = speed;
  easyplayer.value && easyplayer.value.setRate(speed);
  showSpeedSelector.value = false;
  console.log(`播放倍速已设置为: ${speed}x`);
};

onMounted(() => {
  playCreate();

  initCanvas();
});

onUnmounted(() => {
  easyplayer.value?.destroy();

  if (canvas.value) {
    canvas.value.dispose();
    canvas.value = null;
  }

  window.removeEventListener('resize', updateCanvasSize);
});

// canvas相关
const canvas = ref<any>(null);
const containerWidth = ref(0);
const containerHeight = ref(0);

// 处理精度问题
const formatNumber = (num: number) => {
  return Number(num.toFixed(2));
};

// 转换坐标到实际容器尺寸 - point的x,y是相对于容器的百分比
const convertToContainerSize = (point: { x: number; y: number }) => {
  return {
    x: formatNumber(point.x * containerWidth.value),
    y: formatNumber(point.y * containerHeight.value),
  };
};

// 初始化Canvas
const initCanvas = () => {
  nextTick(() => {
    const canvasId = `canvasDom-${props.id}`;
    const canvasElement = document.getElementById(canvasId);
    if (!canvasElement) {
      console.error('Canvas元素不存在:', canvasId);
      return;
    }

    try {
      if (canvas.value) {
        canvas.value.dispose();
      }

      canvas.value = new fabricJS.Canvas(canvasId);

      const videoContainer = document.querySelector('.video-container');
      if (videoContainer) {
        containerWidth.value = videoContainer.clientWidth;
        containerHeight.value = videoContainer.clientHeight;
        canvas.value.setWidth(containerWidth.value);
        canvas.value.setHeight(containerHeight.value);
      }

      window.addEventListener('resize', updateCanvasSize);

      drawLineCanvas();
    } catch (e) {
      console.error('Canvas初始化失败:', e);
    }
  });
};

// 渲染多模型线框
const drawLineCanvas = () => {
  if (!canvas.value) return;

  // 清除之前的线框
  canvas.value.getObjects().forEach((obj: any) => {
    if (obj.type === 'polygon' && obj.visibleLine) {
      canvas.value.remove(obj);
    }
  });

  // 绘制每个模型的区域
  (props.points || []).forEach((modelRegion: ModelRegion) => {
    if (modelRegion.points && modelRegion.points.length > 2) {
      const points = modelRegion.points.map((point) =>
        convertToContainerSize({
          x: point.x,
          y: point.y,
        }),
      );

      const polygon = new fabricJS.Polygon(points, {
        stroke: '#1890ff',
        fill: 'transparent',
        strokeWidth: 2,
        visibleLine: true,
        modelCode: modelRegion.modelCode, // 添加模型代码标识
      });

      canvas.value.add(polygon);
    }
  });

  canvas.value.requestRenderAll();
};

// 更新Canvas大小
const updateCanvasSize = () => {
  if (!canvas.value) return;

  const videoContainer = document.querySelector('.video-container');
  if (videoContainer) {
    containerWidth.value = videoContainer.clientWidth;
    containerHeight.value = videoContainer.clientHeight;
    canvas.value.setWidth(containerWidth.value);
    canvas.value.setHeight(containerHeight.value);
    canvas.value.requestRenderAll();

    if (props.points && props.points.length > 0) {
      drawLineCanvas();
    }
  }
};

defineExpose({ screenshot, customScreenshot, play, pause, canvas });
</script>

<template>
  <div
    class="video-container relative h-full w-full"
    ref="domRef"
    @mousemove="handleMouseMove"
    @click="handleVideoClick"
  >
    <Spin
      v-if="videoUrl"
      class="mySpin"
      :spinning="isLoading"
      tip="视频加载中，请稍候..."
    />

    <div
      class="absolute right-0 top-0 h-full w-full"
      :class="isLive ? 'live-player' : 'playback-player'"
      :id="`easy-player-box-${id}`"
    ></div>

    <!-- canvas -->
    <div class="canvasDom-box z-11 absolute right-0 top-0">
      <canvas :id="`canvasDom-${id}`"></canvas>
    </div>

    <!-- 自定义控制栏 -->
    <div
      class="easyplayer-custom-controls"
      v-show="showControls"
      :class="`easyplayer-custom-controls-${controlPosition}`"
    >
      <div class="easyplayer-custom-controls-left">
        <!-- 播放/暂停按钮 -->
        <div v-if="showPlayPauseButton">
          <div class="easyplayer-custom-controls-left-item" v-if="!playState">
            <IconifyIcon icon="mdi:play-circle" @click="handlePlay('play')" />
          </div>
          <div class="easyplayer-custom-controls-left-item" v-else>
            <IconifyIcon icon="mdi:pause-circle" @click="handlePlay('pause')" />
          </div>
        </div>

        <!-- 倍速选择按钮 -->
        <div
          class="easyplayer-custom-controls-left-item speed-selector-container"
          v-if="showSpeedSelectorButton"
        >
          <div class="speed-selector-button" @click="toggleSpeedSelector">
            <span class="speed-text">{{ currentSpeed }}x</span>
            <IconifyIcon
              icon="mdi:chevron-up"
              :class="{ 'speed-arrow-open': showSpeedSelector }"
              class="speed-arrow"
            />
          </div>

          <!-- 倍速选项列表 -->
          <div v-if="showSpeedSelector" class="speed-options">
            <div
              v-for="speed in speedOptions"
              :key="speed"
              class="speed-option"
              :class="{ 'speed-option-active': currentSpeed === speed }"
              @click="setPlaybackSpeed(speed)"
            >
              {{ speed }}x
            </div>
          </div>
        </div>

        <!-- 电池电量显示（集成到控制栏） -->
        <div
          v-if="showBattery"
          class="easyplayer-custom-controls-left-item battery-controls"
        >
          <div class="battery-container ml-2">
            <div class="battery-body">
              <div
                class="battery-level"
                :style="{
                  width: `${Math.max(0, Math.min(100, batteryLevel))}%`,
                }"
                :class="{
                  'battery-low': batteryLevel <= 20,
                  'battery-medium': batteryLevel > 20 && batteryLevel <= 50,
                  'battery-high': batteryLevel > 50,
                }"
              ></div>
            </div>
            <div class="battery-tip"></div>
          </div>
          <span class="battery-text">{{ Math.round(batteryLevel) }}%</span>
        </div>
      </div>

      <div class="easyplayer-custom-controls-right">
        <!-- 网络速度 -->
        <div
          class="easyplayer-custom-controls-right-item"
          v-if="props.showKbps"
        >
          {{ kbps }}
        </div>

        <!-- 定位按钮 -->
        <div
          class="easyplayer-custom-controls-right-item"
          v-if="props.showLocation && props.longitude && props.latitude"
        >
          <IconifyIcon icon="mdi:map-marker" @click="handleLocation" />
        </div>

        <!-- 截图按钮 -->
        <div class="easyplayer-custom-controls-right-item">
          <IconifyIcon icon="mdi:camera" @click="handlePicture" />
        </div>

        <!-- 录制按钮 -->
        <div class="easyplayer-custom-controls-right-item">
          <IconifyIcon
            :icon="isRecording ? 'mdi:stop-circle' : 'mdi:record-circle'"
            :class="{ 'recording-active': isRecording }"
            @click="handleRecord"
          />
        </div>

        <!-- 全屏按钮 -->
        <div
          class="easyplayer-custom-controls-right-item"
          v-if="isDomFullscreen"
        >
          <IconifyIcon icon="mdi:fullscreen-exit" @click="exit" />
        </div>
        <div class="easyplayer-custom-controls-right-item" v-else>
          <IconifyIcon icon="mdi:fullscreen" @click="enter" />
        </div>
      </div>
    </div>

    <!-- 地图定位弹窗 -->
    <LocationMapModal
      v-model:open="showLocationModal"
      :longitude="props.longitude"
      :latitude="props.latitude"
      :deviceId="props.deviceId"
    />
  </div>
</template>

<style scoped lang="scss">
:deep(.easyplayer-loading-logo) {
  display: none !important;
}

// 隐藏播放器自带的暂停时屏幕中间的播放按钮
:deep(.easyplayer-play-big) {
  display: none !important;
}

:deep(
  .easyplayer-controls .easyplayer-controls-bottom .easyplayer-controls-right
) {
  & > div:nth-child(1),
  & > div:nth-child(2),
  & > div:nth-child(3),
  & > div:nth-child(4),
  & > div:nth-child(5),
  & > div:nth-child(6),
  & > div:nth-child(7),
  & > div:nth-child(8) {
    display: none !important;
  }
}

:deep(
  .easyplayer-controls .easyplayer-controls-bottom .easyplayer-controls-left
) {
  & > div:nth-child(2) {
    display: none !important;
  }
}

.mySpin {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 2;
  transform: translate(-50%, -50%);
}

.battery-container {
  display: flex;
  align-items: center;
  gap: 1px;
}

.battery-body {
  width: 24px;
  height: 12px;
  border: 1.5px solid #fff;
  border-radius: 2px;
  position: relative;
  background: rgba(255, 255, 255, 0.1);
}

.battery-tip {
  width: 2px;
  height: 6px;
  background: #fff;
  border-radius: 0 1px 1px 0;
}

.battery-level {
  height: 100%;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.battery-high {
  background: #52c41a;
}

.battery-medium {
  background: #faad14;
}

.battery-low {
  background: #ff4d4f;
}

.battery-text {
  color: #fff;
  font-size: 12px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

// 自定义控制栏样式
.easyplayer-custom-controls {
  background-color: rgba(43, 51, 63, 0.7);
  box-sizing: border-box;
  height: 40px;
  padding: 0 13px;
  font-size: 14px;
  color: #fff;
  user-select: none;
  width: 100%;
  position: absolute;
  left: 0;
  right: 0;
  z-index: 40;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .easyplayer-custom-controls-right {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .easyplayer-custom-controls-right-item {
      padding: 0 8px;
      font-size: 24px;
      cursor: pointer;
      transition: color 0.2s ease;

      &:hover {
        color: #1890ff;
      }

      &.recording-active {
        color: #ff4d4f;
        animation: recording-pulse 1.5s infinite;
      }
    }
  }

  .easyplayer-custom-controls-left {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .easyplayer-custom-controls-left-item {
      padding: 0 8px;
      font-size: 24px;
      cursor: pointer;
      transition: color 0.2s ease;

      &:hover {
        color: #1890ff;
      }

      &.battery-controls {
        display: flex;
        align-items: center;
        gap: 6px;
        cursor: default;

        &:hover {
          color: inherit;
        }
      }

      &.speed-selector-container {
        position: relative;
        padding: 0 4px;

        .speed-selector-button {
          display: flex;
          align-items: center;
          gap: 2px;
          padding: 2px 6px;
          border-radius: 4px;
          background: rgba(255, 255, 255, 0.1);
          transition: all 0.2s ease;
          cursor: pointer;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
          }

          .speed-text {
            font-size: 12px;
            font-weight: 500;
            color: #fff;
            min-width: 24px;
            text-align: center;
          }

          .speed-arrow {
            font-size: 14px;
            color: #fff;
            transition: transform 0.2s ease;

            &.speed-arrow-open {
              transform: rotate(180deg);
            }
          }
        }

        .speed-options {
          position: absolute;
          top: 100%;
          left: 50%;
          transform: translateX(-50%);
          background: rgba(43, 51, 63, 0.95);
          border-radius: 6px;
          padding: 4px 0;
          margin-top: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
          z-index: 1000;
          min-width: 60px;

          .speed-option {
            padding: 6px 12px;
            font-size: 12px;
            color: #fff;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;

            &:hover {
              background: rgba(255, 255, 255, 0.1);
              color: #1890ff;
            }

            &.speed-option-active {
              background: #1890ff;
              color: #fff;
            }
          }
        }
      }
    }
  }
}

.easyplayer-custom-controls-bottom {
  bottom: 0;
}

.easyplayer-custom-controls-top {
  top: 0;
}

// 控制栏中的电池样式
.easyplayer-custom-controls {
  .battery-container {
    display: flex;
    align-items: center;
    gap: 1px;
  }

  .battery-body {
    width: 22px;
    height: 12px;
    border: 1px solid #fff;
    border-radius: 1px;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
  }

  .battery-tip {
    width: 1.5px;
    height: 5px;
    background: #fff;
    border-radius: 0 1px 1px 0;
  }

  .battery-level {
    height: 100%;
    border-radius: 1px;
    transition: all 0.3s ease;
  }

  .battery-text {
    color: #fff;
    font-size: 13px;
    font-weight: 500;
  }
}

// 隐藏原生控制栏（当使用自定义控制栏时）
:deep(.live-player .easyplayer-controls) {
  display: none !important;
}

// 录制动画
@keyframes recording-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// canvas
.canvasDom-box {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  pointer-events: none;

  #canvasDom {
    width: 100% !important;
    height: 100% !important;
  }
}
</style>
