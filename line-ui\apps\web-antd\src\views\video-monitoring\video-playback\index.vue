<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';
import { DatePicker, Button, message } from 'ant-design-vue';
import dayjs, { type Dayjs } from 'dayjs';

import { Page } from '@vben/common-ui';
import { useEventBus } from '@vueuse/core';

import DeviceTree from '../components/DeviceTree.vue';
import EasyPlayer from '#/components/EasyPlayer/index.vue';
import { getPlayBackFilename_Api } from '#/api/core/camera';

// import { useAccessStore } from '@vben/stores';

const baseUrl = import.meta.env.VITE_GLOB_API_URL;

// const accessStore = useAccessStore();

// 当前选中的摄像头
const selectedCamera = ref<any>(null);
const videoUrl = ref<any>('');
const loadingVideo = ref<boolean>(false);
const videoError = ref<string>('');
const cameraRegions = ref<any>([]);

// 时间范围搜索相关
const timeRange = ref<[Dayjs, Dayjs] | undefined>(undefined);
const searchLoading = ref<boolean>(false);

// 事件总线
const eventBus = useEventBus('device-tree');

// 获取摄像头视频URL
const fetchVideoUrl = async (
  camera: any,
  startTime?: string,
  endTime?: string,
) => {
  try {
    loadingVideo.value = true;
    videoError.value = '';
    videoUrl.value = '';

    console.log('Fetching video URL for camera:', camera);

    // 使用摄像头的code作为deviceId
    const deviceCode = camera.code;
    if (!deviceCode) {
      throw new Error('摄像头设备ID不存在');
    }

    // 构建请求参数
    const requestData: any = { deviceId: deviceCode };
    if (startTime && endTime) {
      requestData.startTime = startTime;
      requestData.endTime = endTime;
    }

    const response = await getPlayBackFilename_Api(requestData);

    videoUrl.value = `${baseUrl}/api/line/camera/playback/play/m3u8/${response.playbackInfoDTO.m3u8Url}`;
    cameraRegions.value = response.cameraRegions;
  } catch (error) {
    console.error('获取视频URL失败:', error);
    videoError.value =
      error instanceof Error ? error.message : '获取视频流失败';
    videoUrl.value = '';
  } finally {
    loadingVideo.value = false;
  }
};

// 时间范围搜索处理
const handleTimeRangeSearch = async () => {
  if (!selectedCamera.value) {
    message.warning('请先选择摄像头');
    return;
  }

  if (!timeRange.value || !timeRange.value[0] || !timeRange.value[1]) {
    message.warning('请选择时间范围');
    return;
  }

  const startTime = timeRange.value[0];
  const endTime = timeRange.value[1];

  // 检查时间范围是否超过24小时
  const diffHours = endTime.diff(startTime, 'hour');
  if (diffHours > 24) {
    message.error('时间范围不能超过24小时');
    return;
  }

  // 检查开始时间是否晚于结束时间
  if (startTime.isAfter(endTime)) {
    message.error('开始时间不能晚于结束时间');
    return;
  }

  try {
    searchLoading.value = true;
    const startTimeStr = startTime.format('YYYY-MM-DD HH:mm:ss');
    const endTimeStr = endTime.format('YYYY-MM-DD HH:mm:ss');

    console.log('Time range search:', { startTimeStr, endTimeStr });
    await fetchVideoUrl(selectedCamera.value, startTimeStr, endTimeStr);
  } catch (error) {
    console.error('时间范围搜索失败:', error);
  } finally {
    searchLoading.value = false;
  }
};

// 清除时间范围
const clearTimeRange = () => {
  timeRange.value = undefined;
  if (selectedCamera.value) {
    // 重新获取实时视频
    fetchVideoUrl(selectedCamera.value);
  }
};

// 监听设备树选择事件
const unsubscribe = eventBus.on((event: any) => {
  console.log('Received event from DeviceTree:', event);

  if (event.type === 'camera-selected') {
    selectedCamera.value = event.data;
    console.log('Camera selected:', event.data);

    // 调用API获取视频URL
    fetchVideoUrl(event.data);
  } else if (event.type === 'organ-selected') {
    console.log('Organ selected:', event.data);
    // 清除当前选中的摄像头
    selectedCamera.value = null;
    videoUrl.value = '';
    videoError.value = '';
    loadingVideo.value = false;
  }
});

onMounted(() => {
  console.log('Video playback page mounted');
});

onUnmounted(() => {
  // 取消事件监听
  unsubscribe();
});
</script>

<template>
  <Page class="h-full">
    <div class="flex h-full w-full gap-[10px]">
      <!-- 左侧设备树 -->
      <div
        class="bg-card flex w-[260px] flex-col gap-[10px] rounded-[var(--radius)] p-2"
      >
        <DeviceTree />
      </div>

      <!-- 右侧视频播放区域 -->
      <div class="bg-card flex flex-[1] flex-col rounded-[var(--radius)] p-2">
        <!-- 时间范围搜索 -->
        <div class="mb-4 flex items-center gap-3 rounded-lg bg-gray-50 p-3">
          <DatePicker.RangePicker
            v-model:value="timeRange"
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            :placeholder="['开始时间', '结束时间']"
            class="max-w-md flex-1"
            :disabled-date="
              (current) => current && current > dayjs().endOf('day')
            "
          />
          <Button
            type="primary"
            :loading="searchLoading"
            @click="handleTimeRangeSearch"
            :disabled="!selectedCamera || !timeRange"
          >
            查询
          </Button>
          <Button @click="clearTimeRange" :disabled="!timeRange"> 重置 </Button>
        </div>
        <!-- 视频播放器 -->
        <div class="flex-1">
          <!-- 视频播放 -->
          <EasyPlayer
            v-if="videoUrl && !loadingVideo"
            id="video-playback"
            :is-live="false"
            :video-url="videoUrl"
            :points="cameraRegions"
            :longitude="selectedCamera?.longitude"
            :latitude="selectedCamera?.latitude"
            control-position="top"
            :show-speed-selector-button="true"
            :show-play-pause-button="true"
          />

          <!-- 加载状态 -->
          <div
            v-else-if="loadingVideo"
            class="flex h-full items-center justify-center rounded-lg bg-gray-100"
          >
            <div class="text-center text-gray-500">
              <div class="mb-3 text-2xl">
                <svg
                  class="mx-auto h-8 w-8 animate-spin"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              </div>
              <div>正在获取视频流...</div>
            </div>
          </div>

          <!-- 错误状态 -->
          <!-- <div
            v-else-if="videoError"
            class="flex h-full items-center justify-center rounded-lg bg-gray-100"
          >
            <div class="text-center text-red-500">
              <div class="mb-2 text-lg">⚠️</div>
              <div class="mb-2">{{ videoError }}</div>
              <button
                v-if="selectedCamera"
                @click="fetchVideoUrl(selectedCamera)"
                class="retry-button"
              >
                重新获取
              </button>
            </div>
          </div> -->

          <!-- 选中摄像头但无视频流 -->
          <!-- <div
            v-else-if="selectedCamera"
            class="flex h-full items-center justify-center rounded-lg bg-gray-100"
          >
            <div class="text-center text-gray-500">
              <div class="mb-2 text-2xl">📹</div>
              <div class="mb-2 text-lg">该摄像头暂无可用视频流</div>
              <button
                @click="fetchVideoUrl(selectedCamera)"
                class="retry-button"
              >
                重新获取
              </button>
            </div>
          </div> -->

          <!-- 未选择摄像头 -->
          <div
            v-else
            class="flex h-full items-center justify-center rounded-lg bg-gray-100"
          >
            <div class="text-center text-gray-500">
              <div class="mb-2 text-2xl">📺</div>
              <div class="text-lg">暂无视频</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Page>
</template>

<style scoped>
.retry-button {
  @apply rounded bg-blue-500 px-4 py-2 text-sm text-white transition-colors hover:bg-blue-600;
}

.retry-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>

<style lang="scss" scoped></style>
