package com.bdyl.line.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用状态枚举
 */
@Getter
@AllArgsConstructor
public enum StatusEnum {

    /**
     * 启用
     */
    ENABLE("ENABLE", "启用", "启用"),
    /**
     * 关闭
     */
    DISABLE("DISABLE", "关闭", "关闭");

    /**
     * value
     */
    private final String value;
    /**
     * 名称
     */
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据枚举值获取枚举类
     *
     * @param status 枚举值
     * @return 枚举类
     */
    public static StatusEnum fromValue(String status) {
        return StatusEnum.valueOf(status);
    }
}
