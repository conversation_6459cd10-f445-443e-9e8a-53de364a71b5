# PowerModal 权限树国际化测试

## 实现方案

### 1. 国际化处理方式

我们采用了基于路由模块的权限树生成方式来处理国际化显示：

1. **导入路由模块**: 从 `/router/routes/modules/` 目录导入所有路由模块
2. **动态生成权限树**: 使用 `buildPermissionTreeFromRoutes` 函数从路由生成权限树
3. **自动国际化**: 路由模块中的 `$t()` 函数在导入时已经执行，标题已经是翻译后的文本

### 2. 权限树生成函数

```typescript
// 根据路由生成权限树
const buildPermissionTreeFromRoutes = (routes: any[]): TreeNode[] => {
  const result: TreeNode[] = [];

  routes.forEach(route => {
    // 只处理有meta的路由且不隐藏的路由
    if (route.meta && route.meta.title && !route.meta.hideInMenu) {
      // 构建权限ID，使用路径作为权限标识
      const permissionId = route.meta.permission || route.name || route.path;

      // 获取国际化标题（路由中的 $t() 已经执行过了）
      let title = route.meta.title;

      // 创建菜单节点
      const menuNode: TreeNode = {
        id: permissionId,
        title: title,
        key: permissionId,
      };

      // 递归处理子路由
      if (route.children && route.children.length > 0) {
        const filteredChildren = route.children.filter((child: any) =>
          child.meta && child.meta.title && !child.meta.hideInMenu
        );
        if (filteredChildren.length > 0) {
          menuNode.children = buildPermissionTreeFromRoutes(filteredChildren);
        }
      }

      result.push(menuNode);
    }
  });

  return result;
};
```

### 3. 路由模块导入

```typescript
// 导入路由模块
import baseManagementRoutes from '#/router/routes/modules/basemanagement';
import iotManagementRoutes from '#/router/routes/modules/iotmanagement';
import videoMonitoringRoutes from '#/router/routes/modules/videomonitoring';
import alarmManagementRoutes from '#/router/routes/modules/alarmmanagement';
import videoInspectionRoutes from '#/router/routes/modules/videoinspection';
import sceneManagementRoutes from '#/router/routes/modules/scenemanagement';
import dashboardRoutes from '#/router/routes/modules/dashboard';

// 所有路由模块
const allRoutes = [
  ...baseManagementRoutes,
  ...iotManagementRoutes,
  ...videoMonitoringRoutes,
  ...alarmManagementRoutes,
  ...videoInspectionRoutes,
  ...sceneManagementRoutes,
  ...dashboardRoutes,
];
```

### 4. 权限树生成

```typescript
// 根据路由生成权限树
const generatePermissionTree = () => {
  // 使用路由模块生成权限树，确保国际化正确显示
  treeData.value = buildPermissionTreeFromRoutes(allRoutes);
};
```

### 5. 测试方法

1. **切换语言测试**: 在系统中切换中英文，观察权限树标题是否正确显示
2. **权限选择测试**: 选择不同的权限节点，确保功能正常
3. **保存测试**: 保存权限配置，验证数据传输正确

### 5. 优势

1. **自动同步**: 权限树结构与路由结构自动同步，无需手动维护
2. **国际化自动处理**: 路由模块中的 `$t()` 函数在导入时已执行，标题已经是翻译后的文本
3. **动态生成**: 基于实际的路由配置动态生成，确保权限树与系统功能一致
4. **易于维护**: 新增路由时，权限树会自动包含新的菜单项
5. **类型安全**: 使用 TypeScript 确保类型安全

### 6. 注意事项

1. 路由模块中必须正确配置 `meta.title` 和国际化
2. 使用 `meta.hideInMenu` 可以控制某些路由不在权限树中显示
3. 权限ID基于路由的 `meta.permission`、`name` 或 `path` 生成
4. 确保路由模块的导入路径正确

## 使用示例

```vue
<template>
  <a-tree
    v-model:checkedKeys="formData.menuIds"
    v-model:expandedKeys="needExpandedKeys"
    checkable
    :tree-data="treeData"
    :field-names="{ title: 'title', key: 'key', children: 'children' }"
    @expand="handleExpand"
    @check="handleCheck"
  />
</template>
```

权限树会自动显示正确的国际化标题，用户可以正常进行权限选择和配置。
