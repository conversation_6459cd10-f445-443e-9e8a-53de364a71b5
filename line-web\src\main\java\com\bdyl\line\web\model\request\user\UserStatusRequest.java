package com.bdyl.line.web.model.request.user;

import jakarta.validation.constraints.NotNull;

import lombok.Data;

import com.bdyl.line.common.constant.enums.StatusEnum;

/**
 * 用户请求对象
 */
@Data
public class UserStatusRequest {
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long id;
    /**
     * 用户状态（ENABLE=启用，DISABLE=关闭） {@link com.bdyl.line.common.constant.enums.StatusEnum}
     */
    @NotNull(message = "用户状态不能为空")
    private StatusEnum status;
}
