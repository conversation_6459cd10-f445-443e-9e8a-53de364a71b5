import type { RouteRecordStringComponent } from '@vben/types';

import { requestClient } from '#/api/request';

enum Api {
  menu = '/api/uaa/permissions',
  userMenu = '/api/uaa/menu/all',
}

// 菜单查询参数
export interface MenuParams {
  page?: number;
  size?: number;
  name?: string;
  parentId?: number;
}

/**
 * 获取用户所有菜单
 */
export async function getAllMenusApi() {
  return requestClient.get<RouteRecordStringComponent[]>(Api.userMenu);
}

// 获取菜单/权限列表
export const getMenuList_Api = (
  params: MenuParams = { page: 1, size: 10000 },
) => {
  return requestClient.get(`${Api.menu}/all`, { params });
};
