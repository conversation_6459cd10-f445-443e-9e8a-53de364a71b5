package com.bdyl.line.web.model.response.inspection;

import lombok.Data;

/**
 * 巡检统计响应对象
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class InspectionStatsResponse {
    /**
     * 总计划数
     */
    private Integer totalPlans;

    /**
     * 启用计划数
     */
    private Integer activePlans;

    /**
     * 总任务数
     */
    private Integer totalTasks;

    /**
     * 已完成任务数
     */
    private Integer completedTasks;

    /**
     * 漏检任务数
     */
    private Integer missedTasks;

    /**
     * 总记录数
     */
    private Integer totalRecords;

    /**
     * 正常记录数
     */
    private Integer normalRecords;

    /**
     * 异常记录数
     */
    private Integer abnormalRecords;

    /**
     * 完成率(百分比)
     */
    private Double completionRate;

    /**
     * 正常率(百分比)
     */
    private Double normalRate;
}
