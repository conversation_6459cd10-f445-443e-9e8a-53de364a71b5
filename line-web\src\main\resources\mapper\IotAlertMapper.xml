<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bdyl.line.web.mapper.IotAlertMapper">
    <!-- 统计物联报警总数 -->
    <select id="countAll" resultType="int">
        SELECT COUNT(*) FROM t_iot_alert
    </select>

    <!-- 统计各类型物联报警数量 -->
    <select id="countByType" resultType="map">
        SELECT type, COUNT(*) AS count FROM t_iot_alert GROUP BY type
    </select>
</mapper> 