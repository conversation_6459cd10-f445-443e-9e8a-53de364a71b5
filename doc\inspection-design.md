# 巡检计划和巡检任务功能设计文档

## 1. 业务需求分析

### 1.1 核心功能
- **巡检计划管理**：用户制定巡检计划，指定巡检周期、设备列表、负责人等
- **巡检任务生成**：系统根据巡检计划自动生成巡检任务
- **巡检执行**：巡检人员根据任务执行巡检，对每个摄像头进行检查并记录
- **巡检记录管理**：存储每个摄像头的巡检详情，包括图片、结果、备注等

### 1.2 业务流程
1. 管理员创建巡检计划，指定巡检设备列表和周期
2. 系统根据计划周期自动生成巡检任务
3. 巡检人员接收任务，逐一检查任务中的摄像头
4. 对每个摄像头进行拍照、记录巡检结果和备注
5. 完成所有摄像头巡检后，任务状态变为已巡检
6. 管理员可查看巡检记录和统计数据

## 2. 数据库表设计

### 2.1 巡检计划表 (t_inspection_plan)
```sql
- id: 主键
- tenant_id: 租户ID
- organ_id: 组织ID
- name: 计划名称
- cycle_type: 巡检周期类型 (HOUR/DAY/WEEK/MONTH)
- cycle_value: 周期值 (如每2天、每周等)
- camera_ids: 设备列表 (JSON数组存储摄像头ID)
- start_date: 启动日期
- responsible_user_id: 巡检负责人ID
- responsible_user_name: 巡检负责人姓名
- status: 启用状态 (ENABLE/DISABLE)
- creator_id: 创建人ID
- updater_id: 更新人ID
- create_time: 创建时间
- update_time: 更新时间
```

### 2.2 巡检任务表 (t_inspection_task)
```sql
- id: 主键
- tenant_id: 租户ID
- organ_id: 组织ID
- plan_id: 巡检计划ID
- task_name: 任务名称
- cycle_type: 巡检周期类型
- cycle_value: 周期值
- scheduled_time: 应巡检时间
- actual_start_time: 实际开始时间
- actual_end_time: 实际结束时间
- status: 状态 (PENDING/IN_PROGRESS/COMPLETED/MISSED)
- responsible_user_id: 巡检负责人ID
- responsible_user_name: 巡检负责人姓名
- camera_count: 摄像头总数
- completed_count: 已完成巡检数量
- creator_id: 创建人ID
- updater_id: 更新人ID
- create_time: 创建时间
- update_time: 更新时间
```

### 2.3 巡检任务摄像头关联表 (t_inspection_task_camera)
```sql
- id: 主键
- tenant_id: 租户ID
- organ_id: 组织ID
- task_id: 巡检任务ID
- camera_id: 摄像头ID
- camera_name: 摄像头名称
- camera_location: 摄像头位置
- status: 巡检状态 (PENDING/COMPLETED)
- creator_id: 创建人ID
- updater_id: 更新人ID
- create_time: 创建时间
- update_time: 更新时间
```

### 2.4 巡检记录表 (t_inspection_record)
```sql
- id: 主键
- tenant_id: 租户ID
- organ_id: 组织ID
- task_id: 巡检任务ID
- task_camera_id: 任务摄像头关联ID
- camera_id: 摄像头ID
- camera_name: 摄像头名称
- camera_location: 摄像头位置
- inspection_images: 巡检图片列表 (JSON数组)
- is_normal: 是否正常 (true/false)
- remarks: 备注
- inspection_time: 巡检时间
- inspector_id: 巡检人员ID
- inspector_name: 巡检人员姓名
- creator_id: 创建人ID
- updater_id: 更新人ID
- create_time: 创建时间
- update_time: 更新时间
```

## 3. 核心设计思路

### 3.1 数据关联设计
1. **计划 -> 任务**：一个巡检计划可以生成多个巡检任务
2. **任务 -> 摄像头**：通过中间表关联任务和摄像头，便于状态管理
3. **任务摄像头 -> 巡检记录**：每个任务中的摄像头对应一条巡检记录

### 3.2 状态管理
- **计划状态**：启用/禁用
- **任务状态**：待巡检/进行中/已完成/漏检
- **任务摄像头状态**：待巡检/已完成
- **巡检结果**：正常/异常

### 3.3 任务生成策略
- 使用定时任务根据巡检计划的周期自动生成任务
- 支持多种周期类型：小时、天、周、月
- 任务生成时自动关联计划中的所有摄像头

### 3.4 巡检执行流程
1. 巡检人员查看待巡检任务列表
2. 选择任务查看摄像头列表
3. 逐一对摄像头进行巡检：
   - 查看实时画面
   - 拍照记录
   - 填写巡检结果和备注
4. 完成所有摄像头后任务自动完成

## 4. API接口设计概要

### 4.1 巡检计划相关
- POST /inspection/plan - 创建巡检计划
- GET /inspection/plan/page - 分页查询巡检计划
- PUT /inspection/plan/{id} - 更新巡检计划
- DELETE /inspection/plan/{id} - 删除巡检计划
- PUT /inspection/plan/{id}/status - 启用/禁用计划

### 4.2 巡检任务相关
- GET /inspection/task/page - 分页查询巡检任务
- GET /inspection/task/{id} - 查询任务详情
- GET /inspection/task/{id}/cameras - 查询任务摄像头列表
- PUT /inspection/task/{id}/start - 开始巡检任务
- PUT /inspection/task/{id}/complete - 完成巡检任务

### 4.3 巡检记录相关
- POST /inspection/record - 提交巡检记录
- GET /inspection/record/page - 分页查询巡检记录
- GET /inspection/record/task/{taskId} - 查询任务的所有巡检记录
- GET /inspection/record/camera/{cameraId} - 查询摄像头的巡检历史

## 5. 技术实现要点

### 5.1 定时任务
- 使用Spring的@Scheduled注解实现定时任务生成
- 支持cron表达式配置不同的生成频率

### 5.2 文件存储
- 巡检图片使用现有的ImageService进行本地存储
- 返回相对路径供前端访问

### 5.3 数据权限
- 所有表都包含tenant_id和organ_id字段
- 利用现有的数据权限控制机制

### 5.4 状态同步
- 当任务中所有摄像头巡检完成时，自动更新任务状态
- 实时更新任务的完成进度

## 6. 前端交互设计

### 6.1 巡检计划管理页面
- 计划列表展示
- 新增/编辑计划表单
- 摄像头选择组件

### 6.2 巡检任务页面
- 任务列表（支持状态筛选）
- 任务详情页面
- 摄像头巡检页面（实时预览+拍照+表单）

### 6.3 巡检记录查看
- 按任务查看记录
- 按摄像头查看历史记录
- 图片预览功能

## 7. 实现步骤规划

### 7.1 数据库脚本
1. 在release-1.0.0-changelog.yaml中添加4个新表的changeset
2. 确保所有表都包含公共字段和索引

### 7.2 后端开发
1. 创建Entity实体类（4个）
2. 创建Mapper接口和XML（4个）
3. 创建Service接口和实现类（4个）
4. 创建Controller类（3个主要控制器）
5. 创建DTO类（Request和Response）
6. 实现定时任务生成逻辑

### 7.3 前端开发
1. 巡检计划管理页面
2. 巡检任务管理页面
3. 巡检执行页面
4. 巡检记录查看页面

## 8. 数据库索引设计

### 8.1 必要索引
```sql
-- 巡检计划表
CREATE INDEX idx_inspection_plan_organ_id ON t_inspection_plan(organ_id);
CREATE INDEX idx_inspection_plan_status ON t_inspection_plan(status);
CREATE INDEX idx_inspection_plan_responsible_user_id ON t_inspection_plan(responsible_user_id);

-- 巡检任务表
CREATE INDEX idx_inspection_task_plan_id ON t_inspection_task(plan_id);
CREATE INDEX idx_inspection_task_status ON t_inspection_task(status);
CREATE INDEX idx_inspection_task_responsible_user_id ON t_inspection_task(responsible_user_id);
CREATE INDEX idx_inspection_task_scheduled_time ON t_inspection_task(scheduled_time);

-- 巡检任务摄像头关联表
CREATE INDEX idx_inspection_task_camera_task_id ON t_inspection_task_camera(task_id);
CREATE INDEX idx_inspection_task_camera_camera_id ON t_inspection_task_camera(camera_id);

-- 巡检记录表
CREATE INDEX idx_inspection_record_task_id ON t_inspection_record(task_id);
CREATE INDEX idx_inspection_record_camera_id ON t_inspection_record(camera_id);
CREATE INDEX idx_inspection_record_inspection_time ON t_inspection_record(inspection_time);
```

## 9. 业务规则和约束

### 9.1 巡检计划规则
- 同一组织下计划名称不能重复
- 启用状态的计划才会生成任务
- 计划中的摄像头列表不能为空
- 负责人必须是有效的用户

### 9.2 巡检任务规则
- 任务名称格式：计划名称 + 生成时间
- 任务的应巡检时间根据计划周期计算
- 超过应巡检时间24小时未完成的任务标记为漏检
- 任务开始后才能提交巡检记录

### 9.3 巡检记录规则
- 每个任务中的摄像头只能有一条巡检记录
- 巡检图片至少上传一张
- 巡检时间不能早于任务开始时间
- 记录提交后不可修改

## 10. 性能优化考虑

### 10.1 查询优化
- 任务列表查询使用分页和索引
- 巡检记录查询按时间范围限制
- 使用合适的JOIN避免N+1查询

### 10.2 存储优化
- 图片文件使用本地存储，数据库只存路径
- JSON字段用于存储数组数据，减少关联表
- 定期清理过期的巡检记录

### 10.3 并发控制
- 任务状态更新使用乐观锁
- 巡检记录提交使用唯一约束防重复
