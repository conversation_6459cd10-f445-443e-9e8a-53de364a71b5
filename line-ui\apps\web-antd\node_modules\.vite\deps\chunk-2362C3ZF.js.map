{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/styleChecker.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/useFlexGapSupport.js"], "sourcesContent": ["import canUseDom from './canUseDom';\nexport const canUseDocElement = () => canUseDom() && window.document.documentElement;\nconst isStyleNameSupport = styleName => {\n  if (canUseDom() && window.document.documentElement) {\n    const styleNameList = Array.isArray(styleName) ? styleName : [styleName];\n    const {\n      documentElement\n    } = window.document;\n    return styleNameList.some(name => name in documentElement.style);\n  }\n  return false;\n};\nconst isStyleValueSupport = (styleName, value) => {\n  if (!isStyleNameSupport(styleName)) {\n    return false;\n  }\n  const ele = document.createElement('div');\n  const origin = ele.style[styleName];\n  ele.style[styleName] = value;\n  return ele.style[styleName] !== origin;\n};\nexport function isStyleSupport(styleName, styleValue) {\n  if (!Array.isArray(styleName) && styleValue !== undefined) {\n    return isStyleValueSupport(styleName, styleValue);\n  }\n  return isStyleNameSupport(styleName);\n}\nlet flexGapSupported;\nexport const detectFlexGapSupported = () => {\n  if (!canUseDocElement()) {\n    return false;\n  }\n  if (flexGapSupported !== undefined) {\n    return flexGapSupported;\n  }\n  // create flex container with row-gap set\n  const flex = document.createElement('div');\n  flex.style.display = 'flex';\n  flex.style.flexDirection = 'column';\n  flex.style.rowGap = '1px';\n  // create two, elements inside it\n  flex.appendChild(document.createElement('div'));\n  flex.appendChild(document.createElement('div'));\n  // append to the DOM (needed to obtain scrollHeight)\n  document.body.appendChild(flex);\n  flexGapSupported = flex.scrollHeight === 1; // flex container should be 1px high from the row-gap\n  document.body.removeChild(flex);\n  return flexGapSupported;\n};\nexport default isStyleSupport;", "import { onMounted, shallowRef } from 'vue';\nimport { detectFlexGapSupported } from '../styleChecker';\nexport default (() => {\n  const flexible = shallowRef(false);\n  onMounted(() => {\n    flexible.value = detectFlexGapSupported();\n  });\n  return flexible;\n});"], "mappings": ";;;;;;;;;AACO,IAAM,mBAAmB,MAAM,kBAAU,KAAK,OAAO,SAAS;AACrE,IAAM,qBAAqB,eAAa;AACtC,MAAI,kBAAU,KAAK,OAAO,SAAS,iBAAiB;AAClD,UAAM,gBAAgB,MAAM,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS;AACvE,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO;AACX,WAAO,cAAc,KAAK,UAAQ,QAAQ,gBAAgB,KAAK;AAAA,EACjE;AACA,SAAO;AACT;AACA,IAAM,sBAAsB,CAAC,WAAW,UAAU;AAChD,MAAI,CAAC,mBAAmB,SAAS,GAAG;AAClC,WAAO;AAAA,EACT;AACA,QAAM,MAAM,SAAS,cAAc,KAAK;AACxC,QAAM,SAAS,IAAI,MAAM,SAAS;AAClC,MAAI,MAAM,SAAS,IAAI;AACvB,SAAO,IAAI,MAAM,SAAS,MAAM;AAClC;AACO,SAAS,eAAe,WAAW,YAAY;AACpD,MAAI,CAAC,MAAM,QAAQ,SAAS,KAAK,eAAe,QAAW;AACzD,WAAO,oBAAoB,WAAW,UAAU;AAAA,EAClD;AACA,SAAO,mBAAmB,SAAS;AACrC;AACA,IAAI;AACG,IAAM,yBAAyB,MAAM;AAC1C,MAAI,CAAC,iBAAiB,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,qBAAqB,QAAW;AAClC,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,SAAS,cAAc,KAAK;AACzC,OAAK,MAAM,UAAU;AACrB,OAAK,MAAM,gBAAgB;AAC3B,OAAK,MAAM,SAAS;AAEpB,OAAK,YAAY,SAAS,cAAc,KAAK,CAAC;AAC9C,OAAK,YAAY,SAAS,cAAc,KAAK,CAAC;AAE9C,WAAS,KAAK,YAAY,IAAI;AAC9B,qBAAmB,KAAK,iBAAiB;AACzC,WAAS,KAAK,YAAY,IAAI;AAC9B,SAAO;AACT;AACA,IAAO,uBAAQ;;;AC/Cf,IAAO,4BAAS,MAAM;AACpB,QAAM,WAAW,WAAW,KAAK;AACjC,YAAU,MAAM;AACd,aAAS,QAAQ,uBAAuB;AAAA,EAC1C,CAAC;AACD,SAAO;AACT;", "names": []}