package com.bdyl.line.web.model.response.camera;

import lombok.Data;
import lombok.RequiredArgsConstructor;

/**
 * 流地址信息DTO
 */
@Data
@RequiredArgsConstructor
public class StreamUrlInfo {

    /**
     * 流协议类型
     */
    private StreamProtocol protocol;

    /**
     * 流地址
     */
    private String url;

    /**
     * 流的MIME类型
     */
    private String mimeType;

    /**
     * 是否为主要地址
     */
    private boolean primary;

    /**
     * 额外信息
     */
    private String extra;

    /**
     * 创建一个基本的流地址信息
     *
     * @param protocol 协议类型
     * @param url 地址
     * @return 流地址信息
     */
    public static StreamUrlInfo create(StreamProtocol protocol, String url) {
        StreamUrlInfo streamUrlInfo = new StreamUrlInfo();
        streamUrlInfo.setProtocol(protocol);
        streamUrlInfo.setUrl(url);
        streamUrlInfo.setPrimary(false);
        return streamUrlInfo;
    }

    /**
     * 创建一个主要的流地址信息
     *
     * @param protocol 协议类型
     * @param url 地址
     * @return 流地址信息
     */
    public static StreamUrlInfo createPrimary(StreamProtocol protocol, String url) {
        StreamUrlInfo streamUrlInfo = new StreamUrlInfo();
        streamUrlInfo.setProtocol(protocol);
        streamUrlInfo.setUrl(url);
        streamUrlInfo.setPrimary(true);
        return streamUrlInfo;
    }

}
