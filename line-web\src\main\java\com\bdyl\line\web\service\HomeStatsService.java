package com.bdyl.line.web.service;

import java.util.List;

import com.bdyl.line.web.model.response.home.*;

/**
 * 首页统计服务接口，定义首页统计相关方法。
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface HomeStatsService {
    /**
     * 首页设备统计
     *
     * @return 设备统计数据
     */
    HomeDeviceStatsResponse getDeviceStats();

    /**
     * 首页业务报警统计
     *
     * @return 业务报警统计数据
     */
    HomeBizAlertStatsResponse getBizAlertStats();

    /**
     * 首页物联报警统计
     *
     * @return 物联报警统计数据
     */
    HomeIotAlertStatsResponse getIotAlertStats();

    /**
     * 首页业务报警按年分月类型统计
     *
     * @param year 年份
     * @return 该年每月各类型报警数量
     */
    List<HomeBizAlertMonthTypeCountResponse> getBizAlertMonthTypeStats(int year);

    /**
     * 首页按场景统计报警数量
     *
     * @param year 年份
     * @return 该年每月各场景报警数量
     */
    HomeBizAlertSceneStatsResponse getBizAlertSceneStats(int year);

    /**
     * 首页统计某年的漏检比例
     *
     * @param year 年份
     * @return 该年每月的漏检比例
     */
    List<MissInspectionMonthTypeCountResponse> getMissInspectionStats(int year);
}
