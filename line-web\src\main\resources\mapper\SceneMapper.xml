<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bdyl.line.web.mapper.SceneMapper">
    <select id="countCamerasInScenes" resultType="com.bdyl.line.web.model.response.camera.SceneCameraCountResponse">
        SELECT ts.id          AS sceneId,
               ts.name        AS sceneName,
               ts.description as sceneDesc,
               COUNT(tsc.scene_id)    AS cameraCount
        FROM t_scene ts
                 left join t_scene_camera tsc
                            on tsc.scene_id = ts.id
        GROUP BY sceneId
    </select>
</mapper>