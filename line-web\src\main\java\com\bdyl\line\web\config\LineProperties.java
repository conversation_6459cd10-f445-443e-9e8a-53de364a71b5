package com.bdyl.line.web.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 数据权限相关配置
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "line")
public class LineProperties {

    /**
     * 下载目录
     */
    private String uploadDir = "/data/srv/upload";
    /**
     * 下载目录相对路径
     */
    private String relativeUploadDir = "/srv/upload";

    /**
     * 域名
     */
    private String domain = "http://192.168.10.28:8002";
    /**
     * 认证服务地址
     */
    private String uaaUrl = "http://127.0.0.1:8180/api/uaa";

    /**
     * 中台地址
     */
    private String platformUrl = "http://192.168.3.16:8180/api/iot";

    /**
     * 国标视频地址
     */
    private String gbUrl = "http://127.0.0.1:8280/api/gb28181/v1";

    /**
     * go2rtc地址
     */
    private String rtcUrl = "http://127.0.0.1:1984";

    /**
     * 平台websocket地址
     */
    private String platformWsUrl = "ws://192.168.3.16:8180";

}
