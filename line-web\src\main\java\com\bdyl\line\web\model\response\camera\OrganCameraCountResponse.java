package com.bdyl.line.web.model.response.camera;

import java.util.List;

import lombok.Data;

/**
 * 组织树节点及摄像头数量响应
 */
@Data
public class OrganCameraCountResponse {
    /**
     * 组织id
     */
    private Long organId;
    /**
     * 组织名称
     */
    private String organName;
    /**
     * 摄像头数量
     */
    private Integer cameraCount;

    /**
     * 摄像头数量
     */
    private List<CameraResponse> cameraList;
    /**
     * 子节点
     */
    private List<OrganCameraCountResponse> children;
}
