package com.bdyl.line.web.model.request.camera;

import java.util.List;

import jakarta.validation.constraints.NotBlank;

import lombok.Data;

/**
 * 摄像头请求对象，用于创建和更新摄像头。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class CameraSynRequest {
    /**
     * 终端编码
     */
    private String edgeCode;
    /**
     * 摄像头名称
     */
    private String alias;
    /**
     * 摄像头编码
     */
    @NotBlank(message = "摄像头编码不能为空")
    private String deviceCode;

    /**
     * 摄像头通道号
     */
    private String channelId;
    /**
     * 摄像头位置
     */
    private String location;

    /**
     * 摄像头经度
     */
    private Double longitude;

    /**
     * 摄像头纬度
     */
    private Double latitude;

    /**
     * 摄像头坐标及详细地址
     */
    private String coordinates;

    /**
     * 摄像头状态 {@link com.bdyl.line.common.constant.enums.CameraStatusEnum}
     */
    private String status;

    /**
     * 描述
     */
    private String remarks;

    /**
     * 摄像头流地址
     */
    private List<String> streamUrls;
}
