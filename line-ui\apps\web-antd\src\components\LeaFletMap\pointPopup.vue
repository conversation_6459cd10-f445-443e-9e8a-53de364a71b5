<template>
  <div class="point-popup">
    <div class="title">点位信息</div>
    <div class="content">
      <div class="item">
        <span>点位名称:</span>
        <span>{{ popupData.name }}</span>
      </div>
      <div class="item">
        <span>隐患总数:</span>
        <span>{{ popupData.hazardNum || 0 }}</span>
      </div>
      <div class="item">
        <span>已整改数:</span>
        <span>{{ popupData.rectifyNum || 0 }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps(['popupData'])
</script>

<style scoped lang="scss">
.point-popup {
  width: 300px;
  .title {
    padding: 5px 0;
    border-bottom: 1px solid #eee;
  }
  .content {
    .item {
      padding: 5px 0;
      display: flex;
      > span:nth-child(1) {
        width: 60px;
        text-align: left;
        margin-right: 10px;
      }
      > span:nth-child(2) {
        flex: 1;
      }
    }
  }
}
</style>
