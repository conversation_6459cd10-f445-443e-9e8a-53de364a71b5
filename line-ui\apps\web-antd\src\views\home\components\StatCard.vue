<script setup lang="ts">
interface Props {
  title: string;
  icon?: string;
  color?: string;
  items: Array<{
    label: string;
    value: number | string;
    unit?: string;
    color?: string;
  }>;
}

const props = withDefaults(defineProps<Props>(), {
  color: '#1890ff',
});
</script>

<template>
  <div class="h-full rounded-lg border border-gray-200 bg-white p-3 shadow-sm">
    <div
      v-for="(item, index) in items"
      :key="index"
      class="flex items-center justify-between"
    >
      <span class="text-sm text-gray-600">{{ item.label }}</span>
      <div class="flex items-center">
        <span class="text-lg font-bold" :style="{ color: item.color || color }">
          {{ item.value }}
        </span>
        <span v-if="item.unit" class="ml-1 text-sm text-gray-500">
          {{ item.unit }}
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
</style>
