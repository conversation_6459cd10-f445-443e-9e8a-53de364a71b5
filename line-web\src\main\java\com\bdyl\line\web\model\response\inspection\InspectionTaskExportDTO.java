package com.bdyl.line.web.model.response.inspection;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 巡检任务响应对象
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class InspectionTaskExportDTO {

    /**
     * 任务名称
     */
    @ExcelProperty("任务名称")
    private String taskName;

    /**
     * 巡检周期类型
     */
    @ExcelProperty("巡检周期类型")
    private String cycleType;

    /**
     * 周期值
     */
    @ExcelProperty("周期值")
    private Integer cycleValue;

    /**
     * 计划开始时间
     */
    @ExcelProperty("计划开始时间")
    private LocalDateTime scheduledStartTime;

    /**
     * 计划结束时间
     */
    @ExcelProperty("计划结束时间")
    private LocalDateTime scheduledEndTime;

    /**
     * 实际开始时间
     */
    @ExcelProperty("实际开始时间")
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    @ExcelProperty("实际结束时间")
    private LocalDateTime actualEndTime;

    /**
     * 状态 {@link com.bdyl.line.common.constant.enums.InspectionStatusEnum}
     */
    @ExcelProperty("状态")
    private String status;

    /**
     * 巡检负责人姓名
     */
    @ExcelProperty("巡检负责人")
    private String responsibleUserName;

    /**
     * 摄像头总数
     */
    @ExcelProperty("摄像头总数")
    private Integer cameraCount;

    /**
     * 已完成巡检数量
     */
    @ExcelProperty("已完成数量")
    private Integer completedCount;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remarks;

}
