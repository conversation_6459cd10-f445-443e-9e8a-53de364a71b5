package com.bdyl.line.web.entity;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.bdyl.line.web.entity.handler.LongListTypeHandler;
import com.bdyl.line.web.entity.handler.TimeSlotTypeHandler;
import com.bdyl.line.web.model.dto.TimeSlotDTO;

/**
 * 巡检计划实体类，对应巡检计划表。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_inspection_plan", autoResultMap = true)
public class InspectionPlanEntity extends BaseEntity {
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 组织ID
     */
    private Long organId;

    /**
     * 计划名称
     */
    private String name;

    /**
     * 巡检周期类型 {@link com.bdyl.line.common.constant.enums.InspectionCycleEnum}
     */
    private String cycleType;

    /**
     * 周期值(如每2天、每3周等)
     */
    private Integer cycleValue;

    /**
     * 设备列表(JSON数组存储摄像头ID)
     */
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> cameraIds;

    /**
     * 启动日期
     */
    private LocalDate startDate;

    /**
     * 巡检负责人ID
     */
    private Long responsibleUserId;

    /**
     * 巡检负责人姓名
     */
    private String responsibleUserName;

    /**
     * 启用状态 {@link com.bdyl.line.common.constant.enums.StatusEnum}
     */
    private String status;

    /**
     * 计划描述
     */
    private String description;

    /**
     * 日期值(周类型:1-7表示星期几，月类型:1-31表示每月几号)
     */
    private Integer dayValue;

    /**
     * 时间段配置(小时级使用，自动转换JSON)
     */
    @TableField(typeHandler = TimeSlotTypeHandler.class)
    private List<TimeSlotDTO> timeSlots;

    /**
     * 开始时间(日/周/月类型使用)
     */
    private LocalTime startTime;

    /**
     * 结束时间(日/周/月类型使用)
     */
    private LocalTime endTime;
}
