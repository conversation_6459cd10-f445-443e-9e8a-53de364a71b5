package com.bdyl.line.web.timer;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.bdyl.boot.tenant.Tenant;
import com.bdyl.boot.tenant.TenantContext;
import com.bdyl.boot.tenant.TenantContextHolder;
import com.bdyl.line.web.service.InspectionTaskService;
import com.bdyl.line.web.utils.LineUserContext;

/**
 * 巡检相关定时任务
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InspectionTimer {

    /**
     * 巡检任务service
     */
    private final InspectionTaskService inspectionTaskService;

    /**
     * 每10分钟执行一次任务生成,检查是否有需要生成的巡检任务
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    public void generateInspectionTasks() {
        try {
            LineUserContext.setSkipPermission(true);
            TenantContextHolder.setTenantContext(new TenantContext(new Tenant("1", "1"), false));
            log.info("开始执行巡检任务生成定时任务");
            inspectionTaskService.generateTasks();
            log.info("巡检任务生成定时任务执行完成");
        } catch (Exception e) {
            log.error("巡检任务生成定时任务执行失败", e);
        } finally {
            LineUserContext.clear();
            TenantContextHolder.resetTenantContext();
        }
    }

    /**
     * 每30分钟执行一次漏检检查 检查是否有任务超时需要标记为漏检
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    public void handleMissedTasks() {
        try {
            LineUserContext.setSkipPermission(true);
            TenantContextHolder.setTenantContext(new TenantContext(new Tenant("1", "1"), false));

            log.info("开始执行漏检任务处理定时任务");
            inspectionTaskService.handleMissedTasks();
            log.info("漏检任务处理定时任务执行完成");
        } catch (Exception e) {
            log.error("漏检任务处理定时任务执行失败", e);
        } finally {
            LineUserContext.clear();
            TenantContextHolder.resetTenantContext();
        }
    }
}
