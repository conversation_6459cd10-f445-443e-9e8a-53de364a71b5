<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from 'vue';

import { Page } from '@vben/common-ui';

import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';

import { getTerminalList_Api, exportTerminal_Api } from '#/api/core/terminal';
import { getOrganTreeByTerminal_Api } from '#/api/core';
import tableComp from '#/components/TableComp/table.vue';

import EditModal from './components/EditModal.vue';
import PlayVideo from './playVideo.vue';

import { useDictStore } from '#/store';
import { downloadFileFromBlob } from '@vben/utils';

const router = useRouter();

// 组织树数据
const treeData = ref<any>([]);
const expandedKeys = ref<string[]>([]);

const dictStore = useDictStore();

// 查询条件
const queryParam = reactive<any>({
  name: undefined, // 终端名称
  code: undefined, // 终端编码
  status: undefined, // 终端状态
  boxStatus: undefined, // 设备箱状态
  organId: undefined, // 组织id
  page: 1,
  size: 10,
});

// const areaVal = ref('按组织');

const loading = ref(false);
const modalFormRef = ref();
const modalVideoRef = ref();
const selectedKeys = ref<string[]>([]);
const searchValue = ref<string>('');

// 表格数据源
const tableData = reactive<any>({
  data: {},
});

// 表格相关配置
const columns = [
  { title: '终端名称', dataIndex: 'name' },
  { title: '终端编码', dataIndex: 'code' },
  { title: '位置', dataIndex: 'location' },
  { title: '终端状态', dataIndex: 'status' },
  { title: '设备箱状态', dataIndex: 'boxStatus' },
  {
    title: '蓄电池电量',
    dataIndex: 'batteryLevel',
    customRender: ({ record }: { record: any }) => {
      return (record.batteryLevel || 0) + '%';
    },
  },
  { title: '操作', dataIndex: 'operation', width: '200px' },
];

// 设备箱状态选项
const boxStatusOptions = dictStore.getDictOptions('DeviceBoxStatusEnum');

// 终端状态选项
const terminalStatusOptions = dictStore.getDictOptions('TerminalStatusEnum');

// 加载终端列表
const loadTerminalList = async () => {
  loading.value = true;
  try {
    const res = await getTerminalList_Api(queryParam);

    // 更新表格数据源
    const temp = {
      data: res.data,
      page: res.page,
      size: res.size,
      total: res.total,
    };
    tableData.data = temp;
  } catch (error) {
    console.error('获取终端列表失败', error);
    message.error('获取终端列表失败');
  } finally {
    loading.value = false;
  }
};
const success = (data: any) => {
  queryParam.page = data.pi;
  queryParam.size = data.ps;
  loadTerminalList();
};

// 查询
const searchTable = () => {
  queryParam.page = 1;
  loadTerminalList();
};

// 重置
const resetTable = () => {
  queryParam.name = undefined;
  queryParam.code = undefined;
  queryParam.status = undefined;
  queryParam.boxStatus = undefined;
  queryParam.page = 1;
  queryParam.size = 10;
  loadTerminalList();
};

// 视频播放
const handleVideo = (record: any) => {
  modalVideoRef.value.openModal(record);
};

// 详情
const handleInfo = (record: any) => {
  router.push('/iot-management/terminal-Infor-management');
  localStorage.setItem(
    'terminalTemp',
    JSON.stringify({
      terminalId: record.id,
      terminalCode: record.code,
      terminalName: record.name,
      terminalStatus: record.status,
    }),
  );
};

// 监听树选择变化
watch(selectedKeys, () => {
  queryParam.organId = selectedKeys.value[0] || undefined;
  if (Number(queryParam.organId)) {
    loadTerminalList(); // 当选择的部门变化时重新加载用户列表
  }
});

// 递归处理树形数据，设置id为null时使用name作为id
const processTreeData = (nodes: any[]): any[] => {
  if (!Array.isArray(nodes)) return [];

  return nodes.map((node) => {
    // 如果id为null，使用name作为id
    if (node.id === null || node.id === undefined) {
      node.id = node.name;
      node.hasOrgan = false;
    } else {
      node.hasOrgan = true;
    }

    // 如果有子节点，递归处理
    if (node.children && Array.isArray(node.children)) {
      node.children = processTreeData(node.children);
    }

    return node;
  });
};

// 获取组织树
const getTreeData = async () => {
  const res = await getOrganTreeByTerminal_Api();
  // 处理树形数据，确保所有节点都有有效的id
  const processedData = processTreeData([res]);
  treeData.value = processedData;
};

const autoExpandParent = ref<boolean>(true);
const onExpand = (keys: string[]) => {
  expandedKeys.value = keys;
  autoExpandParent.value = false;
};

// 递归搜索树节点
const searchTreeNode = (
  nodes: any[],
  searchText: string,
  parentIds: string[] = [],
) => {
  for (const node of nodes) {
    if (node.name.includes(searchText)) {
      selectedKeys.value = [node.id];
      expandedKeys.value = [...parentIds];
      return true;
    }
    if (node.children && node.children.length) {
      const found = searchTreeNode(node.children, searchText, [
        ...parentIds,
        node.id,
      ]);
      if (found) return true;
    }
  }
  return false;
};

// 组织搜索
const handleSearch = () => {
  if (!searchValue.value.trim()) {
    selectedKeys.value = [];
    expandedKeys.value = [];
    return;
  }
  searchTreeNode(treeData.value, searchValue.value);
  autoExpandParent.value = true;
};

// 终端保存成功回调
const handleSaveSuccess = () => {
  loadTerminalList();
};

// 导出终端
const handleExport = async () => {
  const params = {
    name: queryParam.name,
    code: queryParam.code,
    status: queryParam.status,
    boxStatus: queryParam.boxStatus,
    organId: queryParam.organId,
  };
  const res = await exportTerminal_Api(params);
  downloadFileFromBlob({ source: res, fileName: '终端数据.xlsx' });
};

onMounted(() => {
  loadTerminalList();
  getTreeData();
});
</script>

<template>
  <Page>
    <div class="wrap_con">
      <div class="left_cont">
        <a-card>
          <!-- <a-segmented
            block
            v-model:value="areaVal"
            :options="['按组织', '按场景']"
          /> -->
          <a-input-search
            v-model:value="searchValue"
            placeholder="搜索区域名称"
            class="mb-2 mt-2"
            @search="handleSearch"
          />
          <a-tree
            v-model:selectedKeys="selectedKeys"
            :expandedKeys="expandedKeys"
            :tree-data="treeData"
            @expand="onExpand"
            :field-names="{ children: 'children', title: 'name', key: 'id' }"
          >
            <template #title="data">
              <div class="flex items-center gap-[5px] pl-[2px] pr-[5px]">
                <span :class="{ noOrgan: !data.hasOrgan }"
                  >{{ data.name }} ({{ data.terminalCount }})</span
                >
              </div>
            </template>
          </a-tree>
        </a-card>
      </div>
      <div class="right_cont">
        <a-card class="table_header_search mb-5">
          <a-row :gutter="20">
            <a-col :span="6">
              <label>终端名称：</label>
              <div class="table_header_wrp_cont">
                <a-input
                  v-model:value="queryParam.name"
                  allow-clear
                  placeholder="请输入终端名称"
                />
              </div>
            </a-col>
            <!-- <a-col :span="6">
              <label>终端编码：</label>
              <div class="table_header_wrp_cont">
                <a-input
                  v-model:value="queryParam.code"
                  allow-clear
                  placeholder="请输入终端编码"
                />
              </div>
            </a-col> -->
            <a-col :span="6">
              <label>终端状态：</label>
              <div class="table_header_wrp_cont">
                <a-select
                  v-model:value="queryParam.status"
                  allow-clear
                  placeholder="请选择状态"
                  style="width: 100%"
                  :options="terminalStatusOptions"
                  :field-names="{
                    label: 'dictLabel',
                    value: 'dictValue',
                  }"
                />
              </div>
            </a-col>
            <a-col :span="7">
              <label>设备箱状态：</label>
              <div class="table_header_wrp_cont">
                <a-select
                  v-model:value="queryParam.boxStatus"
                  allow-clear
                  placeholder="请选择状态"
                  style="width: 100%"
                  :options="boxStatusOptions"
                  :field-names="{
                    label: 'dictLabel',
                    value: 'dictValue',
                  }"
                />
              </div>
            </a-col>
            <a-col :span="4">
              <a-space>
                <a-button type="primary" class="searchBtn" @click="searchTable">
                  查询
                </a-button>
                <a-button class="refBtn" @click="resetTable">重置</a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-card>

        <a-card size="small">
          <div class="table_action_btn_wrp">
            <!-- <a-button class="addBtn" type="primary" @click="handleAdd">
              新建
            </a-button> -->
            <a-button class="addBtn" @click="handleExport"> 导出 </a-button>
          </div>

          <table-Comp
            :columns="columns"
            :data-source="tableData.data"
            :loading="loading"
            :scroll="{ x: 1200 }"
            @is-loading-fuc="(e) => (loading = e)"
            @success="success"
          >
            <!-- 位置列 -->
            <template #location="{ record }">
              <a-tooltip
                v-if="record.location && record.location.length > 10"
                :title="record.location"
              >
                <span>{{ record.location.substring(0, 10) }}...</span>
              </a-tooltip>
              <span v-else>{{ record.location || '-' }}</span>
            </template>
            <!-- 终端状态列 -->
            <template #status="{ record }">
              <a-tag
                :color="record.status === 'NORMAL' ? 'success' : 'error'"
                >{{
                  dictStore.getDictLable('TerminalStatusEnum', record.status)
                }}</a-tag
              >
            </template>

            <!-- 设备箱状态列 -->
            <template #boxStatus="{ record }">
              <a-tag
                :color="record.boxStatus === 'NORMAL' ? 'success' : 'error'"
              >
                {{
                  dictStore.getDictLable(
                    'DeviceBoxStatusEnum',
                    record.boxStatus,
                  )
                }}
              </a-tag>
            </template>

            <!-- 操作列 -->
            <template #operation="{ record }">
              <!-- <a-button type="link" @click="handleEdit(record)">编辑</a-button> -->
              <a-button type="link" @click="handleInfo(record)">详情</a-button>
              <a-button type="link" @click="handleVideo(record)"
                >实时视频</a-button
              >
              <!-- <a-popconfirm
                title="数据删除后不可恢复，请确认后继续?"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" danger>删除</a-button>
              </a-popconfirm> -->
            </template>
          </table-Comp>
        </a-card>
      </div>
    </div>
    <!-- 终端编辑弹窗 -->
    <EditModal ref="modalFormRef" @success="handleSaveSuccess" />

    <!-- 视频播放弹窗 -->
    <PlayVideo ref="modalVideoRef" />
  </Page>
</template>
<style scoped lang="scss">
.wrap_con {
  position: relative;
}
.left_cont {
  width: 260px;
  max-height: 90vh;
  overflow: auto;
  :deep(.ant-card-body) {
    padding: 10px !important;
  }
}
.right_cont {
  width: calc(100% - 270px);
  position: absolute;
  top: 0;
  right: 0;
}

.noOrgan {
  color: #928f8f;
}
</style>
