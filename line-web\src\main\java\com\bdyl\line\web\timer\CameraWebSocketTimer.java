package com.bdyl.line.web.timer;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.bdyl.boot.tenant.Tenant;
import com.bdyl.boot.tenant.TenantContext;
import com.bdyl.boot.tenant.TenantContextHolder;
import com.bdyl.line.web.config.LineProperties;
import com.bdyl.line.web.entity.CameraEntity;
import com.bdyl.line.web.service.CameraService;
import com.bdyl.line.web.utils.LineUserContext;
import com.bdyl.line.web.ws.CameraWebSocketClient;

/**
 * 摄像头WebSocket连接管理定时任务，动态维护所有摄像头的WebSocket连接。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CameraWebSocketTimer {
    /**
     * 摄像头服务
     */
    private final CameraService cameraService;
    /**
     * 当前所有活跃的WebSocket客户端连接
     */
    private final Map<String, CameraWebSocketClient> clientMap = new ConcurrentHashMap<>();

    /**
     * 配置类
     */
    private final LineProperties lineProperties;

    /**
     * 每分钟同步一次摄像头列表，动态增删WebSocket连接
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void syncCameraConnections() {
        try {
            LineUserContext.setSkipPermission(true);
            TenantContextHolder.setTenantContext(new TenantContext(new Tenant("1", "1"), false));
            log.info("[CameraWebSocketManager] 开始同步摄像头WebSocket连接");
            List<CameraEntity> cameraEntityList = cameraService.list();
            List<String> latestCodes = cameraEntityList.stream().map(CameraEntity::getCode).toList();
            Set<String> latestSet = new HashSet<>(latestCodes);

            // 新增连接
            for (String code : latestSet) {
                if (!clientMap.containsKey(code)) {
                    CameraWebSocketClient client = new CameraWebSocketClient(code, lineProperties.getPlatformWsUrl());
                    client.connect();
                    clientMap.put(code, client);
                }
            }
            // 移除已删除的连接
            for (Iterator<Map.Entry<String, CameraWebSocketClient>> it = clientMap.entrySet().iterator();
                it.hasNext();) {
                Map.Entry<String, CameraWebSocketClient> entry = it.next();
                if (!latestSet.contains(entry.getKey())) {
                    entry.getValue().close(); // 主动关闭client，client内部不会再重连
                    it.remove(); // 移除map引用，便于GC回收
                }
            }
        } catch (Exception e) {
            log.error("[CameraWebSocketManager] 同步摄像头WebSocket连接异常", e);
        } finally {
            LineUserContext.clear();
            TenantContextHolder.resetTenantContext();
        }
    }
}
