package com.bdyl.line.web.entity.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import com.bdyl.line.web.model.dto.TimeSlotDTO;
import com.bdyl.line.web.utils.TimeSlotUtils;

/**
 * 时间段列表的 MyBatis TypeHandler 自动在数据库 JSON 字符串和 Java 对象之间转换
 *
 * <AUTHOR>
 * @since 1.0
 */
@MappedTypes(List.class)
@MappedJdbcTypes({JdbcType.VARCHAR, JdbcType.CLOB})
public class TimeSlotTypeHandler extends BaseTypeHandler<List<TimeSlotDTO>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<TimeSlotDTO> parameter, JdbcType jdbcType)
        throws SQLException {
        // Java 对象转 JSON 字符串存入数据库
        String json = TimeSlotUtils.toJson(parameter);
        ps.setString(i, json);
    }

    @Override
    public List<TimeSlotDTO> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        // 从数据库读取 JSON 字符串转 Java 对象
        String json = rs.getString(columnName);
        return TimeSlotUtils.fromJson(json);
    }

    @Override
    public List<TimeSlotDTO> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        // 从数据库读取 JSON 字符串转 Java 对象
        String json = rs.getString(columnIndex);
        return TimeSlotUtils.fromJson(json);
    }

    @Override
    public List<TimeSlotDTO> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        // 从数据库读取 JSON 字符串转 Java 对象
        String json = cs.getString(columnIndex);
        return TimeSlotUtils.fromJson(json);
    }
}
