import { requestClient } from '#/api/request';

enum Api {
  organ = '/api/line/organ',
}

// 分页获取组织列表
export const getOrganList_Api = (params: any) => {
  return requestClient.get(`${Api.organ}/page`, { params });
};

// 创建组织
export const addOrgan_Api = (data: any) => {
  return requestClient.post(`${Api.organ}`, data);
};

// 修改组织
export const updateOrgan_Api = (data: any) => {
  return requestClient.put(`${Api.organ}`, data);
};

// 删除组织
export const deleteOrgan_Api = (id: any) => {
  return requestClient.delete(`${Api.organ}/${id}`);
};

// 获取组织详情
export const getOrgan_Api = (id: any) => {
  return requestClient.get(`${Api.organ}/${id}`);
};

// 获取行政区域
export const getRegion_Api = (params: any) => {
  return requestClient.get(`/api/line/region/list`, { params });
};

// 获取组织树
export const getOrganTree_Api = (params: any) => {
  return requestClient.get(`/api/line/user/tree/by/organ`, { params });
};

// 导出
export const exportOrgan_Api = (params: any) => {
  return requestClient.download(`${Api.organ}/export`, { params });
};