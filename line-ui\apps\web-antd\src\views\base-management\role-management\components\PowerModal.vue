<script lang="ts" setup>
import { ref, reactive, nextTick, watch, onMounted } from 'vue';

import { message } from 'ant-design-vue';

import { getMenuList_Api } from '#/api/core/menu';
import {
  getRolePermissions_Api,
  assignRolePermissions_Api,
} from '#/api/core/role';
// 注释掉 $t 导入，因为路由模块中的翻译在导入时已经执行
import { $t } from '#/locales';

// 导入路由模块
import baseManagementRoutes from '#/router/routes/modules/basemanagement';
import iotManagementRoutes from '#/router/routes/modules/iotmanagement';
import videoMonitoringRoutes from '#/router/routes/modules/videomonitoring';
import alarmManagementRoutes from '#/router/routes/modules/alarmmanagement';
import videoInspectionRoutes from '#/router/routes/modules/videoinspection';
import sceneManagementRoutes from '#/router/routes/modules/scenemanagement';
// import homeRoutes from '#/router/routes/modules/home';

interface TreeNode {
  title: string;
  key: string;
  id: string;
  children?: TreeNode[];
}

interface FormModel {
  name?: string;
  code?: string;
  menuIds?: any[];
}

const emit = defineEmits(['success']);

const open = ref<boolean>(false);
const modalTitle = ref<string>('');
const recordId = ref<any>();
const formRef = ref();
const loading = ref(false);
// 菜单树数据
const treeData = ref<TreeNode[]>([]);
// const checkedAllIds = ref<any[]>([]); // tree 选中的值并包含他父级所有的ids

// 所有路由模块
const allRoutes = [
  // ...homeRoutes,
  ...videoMonitoringRoutes,
  ...alarmManagementRoutes,
  ...sceneManagementRoutes,
  ...videoInspectionRoutes,
  ...iotManagementRoutes,
  ...baseManagementRoutes,
];
const isAllSelect = ref<boolean>(false);
const isAllExpanded = ref<boolean>(false);
const needExpandedKeys = ref<string[]>([]);

// 表单数据
const formData = reactive<FormModel>({
  name: undefined,
  code: undefined,
  menuIds: [],
});

// 表单验证规则
const formRules = {
  name: [{ required: false, message: '' }],
  code: [{ required: false, message: '' }],
  menuIds: [{ required: true, message: '请选择菜单权限' }],
};

// 根据路由生成权限树
const buildPermissionTreeFromRoutes = (routes: any[]): TreeNode[] => {
  const result: TreeNode[] = [];

  routes.forEach((route) => {
    // 只处理有meta的路由且不隐藏的路由
    if (route.meta && route.meta.title && !route.meta.hideInMenu) {
      // 构建权限ID，使用路径作为权限标识
      const permissionCode =
        route.meta.authority &&
        Array.isArray(route.meta.authority) &&
        route.meta.authority.length > 0
          ? route.meta.authority[0]
          : `line:menu:${route.path}`;

      // 获取国际化标题
      let title = $t(route.meta.title);

      // 创建菜单节点
      const menuNode: TreeNode = {
        id: permissionCode,
        title: title,
        key: permissionCode,
      };

      // 处理子路由
      if (route.children && route.children.length > 0) {
        const filteredChildren = route.children.filter(
          (child: any) =>
            child.meta &&
            child.meta.title &&
            !child.meta.hideInMenu &&
            !child.children &&
            child.meta.authority,
        );
        if (filteredChildren.length > 0) {
          menuNode.children = buildPermissionTreeFromRoutes(filteredChildren);
        }
      }

      result.push(menuNode);
    }
  });

  return result;
};

// 根据路由生成权限树
const generatePermissionTree = () => {
  // 使用路由模块生成权限树，确保国际化正确显示
  treeData.value = buildPermissionTreeFromRoutes(allRoutes);
};

const allPermissions = ref<any>([]);

// 获取菜单列表（保留原有API调用，用于获取后端菜单数据）
const loadMenuList = async () => {
  try {
    const res = await getMenuList_Api();
    allPermissions.value = res || [];
  } catch (error) {
    console.error('获取菜单列表失败', error);
    message.error('获取菜单列表失败');
  }
};

// 获取树中所有父级节点的ID
const getParentNodeIds = (tree: TreeNode[]): Set<string> => {
  const parentIds = new Set<string>();

  const traverse = (nodes: TreeNode[]) => {
    nodes.forEach(node => {
      if (node.children && node.children.length > 0) {
        parentIds.add(node.id);
        traverse(node.children);
      }
    });
  };

  traverse(tree);
  return parentIds;
};

// 过滤掉父级权限，只保留叶子节点权限用于回显
const filterLeafPermissions = (permissionCodes: string[]): string[] => {
  const parentIds = getParentNodeIds(treeData.value);
  return permissionCodes.filter(code => !parentIds.has(code));
};

// 获取角色权限
const loadRolePermissions = async (roleId: number) => {
  try {
    const res = await getRolePermissions_Api(roleId);
    const allPermissionCodes = (res || []).map((item: any) => item.code);
    // 只回显叶子节点权限，避免父子联动导致的显示问题
    formData.menuIds = filterLeafPermissions(allPermissionCodes);
  } catch (error) {
    console.error('获取角色权限失败', error);
    message.error('获取角色权限失败');
  }
};
// 重置表单
const resetForm = async () => {
  await formRef.value?.resetFields();
  Object.assign(formData, {
    name: undefined,
    code: undefined,
    menuIds: [],
  });
  isAllSelect.value = false;
  isAllExpanded.value = false;
  needExpandedKeys.value = [];
};

// 打开弹窗
const openModal = async (record: any) => {
  open.value = true;
  modalTitle.value = `菜单权限配置 - ${record.name}`;
  await resetForm();

  if (record) {
    recordId.value = record.id;
    nextTick(() => {
      // 填充角色信息
      formData.name = record.name;
      formData.code = record.code;
    });

    // 加载角色权限
    await loadRolePermissions(record.id);
  }
};
// 关闭弹窗
const closeModal = () => {
  open.value = false;
  resetForm();
};

// 获取所有相关的权限ID（包括父级）
const getAllRelatedPermissionIds = (selectedCodes: string[]): string[] => {
  const allRelatedCodes = new Set<string>();

  // 递归函数：查找节点并添加其所有父级路径
  const findNodeAndAddParents = (nodeCode: string, tree: TreeNode[], parentPath: string[] = []): boolean => {
    for (const node of tree) {
      const currentPath = [...parentPath, node.id];

      if (node.id === nodeCode) {
        // 找到目标节点，添加整个路径（包括所有父级和自身）
        currentPath.forEach(id => allRelatedCodes.add(id));
        return true;
      }

      if (node.children && node.children.length > 0) {
        // 在子树中递归查找
        if (findNodeAndAddParents(nodeCode, node.children, currentPath)) {
          return true;
        }
      }
    }
    return false;
  };

  // 为每个选中的权限码查找并添加其父级路径
  selectedCodes.forEach(code => {
    findNodeAndAddParents(code, treeData.value);
  });

  return Array.from(allRelatedCodes);
};

// 保存权限配置
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;

    // 获取所有相关的权限码（包括父级）
    const allRelatedCodes = getAllRelatedPermissionIds(formData.menuIds || []);

    // 根据权限码获取对应的权限ID
    const permissionIds = allPermissions.value
      .filter((item: any) => allRelatedCodes.includes(item.code))
      .map((item: any) => item.id);

    const permissionData = {
      roleId: recordId.value,
      permissionIds: permissionIds || [],
    };

    await assignRolePermissions_Api(recordId.value, permissionData);
    message.success('权限配置成功');

    closeModal();
    emit('success');
  } catch (error) {
    console.error('保存权限配置失败', error);
  } finally {
    loading.value = false;
  }
};
// 组装前端传后端的tree数据 因为后台需要的ids和我前端tree展示勾选获取ids不一样
// 选中子数据 默认把他父级及所有父级的id一并获取
const treeIterator = (tree: TreeNode[], func: (node: TreeNode) => void) => {
  tree.forEach((node) => {
    func(node);
    node.children && treeIterator(node.children, func);
  });
};

const targetData: Record<string, any> = {};

const loops = (data: TreeNode[] = [], parent?: any) => {
  return data.map(({ children, id: value }) => {
    const node: any = {
      value,
      parent,
    };
    targetData[value] = node;
    if (children) {
      node.children = loops(children, node);
    }
    return node;
  });
};

const getNode = (value: string): number[] => {
  let node: number[] = [];
  let currentNode = targetData[value];
  node.push(Number(currentNode.value));
  if (currentNode.parent) {
    node = [...getNode(currentNode.parent.value), ...node];
  }
  return node;
};
// 获取树节点ID的工具函数
const getTreeNodes = (
  tree: TreeNode[],
  options = { onlyParent: false },
): string[] => {
  const ids: string[] = [];
  const traverse = (nodes: TreeNode[]) => {
    nodes.forEach((node) => {
      // 如果onlyParent为true，只收集有子节点的节点
      // 如果onlyParent为false，收集所有节点
      if (!options.onlyParent || (node.children && node.children.length > 0)) {
        ids.push(node.id);
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  traverse(tree);
  return ids;
};
// 全选/全不选
const changeAllSelect = (val: boolean) => {
  if (val) {
    formData.menuIds = getTreeNodes(treeData.value);
  } else {
    formData.menuIds = [];
  }
};
// 全部展开/折叠
const changeAllExpanded = (val: boolean) => {
  if (val) {
    needExpandedKeys.value = getTreeNodes(treeData.value, { onlyParent: true });
  } else {
    needExpandedKeys.value = [];
  }
};
// 处理展开/折叠事件
const handleExpand = (expandedKeys: any) => {
  needExpandedKeys.value = expandedKeys;
};

// 处理节点选中状态变化
const handleCheck = (checkedKeys: any) => {
  formData.menuIds = checkedKeys;
};

defineExpose({ openModal }); // 提供 open 方法，用于打开弹窗
onMounted(() => {
  // 初始化权限树
  generatePermissionTree();
  // 加载菜单列表
  loadMenuList();
});
</script>
<template>
  <div>
    <a-modal
      v-model:open="open"
      :title="modalTitle"
      width="600px"
      :mask-closable="false"
      @ok="handleSubmit"
    >
      <a-form
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        :model="formData"
        :rules="formRules"
      >
        <a-row>
          <a-col :span="24">
            <a-form-item label="角色名称" name="name">
              <a-tag color="processing">{{ formData.name }}</a-tag>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="角色标识" name="code">
              <a-tag color="blue">{{ formData.code }}</a-tag>
            </a-form-item>
          </a-col>
          <div
            class="p-2"
            style="border: 1px solid #ddd; margin-left: 25%; width: 66.7%"
          >
            全选/全不选
            <a-switch
              checked-children="全选"
              un-checked-children="否"
              v-model:checked="isAllSelect"
              @change="changeAllSelect"
            />&emsp; 全部展开/折叠
            <a-switch
              checked-children="展开"
              un-checked-children="折叠"
              v-model:checked="isAllExpanded"
              @change="changeAllExpanded"
            />
          </div>
          <a-col :span="24">
            <a-form-item label="菜单权限" name="menuIds">
              <div
                style="border: 1px solid #ddd; height: 400px; overflow: auto"
              >
                <a-tree
                  v-model:checkedKeys="formData.menuIds"
                  v-model:expandedKeys="needExpandedKeys"
                  checkable
                  :tree-data="treeData"
                  :field-names="{
                    title: 'title',
                    key: 'key',
                    children: 'children',
                  }"
                  @expand="handleExpand"
                  @check="handleCheck"
                >
                </a-tree>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>
