package com.bdyl.line.web.event;

import org.springframework.context.ApplicationEvent;

import com.bdyl.line.web.ws.WebsocketMessage;

/**
 * 摄像头消息事件
 *
 * <AUTHOR>
 * @since 1.0
 */
public class CameraMessageEvent extends ApplicationEvent {
    /**
     * 摄像头编号
     */
    private final String cameraCode;
    /**
     * websocket消息体
     */
    private final WebsocketMessage message;

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     * @param cameraCode 摄像头编号
     * @param message websocket消息体
     */
    public CameraMessageEvent(Object source, String cameraCode, WebsocketMessage message) {
        super(source);
        this.cameraCode = cameraCode;
        this.message = message;
    }

    public String getCameraCode() {
        return cameraCode;
    }

    public WebsocketMessage getMessage() {
        return message;
    }
}
