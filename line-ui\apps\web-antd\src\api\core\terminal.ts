import { requestClient } from '#/api/request';

enum Api {
  terminal = '/api/line/terminal',
  solar = '/api/line/solar-panel',
}

// 分页查询终端
export interface TerminalParams {
  page: number;
  size: number;
  organId?: number; // 组织id
  name?: string; // 终端名称
  code?: string; // 终端编码
  status?: string; // 终端状态 ，字典TerminalStatusEnum
  boxStatus?: string; // 设备箱状态，字典DeviceBoxStatusEnum
}
// 分页获取终端列表
export const getTerminalList_Api = (params: TerminalParams) => {
  return requestClient.get(`${Api.terminal}/page`, { params });
};

// 获取终端列表
export const getTerminals_Api = (params: any) => {
  return requestClient.get(`${Api.terminal}/list`, { params });
};

// 创建终端
export interface TerminalParameter {
  id?: number;
  name: string; // 终端名称
  code: string; // 终端编码
  location: string; // 终端位置
  status: string; // 终端状态 ，字典TerminalStatusEnum
  batteryLevel: number; // 蓄电池电量
  boxStatus: string; // 设备箱状态 ，字典DeviceBoxStatusEnum
}
export const addTerminal_Api = (data: TerminalParameter) => {
  return requestClient.post(`${Api.terminal}`, data);
};

// 修改终端
export const updateTerminal_Api = (id: any, data: TerminalParameter) => {
  return requestClient.put(`${Api.terminal}/${id}`, data);
};

// 删除终端
export const deleteTerminal_Api = (id: any) => {
  return requestClient.delete(`${Api.terminal}/${id}`);
};

// 获取终端详情
export const getTerminal_Api = (id: any) => {
  return requestClient.get(`${Api.terminal}/${id}`);
};

// 移除终端（从组织中移除）
export const removeTerminal_Api = (id: any) => {
  return requestClient.delete(`${Api.terminal}/${id}/remove`);
};

// 获取组织树
export const getOrganTreeByTerminal_Api = () => {
  return requestClient.get(`${Api.terminal}/tree/by/organ`);
};

// 导出终端
export const exportTerminal_Api = (params: any) => {
  return requestClient.download(`${Api.terminal}/export`, { params });
};

// 播放视频
export const getVideoByTerminal_Api = (id: any) => {
  return requestClient.post(`${Api.terminal}/play?terminalId=${id}`);
};

// 分页查询太阳能电池板数据
export interface solarQuery {
  terminalId: number;
  page: number;
  size: number;
}
export const getSolarPanelPage_Api = (params: solarQuery) => {
  return requestClient.get(`${Api.solar}/page`, { params });
};

// 查询蓄电池最新实时数据
export const getSolarPanelRealTime_Api = (terminalId: number) => {
  return requestClient.get(`${Api.solar}/realtime?terminalId=${terminalId}`);
};

// 蓄电池按天统计数据
export const getSolarPanelStat24h_Api = (params: {
  terminalId: number;
  day: string; // yy-MM-dd
}) => {
  return requestClient.get(`${Api.solar}/stat24h`, { params });
};
