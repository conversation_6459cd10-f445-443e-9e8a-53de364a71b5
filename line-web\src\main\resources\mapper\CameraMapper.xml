<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bdyl.line.web.mapper.CameraMapper">
    <select id="countCameraByOrganIds" resultType="com.bdyl.line.web.model.response.camera.OrganCameraCountDTO">
        SELECT
            organ_id, COUNT(*) AS cameraCount
        FROM
            t_camera
        WHERE
            organ_id IN
        <foreach collection="organIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
            organ_id
    </select>
    <select id="countCameraByOrganId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_camera
        WHERE organ_id = #{organId}

    </select>
    <select id="camerasInMap" resultType="com.bdyl.line.web.model.response.camera.CameraInMapResponse">
        SELECT c.id,
               c.name,
               c.code,
               c.status,
               c.location,
               c.remarks,
               c.longitude,
               c.latitude,
               t.id            as terminalId,
               t.status        as terminalStatus,
               t.battery_level as batteryLevel
        from t_camera c
                 inner join t_terminal t on c.terminal_id = t.id
    </select>
</mapper>