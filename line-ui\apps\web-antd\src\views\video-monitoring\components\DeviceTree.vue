<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { useEventBus } from '@vueuse/core';
import { Empty } from 'ant-design-vue';

import { getCameraByOrgan_Api } from '#/api/core/camera';
import { getSceneCameraCount_Api } from '#/api/core/scene';

// 树数据
const treeData = ref<any[]>([]);
const loading = ref(false);
// 树类型
const treeType = ref<string>('organ');

// 事件总线，用于与父组件通信
const eventBus = useEventBus('device-tree');

const searchValue = ref('');
// 选中的节点keys
const selectedKeys = ref<string[]>([]);
// 展开的节点keys
const expandedKeys = ref<string[]>([]);
// 是否自动展开父节点
const autoExpandParent = ref(false);

// 获取组织摄像头树数据
const fetchOrganTreeData = async () => {
  try {
    loading.value = true;
    const response = await getCameraByOrgan_Api();

    // 转换数据格式
    const convertedData = convertOrganToTreeData(response);

    treeData.value = convertedData;
  } catch (error) {
    console.error('获取组织树数据失败:', error);
    treeData.value = [];
  } finally {
    loading.value = false;
  }
};

// 获取场景摄像头树数据
const fetchSceneTreeData = async () => {
  try {
    loading.value = true;
    const response = await getSceneCameraCount_Api();

    // 转换数据格式
    const convertedData = convertSceneToTreeData(response);
    treeData.value = convertedData;
  } catch (error) {
    console.error('获取场景树数据失败:', error);
    treeData.value = [];
  } finally {
    loading.value = false;
  }
};

// 根据树类型获取数据
const fetchTreeData = async () => {
  if (treeType.value === 'organ') {
    await fetchOrganTreeData();
  } else if (treeType.value === 'scene') {
    await fetchSceneTreeData();
  }
};

// 递归搜索树节点
const searchTreeNode = (
  nodes: any[],
  searchText: string,
  parentIds: string[] = [],
): boolean => {
  for (const node of nodes) {
    // 检查当前节点是否匹配
    const nodeMatches =
      (node.organName &&
        node.organName.toLowerCase().includes(searchText.toLowerCase())) ||
      (node.sceneName &&
        node.sceneName.toLowerCase().includes(searchText.toLowerCase())) ||
      (node.name &&
        node.name.toLowerCase().includes(searchText.toLowerCase())) ||
      (node.title &&
        node.title.toLowerCase().includes(searchText.toLowerCase()));

    if (nodeMatches) {
      selectedKeys.value = [node.key];
      expandedKeys.value = [...parentIds];
      autoExpandParent.value = true;
      return true;
    }

    if (node.children && node.children.length > 0) {
      const found = searchTreeNode(node.children, searchText, [
        ...parentIds,
        node.key,
      ]);
      if (found) return true;
    }
  }
  return false;
};

// 搜索设备
const handleSearch = () => {
  if (!searchValue.value.trim()) {
    selectedKeys.value = [];
    expandedKeys.value = [];
    autoExpandParent.value = false;
    return;
  }

  searchTreeNode(treeData.value, searchValue.value);
};

// 展开节点事件
const onExpand = (keys: any[]) => {
  expandedKeys.value = keys.map((key: any) => String(key));
  autoExpandParent.value = false;
};

// 转换组织数据格式为Tree组件需要的格式
const convertOrganToTreeData = (data: any): any[] => {
  if (!data) return [];

  // 如果是单个对象，转换为数组
  const dataArray = Array.isArray(data) ? data : [data];

  return dataArray.map((item: any) => convertOrganNodeToTreeNode(item));
};

// 转换组织单个节点
const convertOrganNodeToTreeNode = (node: any): any => {
  const treeNode: any = {
    id: node.organId || node.organName,
    key: String(node.organId || node.organName),
    title: `${node.organName} (${node.cameraCount})`,
    organName: node.organName,
    organId: node.organId,
    cameraCount: node.cameraCount,
    cameraList: node.cameraList || [],
    isOrgan: true,
    children: [],
  };

  // 添加摄像头子节点
  if (node.cameraList && node.cameraList.length > 0) {
    const cameraNodes = node.cameraList.map((camera: any) => ({
      id: `camera_${camera.id}`,
      key: `camera_${camera.id}`,
      title: camera.name,
      name: camera.name,
      cameraId: camera.id,
      cameraData: camera,
      isLeaf: true,
      isCamera: true,
      status: camera.status,
    }));
    treeNode.children.push(...cameraNodes);
  }

  // 递归处理子组织
  if (node.children && node.children.length > 0) {
    const childNodes = node.children.map((child: any) =>
      convertOrganNodeToTreeNode(child),
    );
    treeNode.children.push(...childNodes);
  }

  // 如果没有子节点且没有摄像头，设置为叶子节点
  // if (treeNode.children.length === 0) {
  //   treeNode.isLeaf = true;
  // }

  return treeNode;
};

// 转换场景数据格式为Tree组件需要的格式
const convertSceneToTreeData = (data: any): any[] => {
  if (!data) return [];

  // 如果是单个对象，转换为数组
  const dataArray = Array.isArray(data) ? data : [data];

  return dataArray.map((item: any) => convertSceneNodeToTreeNode(item));
};

// 转换场景单个节点
const convertSceneNodeToTreeNode = (scene: any): any => {
  const treeNode: any = {
    id: scene.sceneId,
    key: String(scene.sceneId),
    title: `${scene.sceneName} (${scene.cameraCount})`,
    sceneName: scene.sceneName,
    sceneId: scene.sceneId,
    sceneDesc: scene.sceneDesc,
    cameraCount: scene.cameraCount,
    cameraList: scene.cameraList || [],
    isScene: true,
    children: [],
  };

  // 添加摄像头子节点
  if (scene.cameraList && scene.cameraList.length > 0) {
    const cameraNodes = scene.cameraList.map((camera: any) => ({
      id: `camera_${camera.id}`,
      key: `camera_${camera.id}`,
      title: camera.name,
      name: camera.name,
      cameraId: camera.id,
      cameraData: camera,
      isLeaf: true,
      isCamera: true,
      status: camera.status,
    }));
    treeNode.children.push(...cameraNodes);
  }

  // 如果没有摄像头，设置为叶子节点
  // if (treeNode.children.length === 0) {
  //   treeNode.isLeaf = true;
  // }

  return treeNode;
};

// 树节点选择事件
const onSelect = (selectedKeys: any[], info: any) => {
  console.log('Tree node selected:', selectedKeys, info);

  if (selectedKeys.length > 0) {
    const selectedNode = info.node;

    // 如果选择的是摄像头节点
    if (selectedNode.isCamera) {
      console.log('Camera selected:', selectedNode.cameraData);
      // 通过事件总线通知父组件
      eventBus.emit({
        type: 'camera-selected',
        data: selectedNode.cameraData,
      });
    }
    // 如果选择的是组织节点
    else if (selectedNode.isOrgan && selectedNode.organId) {
      console.log('Organ selected-child:', {
        organId: selectedNode.organId,
        organName: selectedNode.organName,
        cameraList: selectedNode.cameraList,
      });
      // 通过事件总线通知父组件
      eventBus.emit({
        type: 'organ-selected',
        data: {
          organId: selectedNode.organId,
          organName: selectedNode.organName,
          cameraList: selectedNode.cameraList,
        },
      });
    }
    // 如果选择的是场景节点
    else if (selectedNode.isScene) {
      console.log('Scene selected:', {
        sceneId: selectedNode.sceneId,
        sceneName: selectedNode.sceneName,
        sceneDesc: selectedNode.sceneDesc,
        cameraList: selectedNode.cameraList,
      });
      // 通过事件总线通知父组件
      eventBus.emit({
        type: 'scene-selected',
        data: {
          sceneId: selectedNode.sceneId,
          sceneName: selectedNode.sceneName,
          sceneDesc: selectedNode.sceneDesc,
          cameraList: selectedNode.cameraList,
        },
      });
    }
  }
};

// 监听树类型变化，重新获取数据
watch(treeType, () => {
  fetchTreeData();
});

// 组件挂载时获取数据
onMounted(() => {
  fetchTreeData();
});
</script>

<template>
  <div class="device-tree-container">
    <a-radio-group v-model:value="treeType" button-style="solid" class="mb-2">
      <a-radio-button value="organ">按组织</a-radio-button>
      <a-radio-button value="scene">按场景</a-radio-button>
    </a-radio-group>
    <a-input-search
      v-model:value="searchValue"
      placeholder="查询设备"
      class="mb-2"
      @search="handleSearch"
    />
    <a-tree
      v-if="!loading && treeData.length > 0"
      v-model:selectedKeys="selectedKeys"
      v-model:expandedKeys="expandedKeys"
      :tree-data="treeData"
      :auto-expand-parent="autoExpandParent"
      :field-names="{
        children: 'children',
        title: 'title',
        key: 'key',
      }"
      :show-line="true"
      :show-icon="false"
      @select="onSelect"
      @expand="onExpand"
    >
      <template #switcherIcon="node">
        <IconifyIcon
          :icon="node.expanded ? 'mdi:minus-box' : 'mdi:add-box'"
          class="text-[24px] text-[hsl(var(--primary))]"
        />
      </template>
      <template #title="{ data }">
        <div class="flex items-center gap-[5px] pl-[5px] pr-[5px]">
          <!-- 组织图标 -->
          <IconifyIcon
            v-if="data.isOrgan && data.organId"
            icon="mdi:office-building"
            class="text-[18px] text-blue-500"
          />
          <!-- 场景图标 -->
          <IconifyIcon
            v-else-if="data.isScene"
            icon="mdi:view-dashboard"
            class="text-[18px] text-purple-500"
          />
          <!-- 摄像头图标 -->
          <IconifyIcon
            v-else-if="data.isCamera"
            icon="mdi:video-box"
            class="text-[18px]"
            :class="{
              'text-green-500': data.status === 'ONLINE',
              'text-gray-400': data.status === 'OFFLINE',
              'text-red-500': data.status === 'ERROR',
            }"
          />
          <span
            class="text-sm"
            :class="{ noOrgan: data.isOrgan && !data.organId }"
            >{{
             data.title || data.organName || data.sceneName || data.name
            }}</span
          >
        </div>
      </template>
    </a-tree>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center p-4">
      <IconifyIcon
        icon="mdi:loading"
        class="animate-spin text-2xl text-blue-500"
      />
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 空状态 -->
    <Empty
      v-if="!loading && treeData.length === 0"
      :image="Empty.PRESENTED_IMAGE_SIMPLE"
      description="暂无设备数据"
    />
  </div>
</template>

<style scoped lang="scss">
.device-tree-container {
  height: calc(100vh - 100px);
  overflow-y: auto;

  :deep(.ant-tree) {
    background: transparent;

    .ant-tree-node-content-wrapper {
      padding: 2px 4px;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }

      &.ant-tree-node-selected {
        background-color: rgba(24, 144, 255, 0.1);
      }
    }

    .ant-tree-title {
      font-size: 13px;
    }

    .ant-tree-switcher {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

:deep(.ant-tree-switcher-noop) {
  display: none !important;
}

.noOrgan {
  color: #928f8f;
}
</style>
