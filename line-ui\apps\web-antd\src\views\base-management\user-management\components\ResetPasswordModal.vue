<script lang="ts" setup>
import { reactive, ref } from 'vue';

import { message } from 'ant-design-vue';

import { resetUserPassword_Api } from '#/api/core';

const emit = defineEmits(['success']);

const open = ref<boolean>(false);
const modalTitle = ref<string>('');
const formRef = ref();
const loading = ref(false);

// 表单数据
const formData = reactive<any>({
  userId: undefined,
  newPassword: undefined,
  confirmPassword: undefined,
});

// 表单验证规则
const formRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { max: 72, message: '密码最长72个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { max: 72, message: '密码最长72个字符', trigger: 'blur' },
    {
      validator: (_rule: any, value: string) => {
        if (value && value !== formData.newPassword) {
          return Promise.reject('两次输入的密码不一致');
        }
        return Promise.resolve();
      },
      trigger: 'blur',
    },
  ],
};
// 重置表单
const resetForm = async () => {
  await formRef.value?.resetFields();
  Object.assign(formData, {
    userId: undefined,
    newPassword: undefined,
    confirmPassword: undefined,
  });
};

// 打开弹窗
const openModal = async (record: any) => {
  open.value = true;
  modalTitle.value = '重置密码：' + record.name;
  await resetForm();

  if (record) {
    formData.userId = record.id;
  }
};

// 关闭弹窗
const closeModal = () => {
  open.value = false;
  resetForm();
};

// 重置密码
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;

    await resetUserPassword_Api(formData);
    message.success('密码重置成功');

    closeModal();
    emit('success');
  } catch (error) {
    console.error('重置密码失败', error);
  } finally {
    loading.value = false;
  }
};

// 暴露组件方法
defineExpose({
  openModal,
});
</script>
<template>
  <div>
    <a-modal
      v-model:open="open"
      :title="modalTitle"
      width="600px"
      :mask-closable="false"
      @ok="handleSubmit"
    >
      <a-form
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        :model="formData"
        :rules="formRules"
      >
        <a-row>
          <a-col :span="24">
            <a-form-item label="新密码" name="newPassword">
              <a-input-password
                v-model:value="formData.newPassword"
                placeholder="请输入密码"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="再次输入新密码" name="confirmPassword">
              <a-input-password
                v-model:value="formData.confirmPassword"
                placeholder="请输入密码"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>
