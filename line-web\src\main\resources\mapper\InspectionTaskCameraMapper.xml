<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bdyl.line.web.mapper.InspectionTaskCameraMapper">

    <!-- 根据任务ID查询摄像头列表 -->
    <select id="selectByTaskId" resultType="com.bdyl.line.web.entity.InspectionTaskCameraEntity">
        SELECT *
        FROM t_inspection_task_camera
        WHERE task_id = #{taskId}
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 根据任务ID统计摄像头数量 -->
    <select id="countByTaskId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_inspection_task_camera
        WHERE task_id = #{taskId}
    </select>

    <!-- 根据任务ID统计已完成的摄像头数量 -->
    <select id="countCompletedByTaskId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_inspection_task_camera
        WHERE task_id = #{taskId}
          AND status = 'COMPLETED'
    </select>

</mapper>
