package com.bdyl.line.common.constant;

/**
 * uaa角色常量类
 */
public final class RoleConstants {
    /**
     * 租户管理员角色代码
     */
    public static final String TENANT_ADMIN = "TENANT_ADMIN";

    /**
     * 租户管理员角色名称
     */
    public static final String TENANT_ADMIN_NAME = "租户管理员";

    /**
     * 系统管理员角色代码
     */
    public static final String SYSTEM_ADMIN = "SYSTEM_ADMIN";

    /**
     * 系统管理员角色名称
     */
    public static final String SYSTEM_ADMIN_NAME = "系统管理员";

    /**
     * 普通用户角色代码
     */
    public static final String NORMAL_USER = "NORMAL_USER";

    /**
     * 普通用户角色名称
     */
    public static final String NORMAL_USER_NAME = "普通用户";

    /**
     * 角色类型：系统角色
     */
    public static final String ROLE_TYPE_SYSTEM = "SYSTEM";

    /**
     * 角色类型：自定义角色
     */
    public static final String ROLE_TYPE_CUSTOM = "CUSTOM";

    /**
     * 角色状态：启用
     */
    public static final String ROLE_STATUS_ENABLED = "ENABLED";

    /**
     * 角色状态：禁用
     */
    public static final String ROLE_STATUS_DISABLED = "DISABLED";

    private RoleConstants() {
        // 私有构造方法，防止实例化
    }
}
