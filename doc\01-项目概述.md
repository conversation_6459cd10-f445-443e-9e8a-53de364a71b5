# 线路巡检管理系统 - 项目概述

## 1. 项目简介

线路巡检管理系统（Line Inspection Management System）是一个基于Spring Boot和Vue3的现代化巡检管理平台，主要用于电力线路、通信线路等基础设施的智能化巡检管理。

### 1.1 项目背景

随着基础设施建设的快速发展，传统的人工巡检方式已无法满足现代化管理的需求。本系统通过集成视频监控、物联网传感器、GB28181协议等技术，实现了智能化、自动化的巡检管理。

### 1.2 项目目标

- **提高巡检效率**：通过自动化任务生成和智能调度，提升巡检工作效率
- **降低运维成本**：减少人工巡检成本，优化资源配置
- **增强安全保障**：实时监控和预警，及时发现安全隐患
- **数据化管理**：建立完整的巡检数据档案，支持数据分析和决策

## 2. 核心功能模块

### 2.1 摄像头管理
- 摄像头设备注册和配置
- 实时视频流播放
- 历史视频回放
- GB28181协议集成
- 设备状态监控

### 2.2 巡检计划管理
- 多周期类型支持（小时/日/周/月）
- 灵活的时间段配置
- 摄像头分组管理
- 负责人分配

### 2.3 巡检任务管理
- 自动任务生成
- 任务状态跟踪
- 进度监控
- 漏检检测

### 2.4 巡检记录管理
- 现场拍照记录
- 巡检结果录入
- 异常情况报告
- 历史记录查询

### 2.5 报警系统
- 实时报警监控
- 多种报警类型支持
- 报警处理流程
- 报警统计分析

### 2.6 组织权限管理
- 多租户支持
- 组织架构管理
- 用户权限控制
- 数据权限隔离

## 3. 技术架构

### 3.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   后端服务      │    │   外部系统      │
│   (Vue3 + Vben) │◄──►│ (Spring Boot)   │◄──►│ (GB28181/IoT)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 3.2 技术栈

**后端技术栈：**
- Java 21
- Spring Boot 3.x
- MyBatis-Plus 3.5.x
- MySQL 8.0
- Liquibase（数据库版本管理）
- Maven（依赖管理）

**前端技术栈：**
- Vue 3
- TypeScript
- Vben Admin（管理后台框架）
- Ant Design Vue
- Vite（构建工具）
- pnpm（包管理）

**第三方集成：**
- GB28181协议（视频监控）
- 物联网平台（传感器数据）
- 认证服务（UAA）
- 文件存储服务

## 4. 项目结构

```
line-inspection/
├── doc/                    # 项目文档
├── line-common/           # 公共模块
│   └── src/main/java/com/bdyl/line/common/
├── line-web/              # 后端服务
│   ├── src/main/java/com/bdyl/line/web/
│   │   ├── controller/    # 控制器层
│   │   ├── service/       # 服务层
│   │   ├── mapper/        # 数据访问层
│   │   ├── entity/        # 实体类
│   │   ├── model/         # 数据传输对象
│   │   ├── config/        # 配置类
│   │   ├── remote/        # 远程服务调用
│   │   └── timer/         # 定时任务
│   └── src/main/resources/
│       ├── application.yml
│       └── db/changelog/  # 数据库变更脚本
└── line-ui/               # 前端应用
    ├── apps/web-antd/     # 主应用
    ├── packages/          # 公共包
    └── internal/          # 内部工具
```

## 5. 部署架构

### 5.1 开发环境
- 本地开发：Spring Boot内嵌Tomcat + Vue开发服务器
- 数据库：MySQL 8.0
- 文件存储：本地文件系统

### 5.2 生产环境
- 应用服务器：Spring Boot + Undertow
- 反向代理：Nginx
- 数据库：MySQL 8.0（主从复制）
- 监控：Spring Boot Actuator + Prometheus
- 日志：Logback + ELK Stack

## 6. 项目特色

### 6.1 智能化巡检
- 基于时间规则的自动任务生成
- 智能漏检检测机制
- 多维度巡检统计分析

### 6.2 视频集成
- 支持GB28181国标协议
- 实时视频流播放
- 历史视频回放功能
- M3U8格式支持

### 6.3 多租户架构
- 完善的租户隔离机制
- 灵活的组织架构管理
- 细粒度的数据权限控制

### 6.4 现代化技术栈
- 采用最新的Java 21和Spring Boot 3.x
- 前端使用Vue 3 Composition API
- 响应式设计，支持移动端

## 7. 版本信息

- **当前版本**：2025.1.0.0-SNAPSHOT
- **开发语言**：Java 21 / TypeScript
- **最低JDK版本**：JDK 21
- **数据库版本**：MySQL 8.0+
- **浏览器支持**：Chrome 90+, Firefox 88+, Safari 14+

## 8. 许可证

本项目采用企业内部许可证，仅供内部使用。
