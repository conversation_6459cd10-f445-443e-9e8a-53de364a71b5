import {
  errLog,
  getSlotVNs,
  warnLog
} from "./chunk-GW2WQRXE.js";
import {
  eqEmptyValue,
  formatText,
  getFuncText,
  isEnableConf
} from "./chunk-RFZDZ2Z5.js";
import {
  VxeUI,
  require_xe_utils
} from "./chunk-TV7URO3H.js";
import {
  h,
  nextTick,
  reactive,
  watch
} from "./chunk-ZLVVKZUX.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/ui/src/dom.js
var import_xe_utils = __toESM(require_xe_utils());
var reClsMap = {};
var tpImgEl;
function initTpImg() {
  if (!tpImgEl) {
    tpImgEl = new Image();
    tpImgEl.src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=";
  }
  return tpImgEl;
}
function getTpImg() {
  if (!tpImgEl) {
    return initTpImg();
  }
  return tpImgEl;
}
function getPropClass(property, params) {
  return property ? import_xe_utils.default.isFunction(property) ? property(params) : property : "";
}
function getClsRE(cls) {
  if (!reClsMap[cls]) {
    reClsMap[cls] = new RegExp(`(?:^|\\s)${cls}(?!\\S)`, "g");
  }
  return reClsMap[cls];
}
function getNodeOffset(elem, container, rest) {
  if (elem) {
    const parentElem = elem.parentNode;
    rest.top += elem.offsetTop;
    rest.left += elem.offsetLeft;
    if (parentElem && parentElem !== document.documentElement && parentElem !== document.body) {
      rest.top -= parentElem.scrollTop;
      rest.left -= parentElem.scrollLeft;
    }
    if (container && (elem === container || elem.offsetParent === container) ? 0 : elem.offsetParent) {
      return getNodeOffset(elem.offsetParent, container, rest);
    }
  }
  return rest;
}
function isPx(val) {
  return val && /^\d+(px)?$/.test(val);
}
function isScale(val) {
  return val && /^\d+%$/.test(val);
}
function hasClass(elem, cls) {
  return !!(elem && elem.className && elem.className.match && elem.className.match(getClsRE(cls)));
}
function removeClass(elem, cls) {
  if (elem && hasClass(elem, cls)) {
    elem.className = elem.className.replace(getClsRE(cls), "");
  }
}
function addClass(elem, cls) {
  if (elem && !hasClass(elem, cls)) {
    removeClass(elem, cls);
    elem.className = `${elem.className} ${cls}`;
  }
}
function hasControlKey(evnt) {
  return evnt.ctrlKey || evnt.metaKey;
}
function toCssUnit(val, unit = "px") {
  if (import_xe_utils.default.isNumber(val) || /^\d+$/.test(`${val}`)) {
    return `${val}${unit}`;
  }
  return `${val || ""}`;
}
function getDomNode() {
  const documentElement = document.documentElement;
  const bodyElem = document.body;
  return {
    scrollTop: documentElement.scrollTop || bodyElem.scrollTop,
    scrollLeft: documentElement.scrollLeft || bodyElem.scrollLeft,
    visibleHeight: documentElement.clientHeight || bodyElem.clientHeight,
    visibleWidth: documentElement.clientWidth || bodyElem.clientWidth
  };
}
function getOffsetHeight(elem) {
  return elem ? elem.offsetHeight : 0;
}
function getPaddingTopBottomSize(elem) {
  if (elem) {
    const computedStyle = getComputedStyle(elem);
    const paddingTop = import_xe_utils.default.toNumber(computedStyle.paddingTop);
    const paddingBottom = import_xe_utils.default.toNumber(computedStyle.paddingBottom);
    return paddingTop + paddingBottom;
  }
  return 0;
}
function setScrollTop(elem, scrollTop) {
  if (elem) {
    elem.scrollTop = scrollTop;
  }
}
function setScrollLeft(elem, scrollLeft) {
  if (elem) {
    elem.scrollLeft = scrollLeft;
  }
}
function updateCellTitle(overflowElem, column) {
  const content = column.type === "html" ? overflowElem.innerText : overflowElem.textContent;
  if (overflowElem.getAttribute("title") !== content) {
    overflowElem.setAttribute("title", content);
  }
}
function getEventTargetNode(evnt, container, queryCls, queryMethod) {
  let targetElem;
  let target = evnt.target.shadowRoot && evnt.composed ? evnt.composedPath()[0] || evnt.target : evnt.target;
  while (target && target.nodeType && target !== document) {
    if (queryCls && hasClass(target, queryCls) && (!queryMethod || queryMethod(target))) {
      targetElem = target;
    } else if (target === container) {
      return { flag: queryCls ? !!targetElem : true, container, targetElem };
    }
    target = target.parentNode;
  }
  return { flag: false };
}
function getOffsetPos(elem, container) {
  return getNodeOffset(elem, container, { left: 0, top: 0 });
}
function getAbsolutePos(elem) {
  const bounding = elem.getBoundingClientRect();
  const boundingTop = bounding.top;
  const boundingLeft = bounding.left;
  const { scrollTop, scrollLeft, visibleHeight, visibleWidth } = getDomNode();
  return { boundingTop, top: scrollTop + boundingTop, boundingLeft, left: scrollLeft + boundingLeft, visibleHeight, visibleWidth };
}
var scrollIntoViewIfNeeded = "scrollIntoViewIfNeeded";
var scrollIntoView = "scrollIntoView";
function scrollToView(elem) {
  if (elem) {
    if (elem[scrollIntoViewIfNeeded]) {
      elem[scrollIntoViewIfNeeded]();
    } else if (elem[scrollIntoView]) {
      elem[scrollIntoView]();
    }
  }
}
function triggerEvent(targetElem, type) {
  if (targetElem) {
    targetElem.dispatchEvent(new Event(type));
  }
}

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/src/util.js
var import_xe_utils3 = __toESM(require_xe_utils());

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/src/columnInfo.js
var import_xe_utils2 = __toESM(require_xe_utils());
var { getI18n, formats } = VxeUI;
var ColumnInfo = class {
  /* eslint-disable @typescript-eslint/no-use-before-define */
  constructor($xeTable, _vm, { renderHeader, renderCell, renderFooter, renderData } = {}) {
    const tableProps = $xeTable.props;
    const $xeGrid = $xeTable.xeGrid;
    const formatter = _vm.formatter;
    const visible = import_xe_utils2.default.isBoolean(_vm.visible) ? _vm.visible : true;
    const types = ["seq", "checkbox", "radio", "expand", "html"];
    if (_vm.type && types.indexOf(_vm.type) === -1) {
      warnLog("vxe.error.errProp", [`type=${_vm.type}`, types.join(", ")]);
    }
    if (import_xe_utils2.default.isBoolean(_vm.cellRender) || _vm.cellRender && !import_xe_utils2.default.isObject(_vm.cellRender)) {
      warnLog("vxe.error.errProp", [`column.cell-render=${_vm.cellRender}`, "column.cell-render={}"]);
    }
    if (import_xe_utils2.default.isBoolean(_vm.editRender) || _vm.editRender && !import_xe_utils2.default.isObject(_vm.editRender)) {
      warnLog("vxe.error.errProp", [`column.edit-render=${_vm.editRender}`, "column.edit-render={}"]);
    }
    if (_vm.type === "expand") {
      const { treeConfig } = tableProps;
      const { computeTreeOpts } = $xeTable.getComputeMaps();
      const treeOpts = computeTreeOpts.value;
      if (treeConfig && (treeOpts.showLine || treeOpts.line)) {
        errLog("vxe.error.errConflicts", ["tree-config.showLine", "column.type=expand"]);
      }
    }
    if (formatter) {
      if (import_xe_utils2.default.isString(formatter)) {
        const gFormatOpts = formats.get(formatter) || import_xe_utils2.default[formatter];
        if (!gFormatOpts || !import_xe_utils2.default.isFunction(gFormatOpts.tableCellFormatMethod || gFormatOpts.cellFormatMethod)) {
          errLog("vxe.error.notFormats", [formatter]);
        }
      } else if (import_xe_utils2.default.isArray(formatter)) {
        const gFormatOpts = formats.get(formatter[0]) || import_xe_utils2.default[formatter[0]];
        if (!gFormatOpts || !import_xe_utils2.default.isFunction(gFormatOpts.tableCellFormatMethod || gFormatOpts.cellFormatMethod)) {
          errLog("vxe.error.notFormats", [formatter[0]]);
        }
      }
    }
    Object.assign(this, {
      // 基本属性
      type: _vm.type,
      property: _vm.field,
      field: _vm.field,
      title: _vm.title,
      width: _vm.width,
      minWidth: _vm.minWidth,
      maxWidth: _vm.maxWidth,
      resizable: _vm.resizable,
      fixed: _vm.fixed,
      align: _vm.align,
      headerAlign: _vm.headerAlign,
      footerAlign: _vm.footerAlign,
      showOverflow: _vm.showOverflow,
      showHeaderOverflow: _vm.showHeaderOverflow,
      showFooterOverflow: _vm.showFooterOverflow,
      className: _vm.className,
      headerClassName: _vm.headerClassName,
      footerClassName: _vm.footerClassName,
      formatter,
      footerFormatter: _vm.footerFormatter,
      padding: _vm.padding,
      verticalAlign: _vm.verticalAlign,
      sortable: _vm.sortable,
      sortBy: _vm.sortBy,
      sortType: _vm.sortType,
      filters: toFilters(_vm.filters),
      filterMultiple: import_xe_utils2.default.isBoolean(_vm.filterMultiple) ? _vm.filterMultiple : true,
      filterMethod: _vm.filterMethod,
      filterResetMethod: _vm.filterResetMethod,
      filterRecoverMethod: _vm.filterRecoverMethod,
      filterRender: _vm.filterRender,
      rowGroupNode: _vm.rowGroupNode,
      treeNode: _vm.treeNode,
      dragSort: _vm.dragSort,
      rowResize: _vm.rowResize,
      cellType: _vm.cellType,
      cellRender: _vm.cellRender,
      editRender: _vm.editRender,
      contentRender: _vm.contentRender,
      headerExportMethod: _vm.headerExportMethod,
      exportMethod: _vm.exportMethod,
      footerExportMethod: _vm.footerExportMethod,
      titleHelp: _vm.titleHelp,
      titlePrefix: _vm.titlePrefix,
      titleSuffix: _vm.titleSuffix,
      // 自定义参数
      params: _vm.params,
      // 渲染属性
      id: _vm.colId || import_xe_utils2.default.uniqueId("col_"),
      parentId: null,
      visible,
      // 内部属性（一旦被使用，将导致不可升级版本）
      halfVisible: false,
      defaultVisible: visible,
      defaultFixed: _vm.fixed,
      checked: false,
      halfChecked: false,
      disabled: false,
      // 分组层级
      level: 1,
      // 跨行
      rowSpan: 1,
      // 跨列
      colSpan: 1,
      // 数据排序
      order: null,
      sortTime: 0,
      // 列排序
      sortNumber: 0,
      renderSortNumber: 0,
      renderFixed: "",
      renderVisible: false,
      renderWidth: 0,
      renderHeight: 0,
      renderResizeWidth: 0,
      renderAutoWidth: 0,
      resizeWidth: 0,
      renderLeft: 0,
      renderArgs: [],
      model: {},
      renderHeader: renderHeader || _vm.renderHeader,
      renderCell: renderCell || _vm.renderCell,
      renderFooter: renderFooter || _vm.renderFooter,
      renderData,
      // 单元格插槽，只对 grid 有效
      slots: _vm.slots
    });
    if ($xeGrid) {
      const { computeProxyOpts } = $xeGrid.getComputeMaps();
      const proxyOpts = computeProxyOpts.value;
      if (proxyOpts.beforeColumn) {
        proxyOpts.beforeColumn({ $grid: $xeGrid, column: this });
      }
    }
  }
  getTitle() {
    return getFuncText(this.title || (this.type === "seq" ? getI18n("vxe.table.seqTitle") : ""));
  }
  getKey() {
    const { type } = this;
    return this.field || (type ? `type=${type}` : null);
  }
  update(name, value) {
    if (name !== "filters") {
      if (name === "field") {
        this.property = value;
      }
      this[name] = value;
    }
  }
};

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/src/util.js
var getAllConvertColumns = (columns, parentColumn) => {
  const result = [];
  columns.forEach((column) => {
    column.parentId = parentColumn ? parentColumn.id : null;
    if (column.visible) {
      if (column.children && column.children.length && column.children.some((column2) => column2.visible)) {
        result.push(column);
        result.push(...getAllConvertColumns(column.children, column));
      } else {
        result.push(column);
      }
    }
  });
  return result;
};
var convertHeaderColumnToRows = (originColumns) => {
  let maxLevel = 1;
  const traverse = (column, parent) => {
    if (parent) {
      column.level = parent.level + 1;
      if (maxLevel < column.level) {
        maxLevel = column.level;
      }
    }
    if (column.children && column.children.length && column.children.some((column2) => column2.visible)) {
      let colSpan = 0;
      column.children.forEach((subColumn) => {
        if (subColumn.visible) {
          traverse(subColumn, column);
          colSpan += subColumn.colSpan;
        }
      });
      column.colSpan = colSpan;
    } else {
      column.colSpan = 1;
    }
  };
  originColumns.forEach((column) => {
    column.level = 1;
    traverse(column);
  });
  const rows = [];
  for (let i = 0; i < maxLevel; i++) {
    rows.push([]);
  }
  const allColumns = getAllConvertColumns(originColumns);
  allColumns.forEach((column) => {
    if (column.children && column.children.length && column.children.some((column2) => column2.visible)) {
      column.rowSpan = 1;
    } else {
      column.rowSpan = maxLevel - column.level + 1;
    }
    rows[column.level - 1].push(column);
  });
  return rows;
};
function restoreScrollLocation($xeTable, scrollLeft, scrollTop) {
  const internalData = $xeTable.internalData;
  if (scrollLeft || scrollTop) {
    internalData.intoRunScroll = false;
    internalData.inVirtualScroll = false;
    internalData.inWheelScroll = false;
    internalData.inHeaderScroll = false;
    internalData.inBodyScroll = false;
    internalData.inFooterScroll = false;
    internalData.scrollRenderType = "";
    return $xeTable.scrollTo(scrollLeft, scrollTop);
  }
  return nextTick();
}
function getRowUniqueId() {
  return import_xe_utils3.default.uniqueId("row_");
}
function getRowkey($xeTable) {
  const { props } = $xeTable;
  const { computeRowOpts } = $xeTable.getComputeMaps();
  const rowOpts = computeRowOpts.value;
  return `${props.rowId || rowOpts.keyField || "_X_ROW_KEY"}`;
}
function getRowid($xeTable, row) {
  const rowid = import_xe_utils3.default.get(row, getRowkey($xeTable));
  return encodeRowid(rowid);
}
function createHandleUpdateRowId($xeTable) {
  const rowKey = getRowkey($xeTable);
  const isDeepKey = rowKey.indexOf(".") > -1;
  const updateRId = isDeepKey ? updateDeepRowKey : updateFastRowKey;
  return {
    rowKey,
    handleUpdateRowId(row) {
      return row ? updateRId(row, rowKey) : null;
    }
  };
}
function createHandleGetRowId($xeTable) {
  const rowKey = getRowkey($xeTable);
  const isDeepKey = rowKey.indexOf(".") > -1;
  const getRId = isDeepKey ? getDeepRowIdByKey : getFastRowIdByKey;
  return {
    rowKey,
    handleGetRowId(row) {
      return row ? getRId(row, rowKey) : null;
    }
  };
}
function encodeRowid(rowVal) {
  return import_xe_utils3.default.eqNull(rowVal) ? "" : encodeURIComponent(rowVal);
}
function getDeepRowIdByKey(row, rowKey) {
  return import_xe_utils3.default.get(row, rowKey);
}
function updateDeepRowKey(row, rowKey) {
  let rowid = getDeepRowIdByKey(row, rowKey);
  if (eqEmptyValue(rowid)) {
    rowid = getRowUniqueId();
    import_xe_utils3.default.set(row, rowKey, rowid);
  }
  return rowid;
}
function getFastRowIdByKey(row, rowKey) {
  return row[rowKey];
}
function updateFastRowKey(row, rowKey) {
  let rowid = getFastRowIdByKey(row, rowKey);
  if (eqEmptyValue(rowid)) {
    rowid = getRowUniqueId();
    row[rowKey] = rowid;
  }
  return rowid;
}
var handleFieldOrColumn = ($xeTable, fieldOrColumn) => {
  if (fieldOrColumn) {
    return import_xe_utils3.default.isString(fieldOrColumn) || import_xe_utils3.default.isNumber(fieldOrColumn) ? $xeTable.getColumnByField(`${fieldOrColumn}`) : fieldOrColumn;
  }
  return null;
};
var handleRowidOrRow = ($xeTable, rowidOrRow) => {
  if (rowidOrRow) {
    const rowid = import_xe_utils3.default.isString(rowidOrRow) || import_xe_utils3.default.isNumber(rowidOrRow) ? rowidOrRow : getRowid($xeTable, rowidOrRow);
    return $xeTable.getRowById(rowid);
  }
  return null;
};
function getPaddingLeftRightSize(elem) {
  if (elem) {
    const computedStyle = getComputedStyle(elem);
    const paddingLeft = import_xe_utils3.default.toNumber(computedStyle.paddingLeft);
    const paddingRight = import_xe_utils3.default.toNumber(computedStyle.paddingRight);
    return paddingLeft + paddingRight;
  }
  return 0;
}
function getElementMarginWidth(elem) {
  if (elem) {
    const computedStyle = getComputedStyle(elem);
    const marginLeft = import_xe_utils3.default.toNumber(computedStyle.marginLeft);
    const marginRight = import_xe_utils3.default.toNumber(computedStyle.marginRight);
    return elem.offsetWidth + marginLeft + marginRight;
  }
  return 0;
}
function queryCellElement(cell, selector) {
  return cell.querySelector(".vxe-cell" + selector);
}
function toFilters(filters) {
  if (filters && import_xe_utils3.default.isArray(filters)) {
    return filters.map(({ label, value, data, resetValue, checked }) => {
      return { label, value, data, resetValue, checked: !!checked, _checked: !!checked };
    });
  }
  return filters;
}
function toTreePathSeq(path) {
  return path.map((num, i) => i % 2 === 0 ? Number(num) + 1 : ".").join("");
}
function getCellValue(row, column) {
  return import_xe_utils3.default.get(row, column.field);
}
function setCellValue(row, column, value) {
  return import_xe_utils3.default.set(row, column.field, value);
}
function getRefElem(refEl) {
  if (refEl) {
    const rest = refEl.value;
    if (rest) {
      return rest.$el || rest;
    }
  }
  return null;
}
function getCellHeight(height) {
  if (height === "unset") {
    return 0;
  }
  return height || 0;
}
function getColReMinWidth(params) {
  const { $table, column, cell } = params;
  const tableProps = $table.props;
  const internalData = $table.internalData;
  const { computeResizableOpts } = $table.getComputeMaps();
  const resizableOpts = computeResizableOpts.value;
  const { minWidth: reMinWidth } = resizableOpts;
  if (reMinWidth) {
    const customMinWidth = import_xe_utils3.default.isFunction(reMinWidth) ? reMinWidth(params) : reMinWidth;
    if (customMinWidth !== "auto") {
      return Math.max(1, import_xe_utils3.default.toNumber(customMinWidth));
    }
  }
  const { elemStore } = internalData;
  const { showHeaderOverflow: allColumnHeaderOverflow } = tableProps;
  const { showHeaderOverflow, minWidth: colMinWidth } = column;
  const headOverflow = import_xe_utils3.default.isUndefined(showHeaderOverflow) || import_xe_utils3.default.isNull(showHeaderOverflow) ? allColumnHeaderOverflow : showHeaderOverflow;
  const showEllipsis = headOverflow === "ellipsis";
  const showTitle = headOverflow === "title";
  const showTooltip = headOverflow === true || headOverflow === "tooltip";
  const hasEllipsis = showTitle || showTooltip || showEllipsis;
  const minTitleWidth = import_xe_utils3.default.floor((import_xe_utils3.default.toNumber(getComputedStyle(cell).fontSize) || 14) * 1.6);
  const paddingLeftRight = getPaddingLeftRightSize(cell) + getPaddingLeftRightSize(queryCellElement(cell, ""));
  let mWidth = minTitleWidth + paddingLeftRight;
  if (hasEllipsis) {
    const dragIconWidth = getPaddingLeftRightSize(queryCellElement(cell, ">.vxe-cell--drag-handle"));
    const checkboxIconWidth = getPaddingLeftRightSize(queryCellElement(cell, ">.vxe-cell--checkbox"));
    const requiredIconWidth = getElementMarginWidth(queryCellElement(cell, ">.vxe-cell--required-icon"));
    const editIconWidth = getElementMarginWidth(queryCellElement(cell, ">.vxe-cell--edit-icon"));
    const prefixIconWidth = getElementMarginWidth(queryCellElement(cell, ">.vxe-cell-title-prefix-icon"));
    const suffixIconWidth = getElementMarginWidth(queryCellElement(cell, ">.vxe-cell-title-suffix-icon"));
    const sortIconWidth = getElementMarginWidth(queryCellElement(cell, ">.vxe-cell--sort"));
    const filterIconWidth = getElementMarginWidth(queryCellElement(cell, ">.vxe-cell--filter"));
    mWidth += dragIconWidth + checkboxIconWidth + requiredIconWidth + editIconWidth + prefixIconWidth + suffixIconWidth + filterIconWidth + sortIconWidth;
  }
  if (colMinWidth) {
    const bodyScrollElem = getRefElem(elemStore["main-body-scroll"]);
    if (bodyScrollElem) {
      if (isScale(colMinWidth)) {
        const bodyWidth = bodyScrollElem.clientWidth - 1;
        const meanWidth = bodyWidth / 100;
        return Math.max(mWidth, Math.floor(import_xe_utils3.default.toInteger(colMinWidth) * meanWidth));
      } else if (isPx(colMinWidth)) {
        return Math.max(mWidth, import_xe_utils3.default.toInteger(colMinWidth));
      }
    }
  }
  return mWidth;
}
function isColumnInfo(column) {
  return column && (column.constructor === ColumnInfo || column instanceof ColumnInfo);
}
function createColumn($xeTable, options, renderOptions) {
  return isColumnInfo(options) ? options : reactive(new ColumnInfo($xeTable, options, renderOptions));
}
function watchColumn($xeTable, props, column) {
  Object.keys(props).forEach((name) => {
    watch(() => props[name], (value) => {
      column.update(name, value);
      if ($xeTable) {
        if (name === "filters") {
          $xeTable.setFilter(column, value);
          $xeTable.handleUpdateDataQueue();
        } else if (["visible", "fixed", "width", "minWidth", "maxWidth"].includes(name)) {
          $xeTable.handleRefreshColumnQueue();
        }
      }
    });
  });
}
function assembleColumn($xeTable, elem, column, colgroup) {
  const { reactData } = $xeTable;
  const { staticColumns } = reactData;
  const parentElem = elem.parentNode;
  const parentColumn = colgroup ? colgroup.columnConfig : null;
  const parentCols = parentColumn ? parentColumn.children : staticColumns;
  if (parentElem && parentCols) {
    parentCols.splice(import_xe_utils3.default.arrayIndexOf(parentElem.children, elem), 0, column);
    reactData.staticColumns = staticColumns.slice(0);
  }
}
function destroyColumn($xeTable, column) {
  const { reactData } = $xeTable;
  const { staticColumns } = reactData;
  const matchObj = import_xe_utils3.default.findTree(staticColumns, (item) => item.id === column.id, { children: "children" });
  if (matchObj) {
    matchObj.items.splice(matchObj.index, 1);
  }
  reactData.staticColumns = staticColumns.slice(0);
}
function getRootColumn($xeTable, column) {
  const { internalData } = $xeTable;
  const { fullColumnIdData } = internalData;
  if (!column) {
    return null;
  }
  let parentColId = column.parentId;
  while (fullColumnIdData[parentColId]) {
    const column2 = fullColumnIdData[parentColId].column;
    parentColId = column2.parentId;
    if (!parentColId) {
      return column2;
    }
  }
  return column;
}
var lineOffsetSizes = {
  mini: 3,
  small: 2,
  medium: 1,
  large: 0
};
var countTreeExpand = (prevRow, params) => {
  let count = 1;
  if (!prevRow) {
    return count;
  }
  const { $table } = params;
  const { computeTreeOpts } = $table.getComputeMaps();
  const treeOpts = computeTreeOpts.value;
  const { transform, mapChildrenField } = treeOpts;
  const childrenField = treeOpts.children || treeOpts.childrenField;
  const rowChildren = prevRow[transform ? mapChildrenField : childrenField];
  if (rowChildren && $table.isTreeExpandByRow(prevRow)) {
    for (let index = 0; index < rowChildren.length; index++) {
      count += countTreeExpand(rowChildren[index], params);
    }
  }
  return count;
};
var getOffsetSize = ($xeTable) => {
  const { computeSize } = $xeTable.getComputeMaps();
  const vSize = computeSize.value;
  if (vSize) {
    return lineOffsetSizes[vSize] || 0;
  }
  return 0;
};
function calcTreeLine(params, prevRow) {
  const { $table, row } = params;
  const tableProps = $table.props;
  const tableReactData = $table.reactData;
  const tableInternalData = $table.internalData;
  const { showOverflow } = tableProps;
  const { scrollYLoad } = tableReactData;
  const { fullAllDataRowIdData } = tableInternalData;
  const { computeRowOpts, computeCellOpts, computeDefaultRowHeight } = $table.getComputeMaps();
  const rowOpts = computeRowOpts.value;
  const cellOpts = computeCellOpts.value;
  const defaultRowHeight = computeDefaultRowHeight.value;
  const rowid = getRowid($table, row);
  const rowRest = fullAllDataRowIdData[rowid];
  const currCellHeight = rowRest.resizeHeight || cellOpts.height || rowOpts.height || rowRest.height || defaultRowHeight;
  let expandSize = 1;
  if (prevRow) {
    expandSize = countTreeExpand(prevRow, params);
  }
  let cellHeight = currCellHeight;
  const vnHeight = rowRest.height;
  if (scrollYLoad) {
    if (!showOverflow) {
      cellHeight = vnHeight || currCellHeight;
    }
  }
  return cellHeight * expandSize - (prevRow ? 1 : 12 - getOffsetSize($table));
}
function clearTableDefaultStatus($xeTable) {
  const { props, internalData } = $xeTable;
  internalData.initStatus = false;
  $xeTable.clearSort();
  $xeTable.clearCurrentRow();
  $xeTable.clearCurrentColumn();
  $xeTable.clearRadioRow();
  $xeTable.clearRadioReserve();
  $xeTable.clearCheckboxRow();
  $xeTable.clearCheckboxReserve();
  $xeTable.clearRowExpand();
  $xeTable.clearTreeExpand();
  $xeTable.clearTreeExpandReserve();
  $xeTable.clearPendingRow();
  if ($xeTable.clearFilter) {
    $xeTable.clearFilter();
  }
  if ($xeTable.clearSelected && (props.keyboardConfig || props.mouseConfig)) {
    $xeTable.clearSelected();
  }
  if ($xeTable.clearCellAreas && props.mouseConfig) {
    $xeTable.clearCellAreas();
    $xeTable.clearCopyCellArea();
  }
  return $xeTable.clearScroll();
}
function clearTableAllStatus($xeTable) {
  if ($xeTable.clearFilter) {
    $xeTable.clearFilter();
  }
  return clearTableDefaultStatus($xeTable);
}
function rowToVisible($xeTable, row) {
  const tableProps = $xeTable.props;
  const reactData = $xeTable.reactData;
  const internalData = $xeTable.internalData;
  const { computeLeftFixedWidth, computeRightFixedWidth, computeRowOpts, computeCellOpts, computeDefaultRowHeight } = $xeTable.getComputeMaps();
  const { showOverflow } = tableProps;
  const { scrollYLoad } = reactData;
  const { elemStore, afterFullData, fullAllDataRowIdData, isResizeCellHeight } = internalData;
  const rowOpts = computeRowOpts.value;
  const cellOpts = computeCellOpts.value;
  const defaultRowHeight = computeDefaultRowHeight.value;
  const leftFixedWidth = computeLeftFixedWidth.value;
  const rightFixedWidth = computeRightFixedWidth.value;
  const bodyScrollElem = getRefElem(elemStore["main-body-scroll"]);
  const rowid = getRowid($xeTable, row);
  if (bodyScrollElem) {
    const bodyHeight = bodyScrollElem.clientHeight;
    const bodyScrollTop = bodyScrollElem.scrollTop;
    const trElem = bodyScrollElem.querySelector(`[rowid="${rowid}"]`);
    if (trElem) {
      const trOffsetParent = trElem.offsetParent;
      const trOffsetTop = trElem.offsetTop + (trOffsetParent ? trOffsetParent.offsetTop : 0);
      const trHeight = trElem.clientHeight;
      if (trOffsetTop < bodyScrollTop || trOffsetTop > bodyScrollTop + bodyHeight) {
        return $xeTable.scrollTo(null, trOffsetTop);
      } else if (trOffsetTop + trHeight >= bodyHeight + bodyScrollTop) {
        return $xeTable.scrollTo(null, bodyScrollTop + trHeight);
      }
    } else {
      if (scrollYLoad) {
        const isCustomCellHeight = isResizeCellHeight || cellOpts.height || rowOpts.height;
        if (!isCustomCellHeight && showOverflow) {
          return $xeTable.scrollTo(null, ($xeTable.findRowIndexOf(afterFullData, row) - 1) * defaultRowHeight);
        }
        let scrollTop = 0;
        const rowRest = fullAllDataRowIdData[rowid] || {};
        const rHeight = rowRest.resizeHeight || cellOpts.height || rowOpts.height || rowRest.height || defaultRowHeight;
        for (let i = 0; i < afterFullData.length; i++) {
          const currRow = afterFullData[i];
          const currRowid = getRowid($xeTable, currRow);
          if (currRow === row || currRowid === rowid) {
            break;
          }
          const currRowRest = fullAllDataRowIdData[currRowid] || {};
          scrollTop += currRowRest.resizeHeight || cellOpts.height || rowOpts.height || currRowRest.height || defaultRowHeight;
        }
        if (scrollTop < bodyScrollTop) {
          return $xeTable.scrollTo(null, scrollTop - leftFixedWidth - 1);
        }
        return $xeTable.scrollTo(null, scrollTop + rHeight - (bodyHeight - rightFixedWidth - 1));
      }
    }
  }
  return Promise.resolve();
}
function colToVisible($xeTable, column, row) {
  const reactData = $xeTable.reactData;
  const internalData = $xeTable.internalData;
  const { computeLeftFixedWidth, computeRightFixedWidth } = $xeTable.getComputeMaps();
  const { scrollXLoad } = reactData;
  const { elemStore, visibleColumn } = internalData;
  const leftFixedWidth = computeLeftFixedWidth.value;
  const rightFixedWidth = computeRightFixedWidth.value;
  const bodyScrollElem = getRefElem(elemStore["main-body-scroll"]);
  if (column.fixed) {
    return Promise.resolve();
  }
  if (bodyScrollElem) {
    const bodyWidth = bodyScrollElem.clientWidth;
    const bodyScrollLeft = bodyScrollElem.scrollLeft;
    let tdElem = null;
    if (row) {
      const rowid = getRowid($xeTable, row);
      tdElem = bodyScrollElem.querySelector(`[rowid="${rowid}"] .${column.id}`);
    }
    if (!tdElem) {
      tdElem = bodyScrollElem.querySelector(`.${column.id}`);
    }
    if (tdElem) {
      const tdOffsetParent = tdElem.offsetParent;
      const tdOffsetLeft = tdElem.offsetLeft + (tdOffsetParent ? tdOffsetParent.offsetLeft : 0);
      const cellWidth = tdElem.clientWidth;
      if (tdOffsetLeft < bodyScrollLeft + leftFixedWidth) {
        return $xeTable.scrollTo(tdOffsetLeft - leftFixedWidth - 1);
      } else if (tdOffsetLeft + cellWidth - bodyScrollLeft > bodyWidth - rightFixedWidth) {
        return $xeTable.scrollTo(tdOffsetLeft + cellWidth - (bodyWidth - rightFixedWidth - 1));
      }
    } else {
      if (scrollXLoad) {
        let scrollLeft = 0;
        const cellWidth = column.renderWidth;
        for (let i = 0; i < visibleColumn.length; i++) {
          const currCol = visibleColumn[i];
          if (currCol === column || currCol.id === column.id) {
            break;
          }
          scrollLeft += currCol.renderWidth;
        }
        if (scrollLeft < bodyScrollLeft) {
          return $xeTable.scrollTo(scrollLeft - leftFixedWidth - 1);
        }
        return $xeTable.scrollTo(scrollLeft + cellWidth - (bodyWidth - rightFixedWidth - 1));
      }
    }
  }
  return Promise.resolve();
}

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/src/cell.js
var import_xe_utils4 = __toESM(require_xe_utils());
var { getI18n: getI18n2, getIcon, renderer, formats: formats2, renderEmptyElement } = VxeUI;
function renderTitlePrefixIcon(params) {
  const { $table, column } = params;
  const titlePrefix = column.titlePrefix || column.titleHelp;
  if (titlePrefix) {
    return h("i", {
      class: ["vxe-cell-title-prefix-icon", titlePrefix.iconStatus ? `theme--${titlePrefix.iconStatus}` : "", titlePrefix.icon || getIcon().TABLE_TITLE_PREFIX],
      onMouseenter(evnt) {
        $table.triggerHeaderTitleEvent(evnt, titlePrefix, params);
      },
      onMouseleave(evnt) {
        $table.handleTargetLeaveEvent(evnt);
      }
    });
  }
  return renderEmptyElement($table);
}
function renderTitleSuffixIcon(params) {
  const { $table, column } = params;
  const titleSuffix = column.titleSuffix;
  if (titleSuffix) {
    return h("i", {
      class: ["vxe-cell-title-suffix-icon", titleSuffix.iconStatus ? `theme--${titleSuffix.iconStatus}` : "", titleSuffix.icon || getIcon().TABLE_TITLE_SUFFIX],
      onMouseenter(evnt) {
        $table.triggerHeaderTitleEvent(evnt, titleSuffix, params);
      },
      onMouseleave(evnt) {
        $table.handleTargetLeaveEvent(evnt);
      }
    });
  }
  return renderEmptyElement($table);
}
function renderCellDragIcon(params) {
  const { $table, column } = params;
  const { context } = $table;
  const tableSlots = context.slots;
  const tableProps = $table.props;
  const { slots } = column;
  const { dragConfig } = tableProps;
  const { computeRowDragOpts } = $table.getComputeMaps();
  const rowDragOpts = computeRowDragOpts.value;
  const { icon, trigger, disabledMethod } = rowDragOpts;
  const rDisabledMethod = disabledMethod || (dragConfig ? dragConfig.rowDisabledMethod : null);
  const isDisabled = rDisabledMethod && rDisabledMethod(params);
  const rowDragIconSlot = (slots ? slots.rowDragIcon || slots["row-drag-icon"] : null) || tableSlots.rowDragIcon || tableSlots["row-drag-icon"];
  const ons = {};
  if (trigger !== "cell") {
    ons.onMousedown = (evnt) => {
      if (!isDisabled) {
        $table.handleCellDragMousedownEvent(evnt, params);
      }
    };
    ons.onMouseup = $table.handleCellDragMouseupEvent;
  }
  return h("span", Object.assign({ key: "dg", class: ["vxe-cell--drag-handle", {
    "is--disabled": isDisabled
  }] }, ons), rowDragIconSlot ? $table.callSlot(rowDragIconSlot, params) : [
    h("i", {
      class: icon || (dragConfig ? dragConfig.rowIcon : "") || getIcon().TABLE_DRAG_ROW
    })
  ]);
}
function renderCellBaseVNs(params, content) {
  const { $table, column, level } = params;
  const { dragSort } = column;
  const tableProps = $table.props;
  const { treeConfig, dragConfig } = tableProps;
  const { computeRowOpts, computeRowDragOpts, computeTreeOpts } = $table.getComputeMaps();
  const rowOpts = computeRowOpts.value;
  const rowDragOpts = computeRowDragOpts.value;
  const treeOpts = computeTreeOpts.value;
  const { showIcon, isPeerDrag, isCrossDrag, visibleMethod } = rowDragOpts;
  const rVisibleMethod = visibleMethod || (dragConfig ? dragConfig.rowVisibleMethod : null);
  const vns = [];
  if (dragSort && rowOpts.drag && ((showIcon || (dragConfig ? dragConfig.showRowIcon : false)) && (!rVisibleMethod || rVisibleMethod(params)))) {
    if (treeConfig) {
      if (treeOpts.transform && (isPeerDrag || isCrossDrag || !level)) {
        vns.push(renderCellDragIcon(params));
      }
    } else {
      vns.push(renderCellDragIcon(params));
    }
  }
  return vns.concat(import_xe_utils4.default.isArray(content) ? content : [content]);
}
function renderHeaderCellDragIcon(params) {
  const { $table, column } = params;
  const { context } = $table;
  const tableSlots = context.slots;
  const { slots } = column;
  const { computeColumnOpts, computeColumnDragOpts } = $table.getComputeMaps();
  const columnOpts = computeColumnOpts.value;
  const columnDragOpts = computeColumnDragOpts.value;
  const { showIcon, icon, trigger, isPeerDrag, isCrossDrag, visibleMethod, disabledMethod } = columnDragOpts;
  if (columnOpts.drag && showIcon && (!visibleMethod || visibleMethod(params))) {
    if (!column.fixed && (isPeerDrag || isCrossDrag || !column.parentId)) {
      const isDisabled = disabledMethod && disabledMethod(params);
      const columnDragIconSlot = (slots ? slots.columnDragIcon || slots["column-drag-icon"] : null) || tableSlots.columnDragIcon || tableSlots["column-drag-icon"];
      const ons = {};
      if (trigger !== "cell") {
        ons.onMousedown = (evnt) => {
          if (!isDisabled) {
            $table.handleHeaderCellDragMousedownEvent(evnt, params);
          }
        };
        ons.onMouseup = $table.handleHeaderCellDragMouseupEvent;
      }
      return h("span", Object.assign({ key: "dg", class: ["vxe-cell--drag-handle", {
        "is--disabled": isDisabled
      }] }, ons), columnDragIconSlot ? $table.callSlot(columnDragIconSlot, params) : [
        h("i", {
          class: icon || getIcon().TABLE_DRAG_COLUMN
        })
      ]);
    }
  }
  return renderEmptyElement($table);
}
function renderHeaderCellBaseVNs(params, content) {
  const vns = [
    renderTitlePrefixIcon(params),
    renderHeaderCellDragIcon(params),
    ...import_xe_utils4.default.isArray(content) ? content : [content],
    renderTitleSuffixIcon(params)
  ];
  return vns;
}
function renderTitleContent(params, content) {
  const { $table, column } = params;
  const tableProps = $table.props;
  const tableReactData = $table.reactData;
  const { computeTooltipOpts } = $table.getComputeMaps();
  const { showHeaderOverflow: allColumnHeaderOverflow } = tableProps;
  const { type, showHeaderOverflow } = column;
  const tooltipOpts = computeTooltipOpts.value;
  const showAllTip = tooltipOpts.showAll;
  const headOverflow = import_xe_utils4.default.isUndefined(showHeaderOverflow) || import_xe_utils4.default.isNull(showHeaderOverflow) ? allColumnHeaderOverflow : showHeaderOverflow;
  const showTitle = headOverflow === "title";
  const showTooltip = headOverflow === true || headOverflow === "tooltip";
  const ons = {};
  if (showTitle || showTooltip || showAllTip) {
    ons.onMouseenter = (evnt) => {
      if (tableReactData.isDragResize) {
        return;
      }
      if (showTitle) {
        updateCellTitle(evnt.currentTarget, column);
      } else if (showTooltip || showAllTip) {
        $table.triggerHeaderTooltipEvent(evnt, params);
      }
    };
  }
  if (showTooltip || showAllTip) {
    ons.onMouseleave = (evnt) => {
      if (tableReactData.isDragResize) {
        return;
      }
      if (showTooltip || showAllTip) {
        $table.handleTargetLeaveEvent(evnt);
      }
    };
  }
  return [
    type === "html" && import_xe_utils4.default.isString(content) ? h("span", Object.assign({ class: "vxe-cell--title", innerHTML: content }, ons)) : h("span", Object.assign({ class: "vxe-cell--title" }, ons), getSlotVNs(content))
  ];
}
function getFooterContent(params) {
  const { $table, column, _columnIndex, items, row } = params;
  const { slots, editRender, cellRender, footerFormatter } = column;
  const renderOpts = editRender || cellRender;
  const footerSlot = slots ? slots.footer : null;
  if (footerSlot) {
    return $table.callSlot(footerSlot, params);
  }
  let itemValue = "";
  if (import_xe_utils4.default.isArray(items)) {
    itemValue = items[_columnIndex];
  } else {
    itemValue = import_xe_utils4.default.get(row, column.field);
  }
  const footParams = Object.assign(params, {
    itemValue
  });
  if (footerFormatter) {
    if (import_xe_utils4.default.isFunction(footerFormatter)) {
      return [
        h("span", {
          class: "vxe-cell--label"
        }, `${footerFormatter(footParams)}`)
      ];
    }
    const isArr = import_xe_utils4.default.isArray(footerFormatter);
    const gFormatOpts = isArr ? formats2.get(footerFormatter[0]) : formats2.get(footerFormatter);
    const footerFormatMethod = gFormatOpts ? gFormatOpts.tableFooterCellFormatMethod : null;
    if (footerFormatMethod) {
      return [
        h("span", {
          class: "vxe-cell--label"
        }, `${isArr ? footerFormatMethod(footParams, ...footerFormatter.slice(1)) : footerFormatMethod(footParams)}`)
      ];
    }
    return [
      h("span", {
        class: "vxe-cell--label"
      }, "")
    ];
  }
  if (renderOpts) {
    const compConf = renderer.get(renderOpts.name);
    if (compConf) {
      const rtFooter = compConf.renderTableFooter || compConf.renderFooter;
      if (rtFooter) {
        return getSlotVNs(rtFooter(renderOpts, footParams));
      }
    }
  }
  return [
    h("span", {
      class: "vxe-cell--label"
    }, formatText(itemValue, 1))
  ];
}
function getDefaultCellLabel(params) {
  const { $table, row, column } = params;
  return formatText($table.getCellLabel(row, column), 1);
}
function renderCellHandle(params) {
  const { column, $table } = params;
  const tableProps = $table.props;
  const { editConfig } = tableProps;
  const { type, treeNode, rowGroupNode, editRender } = column;
  const { computeEditOpts, computeCheckboxOpts } = $table.getComputeMaps();
  const checkboxOpts = computeCheckboxOpts.value;
  const editOpts = computeEditOpts.value;
  const isDeepCell = treeNode || rowGroupNode;
  switch (type) {
    case "seq":
      return isDeepCell ? Cell.renderDeepIndexCell(params) : Cell.renderSeqCell(params);
    case "radio":
      return isDeepCell ? Cell.renderDeepRadioCell(params) : Cell.renderRadioCell(params);
    case "checkbox":
      return checkboxOpts.checkField ? isDeepCell ? Cell.renderDeepSelectionCellByProp(params) : Cell.renderCheckboxCellByProp(params) : isDeepCell ? Cell.renderDeepSelectionCell(params) : Cell.renderCheckboxCell(params);
    case "expand":
      return Cell.renderExpandCell(params);
    case "html":
      return isDeepCell ? Cell.renderDeepHTMLCell(params) : Cell.renderHTMLCell(params);
  }
  if (isEnableConf(editConfig) && editRender) {
    return editOpts.mode === "cell" ? isDeepCell ? Cell.renderDeepCellEdit(params) : Cell.renderCellEdit(params) : isDeepCell ? Cell.renderDeepRowEdit(params) : Cell.renderRowEdit(params);
  }
  return isDeepCell ? Cell.renderDeepCell(params) : Cell.renderDefaultCell(params);
}
function renderHeaderHandle(params) {
  const { column, $table } = params;
  const tableProps = $table.props;
  const { editConfig } = tableProps;
  const { type, filters, sortable, editRender } = column;
  switch (type) {
    case "seq":
      return Cell.renderSeqHeader(params);
    case "radio":
      return Cell.renderRadioHeader(params);
    case "checkbox":
      return Cell.renderCheckboxHeader(params);
    case "html":
      if (filters && sortable) {
        return Cell.renderSortAndFilterHeader(params);
      } else if (sortable) {
        return Cell.renderSortHeader(params);
      } else if (filters) {
        return Cell.renderFilterHeader(params);
      }
      break;
  }
  if (editConfig && editRender) {
    return Cell.renderEditHeader(params);
  } else if (filters && sortable) {
    return Cell.renderSortAndFilterHeader(params);
  } else if (sortable) {
    return Cell.renderSortHeader(params);
  } else if (filters) {
    return Cell.renderFilterHeader(params);
  }
  return Cell.renderDefaultHeader(params);
}
function renderFooterHandle(params) {
  return Cell.renderDefaultFooter(params);
}
var Cell = {
  createColumn($xeTable, columnOpts) {
    const { type } = columnOpts;
    const renConfs = {
      renderHeader: renderHeaderHandle,
      renderCell: renderCellHandle,
      renderFooter: renderFooterHandle
    };
    if (type === "expand") {
      renConfs.renderData = Cell.renderExpandData;
    }
    return createColumn($xeTable, columnOpts, renConfs);
  },
  /**
   * 列头标题
   */
  renderHeaderTitle(params) {
    const { $table, column } = params;
    const { slots, editRender, cellRender } = column;
    const renderOpts = editRender || cellRender;
    const headerSlot = slots ? slots.header : null;
    if (headerSlot) {
      return renderTitleContent(params, $table.callSlot(headerSlot, params));
    }
    if (renderOpts) {
      const compConf = renderer.get(renderOpts.name);
      if (compConf) {
        const rtHeader = compConf.renderTableHeader || compConf.renderHeader;
        if (rtHeader) {
          return renderTitleContent(params, getSlotVNs(rtHeader(renderOpts, params)));
        }
      }
    }
    return renderTitleContent(params, formatText(column.getTitle(), 1));
  },
  renderDefaultHeader(params) {
    return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params));
  },
  renderDefaultCell(params) {
    const { $table, row, column } = params;
    const tableReactData = $table.reactData;
    const tableInternalData = $table.internalData;
    const { isRowGroupStatus } = tableReactData;
    const { slots, editRender, cellRender, rowGroupNode } = column;
    const renderOpts = editRender || cellRender;
    const defaultSlot = slots ? slots.default : null;
    if (defaultSlot) {
      return renderCellBaseVNs(params, $table.callSlot(defaultSlot, params));
    }
    if (renderOpts) {
      const compConf = renderer.get(renderOpts.name);
      if (compConf) {
        const rtCell = compConf.renderTableCell || compConf.renderCell;
        const rtDefault = compConf.renderTableDefault || compConf.renderDefault;
        const renderFn = editRender ? rtCell : rtDefault;
        if (renderFn) {
          return renderCellBaseVNs(params, getSlotVNs(renderFn(renderOpts, Object.assign({ $type: editRender ? "edit" : "cell" }, params))));
        }
      }
    }
    let cellValue = "";
    if (isRowGroupStatus && rowGroupNode && row.isAggregate) {
      const { fullColumnFieldData } = tableInternalData;
      const { computeRowGroupOpts } = $table.getComputeMaps();
      const rowGroupOpts = computeRowGroupOpts.value;
      const { showTotal, totalMethod, contentMethod, mapChildrenField } = rowGroupOpts;
      const groupField = row.groupField;
      cellValue = row.groupContent;
      const childList = mapChildrenField ? row[mapChildrenField] || [] : [];
      const totalValue = childList.length;
      const colRest = fullColumnFieldData[groupField] || {};
      const params2 = {
        $table,
        groupField,
        groupColumn: colRest ? colRest.column : null,
        column,
        groupValue: cellValue,
        children: childList,
        totalValue
      };
      if (contentMethod) {
        cellValue = `${contentMethod(params2)}`;
      }
      if (showTotal) {
        cellValue = getI18n2("vxe.table.rowGroupContentTotal", [cellValue, totalMethod ? totalMethod(params2) : totalValue, totalValue]);
      }
    } else if (!(isRowGroupStatus && row.isAggregate)) {
      cellValue = $table.getCellLabel(row, column);
    }
    const cellPlaceholder = editRender ? editRender.placeholder : "";
    return renderCellBaseVNs(params, [
      h("span", {
        class: "vxe-cell--label"
      }, [
        // 如果设置占位符
        editRender && eqEmptyValue(cellValue) ? h("span", {
          class: "vxe-cell--placeholder"
        }, formatText(getFuncText(cellPlaceholder), 1)) : h("span", formatText(cellValue, 1))
      ])
    ]);
  },
  renderDeepCell(params) {
    return Cell.renderDeepNodeBtn(params, Cell.renderDefaultCell(params));
  },
  renderDefaultFooter(params) {
    return getFooterContent(params);
  },
  /**
   * 行分组
   */
  renderRowGroupBtn(params, cellVNodes) {
    const { $table } = params;
    const tableReactData = $table.reactData;
    const tableInternalData = $table.internalData;
    const { row, level } = params;
    const { computeRowGroupOpts } = $table.getComputeMaps();
    const { rowGroupExpandedFlag } = tableReactData;
    const { rowGroupExpandedMaps } = tableInternalData;
    const rowGroupOpts = computeRowGroupOpts.value;
    const { padding, indent } = rowGroupOpts;
    const rowid = getRowid($table, row);
    const isExpand = !!rowGroupExpandedFlag && !!rowGroupExpandedMaps[rowid];
    return h("div", {
      class: ["vxe-row-group--tree-node", {
        "is--expanded": isExpand
      }],
      style: padding && indent ? {
        paddingLeft: `${level * indent}px`
      } : void 0
    }, [
      h("span", {
        class: "vxe-row-group--node-btn",
        onClick(evnt) {
          $table.triggerRowGroupExpandEvent(evnt, params);
        }
      }, [
        h("i", {
          class: isExpand ? getIcon().TABLE_ROW_GROUP_OPEN : getIcon().TABLE_ROW_GROUP_CLOSE
        })
      ]),
      h("div", {
        class: "vxe-row-group-cell"
      }, cellVNodes)
    ]);
  },
  /**
   * 树
   */
  renderTreeNodeBtn(params, cellVNodes) {
    const { $table, isHidden } = params;
    const tableReactData = $table.reactData;
    const tableInternalData = $table.internalData;
    const { row, column, level } = params;
    const { slots } = column;
    const iconSlot = slots ? slots.icon : null;
    if (iconSlot) {
      return $table.callSlot(iconSlot, params);
    }
    const { computeTreeOpts } = $table.getComputeMaps();
    const { treeExpandedFlag } = tableReactData;
    const { fullAllDataRowIdData, treeExpandedMaps, treeExpandLazyLoadedMaps } = tableInternalData;
    const treeOpts = computeTreeOpts.value;
    const { padding, indent, lazy, trigger, iconLoaded, showIcon, iconOpen, iconClose } = treeOpts;
    const childrenField = treeOpts.children || treeOpts.childrenField;
    const hasChildField = treeOpts.hasChild || treeOpts.hasChildField;
    const rowChilds = row[childrenField];
    const hasChild = rowChilds && rowChilds.length;
    let hasLazyChilds = false;
    let isActive = false;
    let isLazyLoading = false;
    let isLazyLoaded = false;
    const ons = {};
    if (!isHidden) {
      const rowid = getRowid($table, row);
      isActive = !!treeExpandedFlag && !!treeExpandedMaps[rowid];
      if (lazy) {
        const rest = fullAllDataRowIdData[rowid];
        isLazyLoading = !!treeExpandLazyLoadedMaps[rowid];
        hasLazyChilds = row[hasChildField];
        isLazyLoaded = !!rest.treeLoaded;
      }
    }
    if (!trigger || trigger === "default") {
      ons.onClick = (evnt) => {
        $table.triggerTreeExpandEvent(evnt, params);
      };
    }
    return h("div", {
      class: ["vxe-cell--tree-node", {
        "is--active": isActive
      }],
      style: padding && indent ? {
        paddingLeft: `${level * indent}px`
      } : void 0
    }, [
      showIcon && (lazy ? isLazyLoaded ? hasChild : hasChild || hasLazyChilds : hasChild) ? [
        h("div", Object.assign({ class: "vxe-cell--tree-btn" }, ons), [
          h("i", {
            class: isLazyLoading ? iconLoaded || getIcon().TABLE_TREE_LOADED : isActive ? iconOpen || getIcon().TABLE_TREE_OPEN : iconClose || getIcon().TABLE_TREE_CLOSE
          })
        ])
      ] : null,
      h("div", {
        class: "vxe-tree-cell"
      }, cellVNodes)
    ]);
  },
  /**
   * 层级节点。
   * 行分组、树结构
   */
  renderDeepNodeBtn(params, cellVNodes) {
    const { row, column } = params;
    const { rowGroupNode } = column;
    if (rowGroupNode && row.isAggregate) {
      return [Cell.renderRowGroupBtn(params, cellVNodes)];
    }
    return [Cell.renderTreeNodeBtn(params, cellVNodes)];
  },
  /**
   * 序号
   */
  renderSeqHeader(params) {
    const { $table, column } = params;
    const { slots } = column;
    const headerSlot = slots ? slots.header : null;
    return renderHeaderCellBaseVNs(params, renderTitleContent(params, headerSlot ? $table.callSlot(headerSlot, params) : formatText(column.getTitle(), 1)));
  },
  renderSeqCell(params) {
    const { $table, column } = params;
    const tableProps = $table.props;
    const { treeConfig } = tableProps;
    const { computeSeqOpts } = $table.getComputeMaps();
    const seqOpts = computeSeqOpts.value;
    const { slots } = column;
    const defaultSlot = slots ? slots.default : null;
    if (defaultSlot) {
      return renderCellBaseVNs(params, $table.callSlot(defaultSlot, params));
    }
    const { seq } = params;
    const seqMethod = seqOpts.seqMethod;
    return renderCellBaseVNs(params, [
      h("span", `${formatText(seqMethod ? seqMethod(params) : treeConfig ? seq : (seqOpts.startIndex || 0) + seq, 1)}`)
    ]);
  },
  renderDeepIndexCell(params) {
    return Cell.renderDeepNodeBtn(params, Cell.renderSeqCell(params));
  },
  /**
   * 单选
   */
  renderRadioHeader(params) {
    const { $table, column } = params;
    const { slots } = column;
    const headerSlot = slots ? slots.header : null;
    const titleSlot = slots ? slots.title : null;
    return renderHeaderCellBaseVNs(params, renderTitleContent(params, headerSlot ? $table.callSlot(headerSlot, params) : [
      h("span", {
        class: "vxe-radio--label"
      }, titleSlot ? $table.callSlot(titleSlot, params) : formatText(column.getTitle(), 1))
    ]));
  },
  renderRadioCell(params) {
    const { $table, column, isHidden } = params;
    const tableReactData = $table.reactData;
    const { computeRadioOpts } = $table.getComputeMaps();
    const { selectRadioRow } = tableReactData;
    const radioOpts = computeRadioOpts.value;
    const { slots } = column;
    const { labelField, checkMethod, visibleMethod } = radioOpts;
    const { row } = params;
    const defaultSlot = slots ? slots.default : null;
    const radioSlot = slots ? slots.radio : null;
    const isChecked = $table.eqRow(row, selectRadioRow);
    const isVisible = !visibleMethod || visibleMethod({ $table, row });
    let isDisabled = !!checkMethod;
    let ons;
    if (!isHidden) {
      ons = {
        onClick(evnt) {
          if (!isDisabled && isVisible) {
            $table.triggerRadioRowEvent(evnt, params);
          }
        }
      };
      if (checkMethod) {
        isDisabled = !checkMethod({ $table, row });
      }
    }
    const radioParams = Object.assign(Object.assign({}, params), { checked: isChecked, disabled: isDisabled, visible: isVisible });
    if (radioSlot) {
      return renderCellBaseVNs(params, $table.callSlot(radioSlot, radioParams));
    }
    const radioVNs = [];
    if (isVisible) {
      radioVNs.push(h("span", {
        class: ["vxe-radio--icon", isChecked ? getIcon().TABLE_RADIO_CHECKED : getIcon().TABLE_RADIO_UNCHECKED]
      }));
    }
    if (defaultSlot || labelField) {
      radioVNs.push(h("span", {
        class: "vxe-radio--label"
      }, defaultSlot ? $table.callSlot(defaultSlot, radioParams) : import_xe_utils4.default.get(row, labelField)));
    }
    return renderCellBaseVNs(params, [
      h("span", Object.assign({ class: ["vxe-cell--radio", {
        "is--checked": isChecked,
        "is--disabled": isDisabled
      }] }, ons), radioVNs)
    ]);
  },
  renderDeepRadioCell(params) {
    return Cell.renderDeepNodeBtn(params, Cell.renderRadioCell(params));
  },
  /**
   * 多选
   */
  renderCheckboxHeader(params) {
    const { $table, column, isHidden } = params;
    const tableReactData = $table.reactData;
    const { computeIsAllCheckboxDisabled, computeCheckboxOpts } = $table.getComputeMaps();
    const { isAllSelected: isAllCheckboxSelected, isIndeterminate: isAllCheckboxIndeterminate } = tableReactData;
    const isAllCheckboxDisabled = computeIsAllCheckboxDisabled.value;
    const { slots } = column;
    const headerSlot = slots ? slots.header : null;
    const titleSlot = slots ? slots.title : null;
    const checkboxOpts = computeCheckboxOpts.value;
    const { checkStrictly, showHeader, headerTitle } = checkboxOpts;
    const colTitle = column.getTitle();
    const ons = {};
    if (!isHidden) {
      ons.onClick = (evnt) => {
        if (!isAllCheckboxDisabled) {
          $table.triggerCheckAllEvent(evnt, !isAllCheckboxSelected);
        }
      };
    }
    const checkboxParams = Object.assign(Object.assign({}, params), { checked: isAllCheckboxSelected, disabled: isAllCheckboxDisabled, indeterminate: isAllCheckboxIndeterminate });
    if (headerSlot) {
      return renderHeaderCellBaseVNs(params, renderTitleContent(checkboxParams, $table.callSlot(headerSlot, checkboxParams)));
    }
    if (checkStrictly ? !showHeader : showHeader === false) {
      return renderHeaderCellBaseVNs(params, renderTitleContent(checkboxParams, [
        h("span", {
          class: "vxe-checkbox--label"
        }, titleSlot ? $table.callSlot(titleSlot, checkboxParams) : colTitle)
      ]));
    }
    return renderHeaderCellBaseVNs(params, renderTitleContent(checkboxParams, [
      h("span", Object.assign({ class: ["vxe-cell--checkbox", {
        "is--checked": isAllCheckboxSelected,
        "is--disabled": isAllCheckboxDisabled,
        "is--indeterminate": isAllCheckboxIndeterminate
      }], title: import_xe_utils4.default.eqNull(headerTitle) ? getI18n2("vxe.table.allTitle") : `${headerTitle || ""}` }, ons), [
        h("span", {
          class: ["vxe-checkbox--icon", isAllCheckboxIndeterminate ? getIcon().TABLE_CHECKBOX_INDETERMINATE : isAllCheckboxSelected ? getIcon().TABLE_CHECKBOX_CHECKED : getIcon().TABLE_CHECKBOX_UNCHECKED]
        })
      ].concat(titleSlot || colTitle ? [
        h("span", {
          class: "vxe-checkbox--label"
        }, titleSlot ? $table.callSlot(titleSlot, checkboxParams) : colTitle)
      ] : []))
    ]));
  },
  renderCheckboxCell(params) {
    const { $table, row, column, isHidden } = params;
    const tableProps = $table.props;
    const tableReactData = $table.reactData;
    const tableInternalData = $table.internalData;
    const { treeConfig } = tableProps;
    const { updateCheckboxFlag, isRowGroupStatus } = tableReactData;
    const { selectCheckboxMaps, treeIndeterminateRowMaps } = tableInternalData;
    const { computeCheckboxOpts } = $table.getComputeMaps();
    const checkboxOpts = computeCheckboxOpts.value;
    const { labelField, checkMethod, visibleMethod } = checkboxOpts;
    const { slots } = column;
    const defaultSlot = slots ? slots.default : null;
    const checkboxSlot = slots ? slots.checkbox : null;
    let indeterminate = false;
    let isChecked = false;
    const isVisible = !visibleMethod || visibleMethod({ $table, row });
    let isDisabled = !!checkMethod;
    const ons = {};
    if (!isHidden) {
      const rowid = getRowid($table, row);
      isChecked = !!updateCheckboxFlag && !!selectCheckboxMaps[rowid];
      ons.onClick = (evnt) => {
        if (!isDisabled && isVisible) {
          $table.triggerCheckRowEvent(evnt, params, !isChecked);
        }
      };
      if (checkMethod) {
        isDisabled = !checkMethod({ $table, row });
      }
      if (treeConfig || isRowGroupStatus) {
        indeterminate = !!treeIndeterminateRowMaps[rowid];
      }
    }
    const checkboxParams = Object.assign(Object.assign({}, params), { checked: isChecked, disabled: isDisabled, visible: isVisible, indeterminate });
    if (checkboxSlot) {
      return renderCellBaseVNs(params, $table.callSlot(checkboxSlot, checkboxParams));
    }
    const checkVNs = [];
    if (isVisible) {
      checkVNs.push(h("span", {
        class: ["vxe-checkbox--icon", indeterminate ? getIcon().TABLE_CHECKBOX_INDETERMINATE : isChecked ? getIcon().TABLE_CHECKBOX_CHECKED : getIcon().TABLE_CHECKBOX_UNCHECKED]
      }));
    }
    if (defaultSlot || labelField) {
      checkVNs.push(h("span", {
        class: "vxe-checkbox--label"
      }, defaultSlot ? $table.callSlot(defaultSlot, checkboxParams) : import_xe_utils4.default.get(row, labelField)));
    }
    return renderCellBaseVNs(params, [
      h("span", Object.assign({ class: ["vxe-cell--checkbox", {
        "is--checked": isChecked,
        "is--disabled": isDisabled,
        "is--indeterminate": indeterminate,
        "is--hidden": !isVisible
      }] }, ons), checkVNs)
    ]);
  },
  renderDeepSelectionCell(params) {
    return Cell.renderDeepNodeBtn(params, Cell.renderCheckboxCell(params));
  },
  renderCheckboxCellByProp(params) {
    const { $table, row, column, isHidden } = params;
    const tableProps = $table.props;
    const tableReactData = $table.reactData;
    const tableInternalData = $table.internalData;
    const { treeConfig } = tableProps;
    const { updateCheckboxFlag, isRowGroupStatus } = tableReactData;
    const { treeIndeterminateRowMaps } = tableInternalData;
    const { computeCheckboxOpts } = $table.getComputeMaps();
    const checkboxOpts = computeCheckboxOpts.value;
    const { labelField, checkField, checkMethod, visibleMethod } = checkboxOpts;
    const indeterminateField = checkboxOpts.indeterminateField || checkboxOpts.halfField;
    const { slots } = column;
    const defaultSlot = slots ? slots.default : null;
    const checkboxSlot = slots ? slots.checkbox : null;
    let isIndeterminate = false;
    let isChecked = false;
    const isVisible = !visibleMethod || visibleMethod({ $table, row });
    let isDisabled = !!checkMethod;
    const ons = {};
    if (!isHidden) {
      const rowid = getRowid($table, row);
      isChecked = !!updateCheckboxFlag && import_xe_utils4.default.get(row, checkField);
      ons.onClick = (evnt) => {
        if (!isDisabled && isVisible) {
          $table.triggerCheckRowEvent(evnt, params, !isChecked);
        }
      };
      if (checkMethod) {
        isDisabled = !checkMethod({ $table, row });
      }
      if (treeConfig || isRowGroupStatus) {
        isIndeterminate = !!treeIndeterminateRowMaps[rowid];
      }
    }
    const checkboxParams = Object.assign(Object.assign({}, params), { checked: isChecked, disabled: isDisabled, visible: isVisible, indeterminate: isIndeterminate });
    if (checkboxSlot) {
      return renderCellBaseVNs(params, $table.callSlot(checkboxSlot, checkboxParams));
    }
    const checkVNs = [];
    if (isVisible) {
      checkVNs.push(h("span", {
        class: ["vxe-checkbox--icon", isIndeterminate ? getIcon().TABLE_CHECKBOX_INDETERMINATE : isChecked ? getIcon().TABLE_CHECKBOX_CHECKED : getIcon().TABLE_CHECKBOX_UNCHECKED]
      }));
      if (defaultSlot || labelField) {
        checkVNs.push(h("span", {
          class: "vxe-checkbox--label"
        }, defaultSlot ? $table.callSlot(defaultSlot, checkboxParams) : import_xe_utils4.default.get(row, labelField)));
      }
    }
    return renderCellBaseVNs(params, [
      h("span", Object.assign({ class: ["vxe-cell--checkbox", {
        "is--checked": isChecked,
        "is--disabled": isDisabled,
        "is--indeterminate": indeterminateField && !isChecked ? row[indeterminateField] : isIndeterminate,
        "is--hidden": !isVisible
      }] }, ons), checkVNs)
    ]);
  },
  renderDeepSelectionCellByProp(params) {
    return Cell.renderDeepNodeBtn(params, Cell.renderCheckboxCellByProp(params));
  },
  /**
   * 展开行
   */
  renderExpandCell(params) {
    const { $table, isHidden, row, column } = params;
    const tableReactData = $table.reactData;
    const tableInternalData = $table.internalData;
    const { isRowGroupStatus } = tableReactData;
    const { rowExpandedMaps, rowExpandLazyLoadedMaps } = tableInternalData;
    const { computeExpandOpts } = $table.getComputeMaps();
    const expandOpts = computeExpandOpts.value;
    const { lazy, labelField, iconLoaded, showIcon, iconOpen, iconClose, visibleMethod } = expandOpts;
    const { slots } = column;
    const defaultSlot = slots ? slots.default : null;
    const iconSlot = slots ? slots.icon : null;
    let isActive = false;
    let isLazyLoading = false;
    if (isRowGroupStatus && row.isAggregate) {
      return renderCellBaseVNs(params, []);
    }
    if (iconSlot) {
      return renderCellBaseVNs(params, $table.callSlot(iconSlot, params));
    }
    if (!isHidden) {
      const rowid = getRowid($table, row);
      isActive = !!rowExpandedMaps[rowid];
      if (lazy) {
        isLazyLoading = !!rowExpandLazyLoadedMaps[rowid];
      }
    }
    return renderCellBaseVNs(params, [
      showIcon && (!visibleMethod || visibleMethod(params)) ? h("span", {
        class: ["vxe-table--expanded", {
          "is--active": isActive
        }],
        onMousedown(evnt) {
          evnt.stopPropagation();
        },
        onClick(evnt) {
          $table.triggerRowExpandEvent(evnt, params);
        }
      }, [
        h("i", {
          class: ["vxe-table--expand-btn", isLazyLoading ? iconLoaded || getIcon().TABLE_EXPAND_LOADED : isActive ? iconOpen || getIcon().TABLE_EXPAND_OPEN : iconClose || getIcon().TABLE_EXPAND_CLOSE]
        })
      ]) : renderEmptyElement($table),
      defaultSlot || labelField ? h("span", {
        class: "vxe-table--expand-label"
      }, defaultSlot ? $table.callSlot(defaultSlot, params) : import_xe_utils4.default.get(row, labelField)) : renderEmptyElement($table)
    ]);
  },
  renderExpandData(params) {
    const { $table, column } = params;
    const { slots, contentRender } = column;
    const contentSlot = slots ? slots.content : null;
    if (contentSlot) {
      return $table.callSlot(contentSlot, params);
    }
    if (contentRender) {
      const compConf = renderer.get(contentRender.name);
      if (compConf) {
        const rtExpand = compConf.renderTableExpand || compConf.renderExpand;
        if (rtExpand) {
          return getSlotVNs(rtExpand(contentRender, params));
        }
      }
    }
    return [];
  },
  /**
   * HTML 标签
   */
  renderHTMLCell(params) {
    const { $table, column } = params;
    const { slots } = column;
    const defaultSlot = slots ? slots.default : null;
    if (defaultSlot) {
      return renderCellBaseVNs(params, $table.callSlot(defaultSlot, params));
    }
    return renderCellBaseVNs(params, [
      h("span", {
        class: "vxe-cell--html",
        innerHTML: getDefaultCellLabel(params)
      })
    ]);
  },
  renderDeepHTMLCell(params) {
    return Cell.renderDeepNodeBtn(params, Cell.renderHTMLCell(params));
  },
  /**
   * 排序和筛选
   */
  renderSortAndFilterHeader(params) {
    return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params).concat(Cell.renderSortIcon(params).concat(Cell.renderFilterIcon(params))));
  },
  /**
   * 排序
   */
  renderSortHeader(params) {
    return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params).concat(Cell.renderSortIcon(params)));
  },
  renderSortIcon(params) {
    const { $table, column } = params;
    const { computeSortOpts } = $table.getComputeMaps();
    const sortOpts = computeSortOpts.value;
    const { showIcon, allowBtn, ascTitle, descTitle, iconLayout, iconAsc, iconDesc, iconVisibleMethod } = sortOpts;
    const { order } = column;
    if (showIcon && (!iconVisibleMethod || iconVisibleMethod(params))) {
      return [
        h("span", {
          class: ["vxe-cell--sort", `vxe-cell--sort-${iconLayout}-layout`]
        }, [
          h("i", {
            class: ["vxe-sort--asc-btn", iconAsc || getIcon().TABLE_SORT_ASC, {
              "sort--active": order === "asc"
            }],
            title: import_xe_utils4.default.eqNull(ascTitle) ? getI18n2("vxe.table.sortAsc") : `${ascTitle || ""}`,
            onClick: allowBtn ? (evnt) => {
              evnt.stopPropagation();
              $table.triggerSortEvent(evnt, column, "asc");
            } : void 0
          }),
          h("i", {
            class: ["vxe-sort--desc-btn", iconDesc || getIcon().TABLE_SORT_DESC, {
              "sort--active": order === "desc"
            }],
            title: import_xe_utils4.default.eqNull(descTitle) ? getI18n2("vxe.table.sortDesc") : `${descTitle || ""}`,
            onClick: allowBtn ? (evnt) => {
              evnt.stopPropagation();
              $table.triggerSortEvent(evnt, column, "desc");
            } : void 0
          })
        ])
      ];
    }
    return [];
  },
  /**
   * 筛选
   */
  renderFilterHeader(params) {
    return renderHeaderCellBaseVNs(params, Cell.renderHeaderTitle(params).concat(Cell.renderFilterIcon(params)));
  },
  renderFilterIcon(params) {
    const { $table, column, hasFilter } = params;
    const tableReactData = $table.reactData;
    const { filterStore } = tableReactData;
    const { computeFilterOpts } = $table.getComputeMaps();
    const filterOpts = computeFilterOpts.value;
    const { showIcon, iconNone, iconMatch, iconVisibleMethod } = filterOpts;
    if (showIcon && (!iconVisibleMethod || iconVisibleMethod(params))) {
      return [
        h("span", {
          class: ["vxe-cell--filter", {
            "is--active": filterStore.visible && filterStore.column === column
          }],
          onClick(evnt) {
            if ($table.triggerFilterEvent) {
              $table.triggerFilterEvent(evnt, params.column, params);
            }
          }
        }, [
          h("i", {
            class: ["vxe-filter--btn", hasFilter ? iconMatch || getIcon().TABLE_FILTER_MATCH : iconNone || getIcon().TABLE_FILTER_NONE],
            title: getI18n2("vxe.table.filter")
          })
        ])
      ];
    }
    return [];
  },
  /**
   * 可编辑
   */
  renderEditHeader(params) {
    const { $table, column } = params;
    const tableProps = $table.props;
    const { computeEditOpts } = $table.getComputeMaps();
    const { editConfig, editRules } = tableProps;
    const editOpts = computeEditOpts.value;
    const { sortable, filters, editRender } = column;
    let isRequired = false;
    if (editRules) {
      const columnRules = import_xe_utils4.default.get(editRules, column.field);
      if (columnRules) {
        isRequired = columnRules.some((rule) => rule.required);
      }
    }
    let editIconVNs = [];
    if (isEnableConf(editConfig)) {
      editIconVNs = [
        isRequired && editOpts.showAsterisk ? h("i", {
          class: "vxe-cell--required-icon"
        }) : renderEmptyElement($table),
        isEnableConf(editRender) && editOpts.showIcon ? h("i", {
          class: ["vxe-cell--edit-icon", editOpts.icon || getIcon().TABLE_EDIT]
        }) : renderEmptyElement($table)
      ];
    }
    return renderHeaderCellBaseVNs(params, editIconVNs.concat(Cell.renderHeaderTitle(params)).concat(sortable ? Cell.renderSortIcon(params) : []).concat(filters ? Cell.renderFilterIcon(params) : []));
  },
  // 行格编辑模式
  renderRowEdit(params) {
    const { $table, column } = params;
    const tableReactData = $table.reactData;
    const { editStore } = tableReactData;
    const { actived } = editStore;
    const { editRender } = column;
    return Cell.runRenderer(params, isEnableConf(editRender) && actived && actived.row === params.row);
  },
  renderDeepRowEdit(params) {
    return Cell.renderDeepNodeBtn(params, Cell.renderRowEdit(params));
  },
  // 单元格编辑模式
  renderCellEdit(params) {
    const { $table, column } = params;
    const tableReactData = $table.reactData;
    const { editStore } = tableReactData;
    const { actived } = editStore;
    const { editRender } = column;
    return Cell.runRenderer(params, isEnableConf(editRender) && actived && actived.row === params.row && actived.column === params.column);
  },
  renderDeepCellEdit(params) {
    return Cell.renderDeepNodeBtn(params, Cell.renderCellEdit(params));
  },
  runRenderer(params, isEdit) {
    const { $table, column } = params;
    const { slots, editRender, formatter } = column;
    const defaultSlot = slots ? slots.default : null;
    const editSlot = slots ? slots.edit : null;
    const compConf = renderer.get(editRender.name);
    const rtEdit = compConf ? compConf.renderTableEdit || compConf.renderEdit : null;
    const cellParams = Object.assign({ $type: "", isEdit }, params);
    if (isEdit) {
      cellParams.$type = "edit";
      if (editSlot) {
        return $table.callSlot(editSlot, cellParams);
      }
      if (rtEdit) {
        return getSlotVNs(rtEdit(editRender, cellParams));
      }
      return [];
    }
    if (defaultSlot) {
      return renderCellBaseVNs(params, $table.callSlot(defaultSlot, cellParams));
    }
    if (formatter) {
      return renderCellBaseVNs(params, [
        h("span", {
          class: "vxe-cell--label"
        }, getDefaultCellLabel(cellParams))
      ]);
    }
    return Cell.renderDefaultCell(cellParams);
  }
};
var cell_default = Cell;

export {
  initTpImg,
  getTpImg,
  getPropClass,
  isPx,
  isScale,
  hasClass,
  removeClass,
  addClass,
  hasControlKey,
  toCssUnit,
  getDomNode,
  getOffsetHeight,
  getPaddingTopBottomSize,
  setScrollTop,
  setScrollLeft,
  updateCellTitle,
  getEventTargetNode,
  getOffsetPos,
  getAbsolutePos,
  scrollToView,
  triggerEvent,
  convertHeaderColumnToRows,
  restoreScrollLocation,
  getRowUniqueId,
  getRowkey,
  getRowid,
  createHandleUpdateRowId,
  createHandleGetRowId,
  handleFieldOrColumn,
  handleRowidOrRow,
  toFilters,
  toTreePathSeq,
  getCellValue,
  setCellValue,
  getRefElem,
  getCellHeight,
  getColReMinWidth,
  isColumnInfo,
  watchColumn,
  assembleColumn,
  destroyColumn,
  getRootColumn,
  getOffsetSize,
  calcTreeLine,
  clearTableAllStatus,
  rowToVisible,
  colToVisible,
  cell_default
};
//# sourceMappingURL=chunk-R73ZCAIJ.js.map
