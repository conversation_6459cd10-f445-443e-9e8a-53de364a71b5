package com.bdyl.line.web.model.response.solarpanel;

import lombok.Data;

/**
 * 太阳能电池板每小时统计项。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class SolarPanelHourStatItem {
    /**
     * 小时（0-23）
     */
    private Integer hour;

    /**
     * 电池温度（摄氏度）
     */
    private Double batteryTemperature;

    /**
     * 电池电压（伏特）
     */
    private Double batteryVoltage;

    /**
     * 电池电流（安培）
     */
    private Double batteryCurrent;

    /**
     * 电池电量百分比（%）
     */
    private Double batteryLevel;

    /**
     * 直流负载电压（伏特）
     */
    private Double dcLoadVoltage;

    /**
     * 直流负载电流（安培）
     */
    private Double dcLoadCurrent;

    /**
     * 直流负载功率（瓦特）
     */
    private Double dcLoadPower;

    /**
     * 直流负载今日用电量（千瓦时）
     */
    private Double dcLoadEnergyToday;

    /**
     * 光伏电压（伏特）
     */
    private Double pvVoltage;

    /**
     * 光伏电流（安培）
     */
    private Double pvCurrent;

    /**
     * 光伏功率（瓦特）
     */
    private Double pvPower;
}
