<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import * as SceneApi from '#/api/core/scene';
import tableComp from '#/components/TableComp/table.vue';

import SceneModal from './components/SceneModal.vue';

import { formatDate } from '@vben/utils';

// 查询条件
const queryParam = reactive({
  name: undefined as string | undefined, // 场景名称
  page: 1,
  size: 10,
});

const loading = ref(false);
const modalFormRef = ref();

// 表格数据源
const tableData = reactive<any>({
  data: {},
});

// 表格相关配置
const columns = [
  { title: '场景ID', dataIndex: 'id', width: '100px' },
  { title: '场景名称', dataIndex: 'name' },
  { title: '场景描述', dataIndex: 'description' },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    customRender: ({ record }: { record: any }) => {
      return formatDate(record.createTime, 'YYYY-MM-DD HH:mm:ss');
    },
  },
  { title: '操作', dataIndex: 'operation', width: '160px' },
];

// 加载场景列表
const loadScenes = async () => {
  loading.value = true;
  try {
    const res = await SceneApi.getSceneList_Api(queryParam);
    // 更新表格数据源
    const temp = {
      data: res.data,
      page: res.page,
      size: res.size,
      total: res.total,
    };
    tableData.data = temp;
  } catch (error) {
    console.error('获取场景列表失败', error);
  } finally {
    loading.value = false;
  }
};

const success = (data: any) => {
  queryParam.page = data.pi;
  queryParam.size = data.ps;
  loadScenes();
};

// 查询
const searchTable = () => {
  queryParam.page = 1;
  loadScenes();
};

// 重置
const resetTable = () => {
  queryParam.name = undefined;
  queryParam.page = 1;
  loadScenes();
};

// 新增场景
const handleAdd = () => {
  modalFormRef.value.openModal();
};

// 编辑场景
const handleEdit = (record: any) => {
  modalFormRef.value.openModal(record);
};

// 删除场景
const handleDelete = async (record: any) => {
  try {
    await SceneApi.delScene_Api(record.id);
    message.success('删除成功');
    loadScenes();
  } catch (error) {
    message.error('删除失败');
    console.error('删除场景失败', error);
  }
};

// 场景保存成功回调
const handleSaveSuccess = () => {
  loadScenes();
};

// 初始化加载数据
onMounted(() => {
  loadScenes();
});
</script>

<template>
  <Page>
    <a-card class="table_header_search mb-5">
      <a-row :gutter="20">
        <a-col :span="6">
          <label>场景名称：</label>
          <div class="table_header_wrp_cont">
            <a-input
              v-model:value="queryParam.name"
              allow-clear
              placeholder="请输入场景名称"
            />
          </div>
        </a-col>
        <a-col :span="5">
          <a-button type="primary" class="searchBtn" @click="searchTable">
            查询
          </a-button>
          <a-button class="refBtn" @click="resetTable">重置</a-button>
        </a-col>
      </a-row>
    </a-card>

    <a-card>
      <div class="table_header_btn">
        <a-button type="primary" @click="handleAdd">新建</a-button>
      </div>

      <tableComp
        :loading="loading"
        :columns="columns"
        :data-source="tableData.data"
        @success="success"
      >
        <!-- 名称列 -->
        <template #name="{ record }">
          <a-tooltip
            v-if="record.name && record.name.length > 20"
            :title="record.name"
          >
            <span>{{ record.name.substring(0, 20) }}...</span>
          </a-tooltip>
          <span v-else>{{ record.name || '-' }}</span>
        </template>
        <!-- 描述列 -->
        <template #description="{ record }">
          <a-tooltip
            v-if="record.description && record.description.length > 20"
            :title="record.description"
          >
            <span>{{ record.description.substring(0, 20) }}...</span>
          </a-tooltip>
          <span v-else>{{ record.description || '-' }}</span>
        </template>

        <!-- 操作列 -->
        <template #operation="{ record }">
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-popconfirm
            title="场景删除后不可恢复，请确认后继续?"
            @confirm="handleDelete(record)"
          >
            <a-button type="link" danger>删除</a-button>
          </a-popconfirm>
        </template>
      </tableComp>
    </a-card>

    <!-- 场景弹窗 -->
    <SceneModal ref="modalFormRef" @success="handleSaveSuccess" />
  </Page>
</template>

<style lang="scss" scoped>
.table_header_search {
  .table_header_wrp_cont {
    // margin-top: 5px;
  }

  .searchBtn {
    // margin-top: 5px;
    margin-right: 10px;
  }

  .refBtn {
    // margin-top: 5px;
  }
}

.table_header_btn {
  margin-bottom: 16px;
}
</style>
