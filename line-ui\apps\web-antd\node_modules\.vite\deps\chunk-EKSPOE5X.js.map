{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/style/compact-item.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// handle border collapse\nfunction compactItemBorder(token, parentCls, options) {\n  const {\n    focusElCls,\n    focus,\n    borderElCls\n  } = options;\n  const childCombinator = borderElCls ? '> *' : '';\n  const hoverEffects = ['hover', focus ? 'focus' : null, 'active'].filter(Boolean).map(n => `&:${n} ${childCombinator}`).join(',');\n  return {\n    [`&-item:not(${parentCls}-last-item)`]: {\n      marginInlineEnd: -token.lineWidth\n    },\n    '&-item': _extends(_extends({\n      [hoverEffects]: {\n        zIndex: 2\n      }\n    }, focusElCls ? {\n      [`&${focusElCls}`]: {\n        zIndex: 2\n      }\n    } : {}), {\n      [`&[disabled] ${childCombinator}`]: {\n        zIndex: 0\n      }\n    })\n  };\n}\n// handle border-radius\nfunction compactItemBorderRadius(prefixCls, parentCls, options) {\n  const {\n    borderElCls\n  } = options;\n  const childCombinator = borderElCls ? `> ${borderElCls}` : '';\n  return {\n    [`&-item:not(${parentCls}-first-item):not(${parentCls}-last-item) ${childCombinator}`]: {\n      borderRadius: 0\n    },\n    [`&-item:not(${parentCls}-last-item)${parentCls}-first-item`]: {\n      [`& ${childCombinator}, &${prefixCls}-sm ${childCombinator}, &${prefixCls}-lg ${childCombinator}`]: {\n        borderStartEndRadius: 0,\n        borderEndEndRadius: 0\n      }\n    },\n    [`&-item:not(${parentCls}-first-item)${parentCls}-last-item`]: {\n      [`& ${childCombinator}, &${prefixCls}-sm ${childCombinator}, &${prefixCls}-lg ${childCombinator}`]: {\n        borderStartStartRadius: 0,\n        borderEndStartRadius: 0\n      }\n    }\n  };\n}\nexport function genCompactItemStyle(token) {\n  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    focus: true\n  };\n  const {\n    componentCls\n  } = token;\n  const compactCls = `${componentCls}-compact`;\n  return {\n    [compactCls]: _extends(_extends({}, compactItemBorder(token, compactCls, options)), compactItemBorderRadius(componentCls, compactCls, options))\n  };\n}"], "mappings": ";;;;;AAEA,SAAS,kBAAkB,OAAO,WAAW,SAAS;AACpD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB,cAAc,QAAQ;AAC9C,QAAM,eAAe,CAAC,SAAS,QAAQ,UAAU,MAAM,QAAQ,EAAE,OAAO,OAAO,EAAE,IAAI,OAAK,KAAK,CAAC,IAAI,eAAe,EAAE,EAAE,KAAK,GAAG;AAC/H,SAAO;AAAA,IACL,CAAC,cAAc,SAAS,aAAa,GAAG;AAAA,MACtC,iBAAiB,CAAC,MAAM;AAAA,IAC1B;AAAA,IACA,UAAU,SAAS,SAAS;AAAA,MAC1B,CAAC,YAAY,GAAG;AAAA,QACd,QAAQ;AAAA,MACV;AAAA,IACF,GAAG,aAAa;AAAA,MACd,CAAC,IAAI,UAAU,EAAE,GAAG;AAAA,QAClB,QAAQ;AAAA,MACV;AAAA,IACF,IAAI,CAAC,CAAC,GAAG;AAAA,MACP,CAAC,eAAe,eAAe,EAAE,GAAG;AAAA,QAClC,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,SAAS,wBAAwB,WAAW,WAAW,SAAS;AAC9D,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB,cAAc,KAAK,WAAW,KAAK;AAC3D,SAAO;AAAA,IACL,CAAC,cAAc,SAAS,oBAAoB,SAAS,eAAe,eAAe,EAAE,GAAG;AAAA,MACtF,cAAc;AAAA,IAChB;AAAA,IACA,CAAC,cAAc,SAAS,cAAc,SAAS,aAAa,GAAG;AAAA,MAC7D,CAAC,KAAK,eAAe,MAAM,SAAS,OAAO,eAAe,MAAM,SAAS,OAAO,eAAe,EAAE,GAAG;AAAA,QAClG,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,MACtB;AAAA,IACF;AAAA,IACA,CAAC,cAAc,SAAS,eAAe,SAAS,YAAY,GAAG;AAAA,MAC7D,CAAC,KAAK,eAAe,MAAM,SAAS,OAAO,eAAe,MAAM,SAAS,OAAO,eAAe,EAAE,GAAG;AAAA,QAClG,wBAAwB;AAAA,QACxB,sBAAsB;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACF;AACO,SAAS,oBAAoB,OAAO;AACzC,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,IAChF,OAAO;AAAA,EACT;AACA,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,aAAa,GAAG,YAAY;AAClC,SAAO;AAAA,IACL,CAAC,UAAU,GAAG,SAAS,SAAS,CAAC,GAAG,kBAAkB,OAAO,YAAY,OAAO,CAAC,GAAG,wBAAwB,cAAc,YAAY,OAAO,CAAC;AAAA,EAChJ;AACF;", "names": []}