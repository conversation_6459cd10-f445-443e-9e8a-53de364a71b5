package com.bdyl.line.web.model.response.camera;

import java.util.List;
import java.util.Map;

import lombok.Builder;
import lombok.Data;

/**
 * 媒体流响应DTO
 */
@Data
@Builder
public class GBStreamResponse {

    /**
     * 操作是否成功
     */
    private boolean success;

    /**
     * 会话ID（用于后续控制）
     */
    private String sessionId;

    /**
     * 媒体服务器IP
     */
    private String mediaServerIp;

    /**
     * 媒体服务器端口
     */
    private Integer mediaServerPort;

    /**
     * 媒体流访问URL (已废弃，请使用 streamUrlInfos)
     *
     * @deprecated 使用streamUrlInfos替代
     */
    @Deprecated
    private String streamUrl;

    /**
     * 多种协议的媒体流URL (已废弃，请使用 streamUrlInfos)
     *
     * @deprecated 使用streamUrlInfos替代
     */
    @Deprecated
    private Map<StreamProtocol, String> streamUrls;

    /**
     * 媒体流URL信息列表，支持更灵活的URL描述
     */
    private List<StreamUrlInfo> streamUrlInfos;

    /**
     * 错误码（操作失败时有效）
     */
    private String errorCode;

    /**
     * 错误信息（操作失败时有效）
     */
    private String errorMessage;

    /**
     * 创建成功响应（带会话ID和流地址信息列表）
     *
     * @param sessionId 会话ID
     * @param streamUrlInfos 流地址信息列表
     * @param mediaServerIp 媒体服务器IP
     * @param mediaServerPort 媒体服务器端口
     * @return 成功响应
     */
    public static GBStreamResponse success(String sessionId, List<StreamUrlInfo> streamUrlInfos, String mediaServerIp,
        Integer mediaServerPort) {
        return GBStreamResponse.builder().success(true).sessionId(sessionId).streamUrlInfos(streamUrlInfos)
            .mediaServerIp(mediaServerIp).mediaServerPort(mediaServerPort).build();
    }

    /**
     * 创建成功响应（带会话ID和多种协议URL）
     *
     * @param sessionId 会话ID
     * @param streamUrls 多种协议的流地址
     * @param mediaServerIp 媒体服务器IP
     * @param mediaServerPort 媒体服务器端口
     * @return 成功响应
     * @deprecated 使用带流地址信息列表的方法替代
     */
    @Deprecated
    public static GBStreamResponse success(String sessionId, Map<StreamProtocol, String> streamUrls,
        String mediaServerIp, Integer mediaServerPort) {
        return GBStreamResponse.builder().success(true).sessionId(sessionId).streamUrls(streamUrls)
            .mediaServerIp(mediaServerIp).mediaServerPort(mediaServerPort).build();
    }

    /**
     * 创建成功响应（带会话ID和单一URL，兼容旧版本）
     *
     * @param sessionId 会话ID
     * @param streamUrl 流地址
     * @param mediaServerIp 媒体服务器IP
     * @param mediaServerPort 媒体服务器端口
     * @return 成功响应
     * @deprecated 使用带流地址信息列表的方法替代
     */
    @Deprecated
    public static GBStreamResponse success(String sessionId, String streamUrl, String mediaServerIp,
        Integer mediaServerPort) {
        return GBStreamResponse.builder().success(true).sessionId(sessionId).streamUrl(streamUrl)
            .mediaServerIp(mediaServerIp).mediaServerPort(mediaServerPort).build();
    }

    /**
     * 创建成功响应（无参数）
     *
     * @return 成功响应
     */
    public static GBStreamResponse success() {
        return GBStreamResponse.builder().success(true).build();
    }

    /**
     * 创建失败响应
     *
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     * @return 失败响应
     */
    public static GBStreamResponse failed(String errorCode, String errorMessage) {
        return GBStreamResponse.builder().success(false).errorCode(errorCode).errorMessage(errorMessage).build();
    }
}
