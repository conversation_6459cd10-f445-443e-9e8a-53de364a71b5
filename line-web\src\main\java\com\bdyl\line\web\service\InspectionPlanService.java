package com.bdyl.line.web.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

import com.bdyl.boot.data.query.Page;
import com.bdyl.line.web.entity.InspectionPlanEntity;
import com.bdyl.line.web.model.request.inspection.InspectionPlanPageRequest;
import com.bdyl.line.web.model.request.inspection.InspectionPlanRequest;
import com.bdyl.line.web.model.response.inspection.InspectionPlanExportDTO;
import com.bdyl.line.web.model.response.inspection.InspectionPlanResponse;

/**
 * 巡检计划服务接口
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface InspectionPlanService extends IService<InspectionPlanEntity> {

    /**
     * 创建巡检计划
     *
     * @param request 请求对象
     * @param currentOrganId 当前组织ID
     * @return 是否成功
     */
    boolean createPlan(InspectionPlanRequest request, Long currentOrganId);

    /**
     * 更新巡检计划
     *
     * @param id 计划ID
     * @param request 请求对象
     * @return 是否成功
     */
    boolean updatePlan(Long id, InspectionPlanRequest request);

    /**
     * 删除巡检计划
     *
     * @param id 计划ID
     * @return 是否成功
     */
    boolean deletePlan(Long id);

    /**
     * 分页查询巡检计划
     *
     * @param request 分页请求
     * @return 分页结果
     */
    Page<InspectionPlanResponse> pagePlans(InspectionPlanPageRequest request);

    /**
     * 查询计划详情
     *
     * @param id 计划ID
     * @return 计划详情
     */
    InspectionPlanResponse getPlanDetail(Long id);

    /**
     * 更新计划状态
     *
     * @param id 计划ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updatePlanStatus(Long id, String status);

    /**
     * 导出巡检计划
     *
     * @param request 请求对象
     * @return 导出结果
     */
    List<InspectionPlanExportDTO> export(InspectionPlanPageRequest request);
}
