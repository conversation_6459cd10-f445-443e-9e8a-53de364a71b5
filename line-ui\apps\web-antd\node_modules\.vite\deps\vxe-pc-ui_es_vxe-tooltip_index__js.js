import {
  tooltip_default
} from "./chunk-6LBBMTDQ.js";
import "./chunk-45Y6P64G.js";
import "./chunk-PLEPSOXJ.js";
import "./chunk-JL3MBCJ6.js";
import {
  dynamicApp
} from "./chunk-TLDIGKI7.js";
import {
  VxeUI
} from "./chunk-TV7URO3H.js";
import "./chunk-JUL7CFXM.js";
import "./chunk-ZLVVKZUX.js";
import "./chunk-CMAVT37G.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/tooltip/index.js
var VxeTooltip = Object.assign({}, tooltip_default, {
  install(app) {
    app.component(tooltip_default.name, tooltip_default);
  }
});
dynamicApp.use(VxeTooltip);
VxeUI.component(tooltip_default);
var Tooltip = VxeTooltip;
var tooltip_default2 = VxeTooltip;

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-tooltip/index.js
var vxe_tooltip_default = tooltip_default2;
export {
  Tooltip,
  VxeTooltip,
  vxe_tooltip_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-tooltip_index__js.js.map
