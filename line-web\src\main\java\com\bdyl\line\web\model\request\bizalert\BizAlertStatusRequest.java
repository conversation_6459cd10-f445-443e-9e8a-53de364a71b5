package com.bdyl.line.web.model.request.bizalert;

import java.time.LocalDateTime;
import java.util.List;

import jakarta.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 业务报警请求对象，用于创建和更新业务报警。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class BizAlertStatusRequest {

    /**
     * 核实状态 {@link com.bdyl.line.common.constant.enums.VerifyStatusEnum}
     */
    @NotBlank(message = "核实状态不能为空")
    private String verifyStatus;

    /**
     * 处理图片列表
     */
    private List<String> handleImages;

    /**
     * 处理说明
     */
    private String handleDesc;

    /**
     * 处理人ID
     */
    private Long handleId;

    /**
     * 处理人名称
     */
    private String handleName;
    /**
     * 处理时间
     */
    private String handleTime;

    /**
     * 禁止报警
     */
    private Boolean forbidAlarm;

    /**
     * 解除禁止的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime forbidAlarmTime;
}
