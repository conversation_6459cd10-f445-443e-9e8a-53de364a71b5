package com.bdyl.line.web.model.response.bizalert;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 业务报警响应对象，用于业务报警相关接口的返回。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class BizAlertExportDTO {
    /**
     * 报警类型
     */
    @ExcelProperty("报警类型")
    private String type;
    /**
     * 摄像头名称
     */
    @ExcelProperty("摄像头名称")
    private String cameraName;
    /**
     * 区域描述
     */
    @ExcelProperty("区域")
    private String region;
    /**
     * 地址
     */
    @ExcelProperty("地址")
    private String address;
    /**
     * 场景名
     */
    @ExcelProperty("场景名")
    private String sceneName;
    /**
     * 核实状态
     */
    @ExcelProperty("核实状态")
    private String verifyStatus;
    /**
     * 报警图
     */
    @ExcelProperty("报警图")
    private String image;
    /**
     * 报警视频
     */
    @ExcelProperty("报警视频")
    private String video;
    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}
