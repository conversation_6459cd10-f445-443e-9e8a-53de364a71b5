package com.bdyl.line.web.model.response.camera;

import java.util.List;

import lombok.Data;

/**
 * 场景摄像头数量响应
 */
@Data
public class SceneCameraCountResponse {
    /**
     * 场景ID
     */
    private Long sceneId;
    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 场景描述
     */
    private String sceneDesc;
    /**
     * 摄像头数量
     */
    private Integer cameraCount;

    /**
     * 摄像头列表
     */
    private List<CameraResponse> cameraList;
}
