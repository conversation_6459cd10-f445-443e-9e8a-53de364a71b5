import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    name: 'VideoMonitoring',
    path: '/video-monitoring',
    meta: {
      icon: 'mdi:video', // 可根据实际需求替换图标（如使用其他mdi图标）
      order: 5, // 建议根据现有路由顺序调整（basemanagement为20，可设为25）
      title: $t('page.videomonitoring.title'), // 需在国际化文件中添加对应翻译
      authority: ['camera:menu'],
    },
    children: [
      {
        name: 'RealTimeMonitoring',
        path: 'real-time-monitoring',
        component: () =>
          import('#/views/video-monitoring/real-time-monitoring/index.vue'), // 需确保对应视图文件存在
        meta: {
          icon: 'mdi:video-outline',
          order: 30,
          title: $t('page.videomonitoring.realTimeTitle'), // 需在国际化文件中添加对应翻译
          authority: ['cameraPlay:menu'],
        },
      },
      {
        name: 'VideoPlayback',
        path: 'video-playback',
        component: () =>
          import('#/views/video-monitoring/video-playback/index.vue'), // 需确保对应视图文件存在
        meta: {
          icon: 'mdi:video-box',
          order: 40,
          title: $t('page.videomonitoring.playbackTitle'), // 需在国际化文件中添加对应翻译
          authority: ['cameraPlayback:menu'],
        },
      },
    ],
  },
];

export default routes;
