package com.bdyl.line.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bdyl.line.web.entity.InspectionRecordEntity;

/**
 * 巡检记录Mapper接口，提供巡检记录相关的数据库操作。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Mapper
public interface InspectionRecordMapper extends BaseMapper<InspectionRecordEntity> {

    /**
     * 根据任务摄像头关联ID查询记录
     *
     * @param taskCameraId 任务摄像头关联ID
     * @return 记录实体
     */
    InspectionRecordEntity selectByTaskCameraId(@Param("taskCameraId") Long taskCameraId);
}
