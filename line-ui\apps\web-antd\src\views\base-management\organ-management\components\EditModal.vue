<script lang="ts" setup>
import { computed, nextTick, reactive, ref } from 'vue';

import { message } from 'ant-design-vue';

import { getRegion_Api } from '#/api/core';
import { addOrgan_Api, updateOrgan_Api } from '#/api/core/organ';
import { useDictStore } from '#/store';
import { useUserStore } from '@vben/stores';

const emit = defineEmits(['success']);

const userStore = useUserStore();

const organType = userStore.userInfo?.organType;
const regionCode = userStore.userInfo?.regionCode;

const dictStore = useDictStore();

// 根据当前用户的组织类型过滤区域级别选项
const regionLevelOptions = computed(() => {
  const allOptions = dictStore
    .getDictOptions('RegionLevelEnum')
    .filter((item: any) => item.dictValue !== 'PLATFORM');

  // 如果当前用户是省级，只显示市级选项
  if (organType === 'PROVINCE') {
    return allOptions.filter((item: any) => item.dictValue === 'CITY');
  }

  // 平台级保持原有逻辑
  return allOptions;
});

const open = ref<boolean>(false);
const modalTitle = ref<string>('');
const formRef = ref();
const loading = ref(false);
const labelCol = { span: 6 };
const wrapperCol = { span: 16 };

// 区域相关
const regionOptions = ref<any[]>([]);
const regionLoading = ref(false);

// 表单数据
const formData = reactive<any>({
  id: undefined,
  orgType: undefined,
  platformName: undefined,
  regionCode: undefined,
  adminPhone: undefined,
  adminAccount: undefined,
  adminPassword: undefined,
  remark: undefined,
  fullRegionName: undefined, // 行政区域名称
});

// 手机号验证规则
const validatePhone = (_rule: any, value: string) => {
  if (!value) {
    return Promise.resolve();
  }
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(value)) {
    return Promise.reject(new Error('请输入正确的手机号码'));
  }
  return Promise.resolve();
};

// 表单验证规则
const formRules = {
  orgType: [{ required: true, message: '请选择组织类型', trigger: 'change' }],
  platformName: [
    { required: true, message: '请输入平台名称', trigger: 'blur' },
  ],
  fullRegionName: [
    { required: true, message: '请选择地区', trigger: 'change' },
  ],
  adminPhone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' },
  ],
  adminAccount: [
    { required: true, message: '请输入管理员账号', trigger: 'blur' },
  ],
  adminPassword: [
    { required: true, message: '请输入管理员密码', trigger: 'blur' },
    { max: 72, message: '密码最长72个字符', trigger: 'blur' },
  ],
};

// 是否为编辑模式
const isEdit = computed(() => !!formData.id);

// 动态加载区域数据
const loadRegionData = async (selectedOptions: any[]) => {
  const targetOption = selectedOptions[selectedOptions.length - 1];
  targetOption.loading = true;

  try {
    const params = {
      parentCode: targetOption.code,
    };
    const children = await getRegion_Api(params);

    targetOption.loading = false;
    if (children && children.length > 0) {
      // 为子区域设置isLeaf属性
      const childrenWithLeaf = children.map((item: any) => ({
        ...item,
        isLeaf: false, // 默认都不是叶子节点，加载时再判断
        value: JSON.stringify({ name: item.name, code: item.code }),
        label: item.name,
        code: item.code,
      }));
      targetOption.children = childrenWithLeaf;
    } else {
      targetOption.isLeaf = true;
    }
  } catch (error) {
    targetOption.loading = false;
    console.error('加载区域数据失败', error);
    message.error('加载区域数据失败');
  }
};

// 加载初始区域数据
const loadInitialRegions = async () => {
  try {
    regionLoading.value = true;
    let params: any;

    // 根据组织类型决定查询参数
    if (organType === 'PROVINCE') {
      // 省级用户，根据regionCode查询下级区域（市级）
      params = { parentCode: regionCode };
    } else {
      // 平台级用户，查询顶级区域（省级）
      params = { parentCode: undefined };
    }

    const regions = await getRegion_Api(params);

    // 转换区域数据格式，适配cascader组件
    return regions.map((item: any) => ({
      ...item,
      isLeaf: false, // 默认一级区域都不是叶子节点
      value: JSON.stringify({ name: item.name, code: item.code }),
      label: item.name,
      code: item.code,
    }));
  } catch (error) {
    console.error('加载区域数据失败', error);
    message.error('加载区域数据失败');
    return [];
  } finally {
    regionLoading.value = false;
  }
};
// 重置表单
const resetForm = async () => {
  await formRef.value?.resetFields();
  Object.assign(formData, {
    id: undefined,
    orgType: undefined,
    platformName: undefined,
    regionCode: undefined,
    adminPhone: undefined,
    adminAccount: undefined,
    adminPassword: undefined,
    remark: undefined,
    fullRegionName: undefined, // 行政区域名称
  });
};

// 打开弹窗
const openModal = async (type: string, record?: any) => {
  open.value = true;
  modalTitle.value = type === 'create' ? '新增组织' : '编辑组织';
  resetForm();

  // 加载初始区域数据
  regionOptions.value = await loadInitialRegions();

  if (type === 'update' && record) {
    nextTick(() => {
      // 编辑模式，只填充formData中已定义的字段
      Object.keys(formData).forEach((key: any) => {
        if (record[key] !== undefined) {
          formData[key] = record[key];
        }
      });
    });
  }
};

// 关闭弹窗
const closeModal = () => {
  open.value = false;
  resetForm();
};

// 保存组织
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;

    if (
      formData.fullRegionName &&
      formData.fullRegionName.length &&
      Array.isArray(formData.fullRegionName)
    ) {
      formData.regionCode = JSON.parse(
        formData.fullRegionName[formData.fullRegionName.length - 1],
      ).code;
      formData.fullRegionName = formData.fullRegionName
        .map((item: any) => JSON.parse(item).name)
        .join('/');
    }

    if (isEdit.value) {
      // 更新组织
      await updateOrgan_Api(formData);
      message.success('更新成功');
    } else {
      // 新增组织
      await addOrgan_Api(formData);
      message.success('添加成功');
    }

    closeModal();
    emit('success');
  } catch (error) {
    console.error('保存组织失败', error);
  } finally {
    loading.value = false;
  }
};

// 暴露组件方法
defineExpose({
  openModal,
});
</script>
<template>
  <a-modal
    v-model:open="open"
    :title="modalTitle"
    :confirm-loading="loading"
    :mask-closable="false"
    width="600px"
    @cancel="closeModal"
  >
    <a-form
      ref="formRef"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :model="formData"
      :rules="formRules"
    >
      <a-form-item label="组织类型" name="orgType">
        <a-radio-group v-model:value="formData.orgType" :disabled="isEdit">
          <a-radio
            v-for="item in regionLevelOptions"
            :key="item.dictValue"
            :value="item.dictValue"
          >
            {{ item.dictLabel }}
          </a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="地区" name="fullRegionName">
        <a-cascader
          v-model:value="formData.fullRegionName"
          :options="regionOptions"
          :load-data="loadRegionData"
          placeholder="请选择地区"
          style="width: 100%"
          change-on-select
          :loading="regionLoading"
          :disabled="isEdit"
        />
      </a-form-item>

      <a-form-item label="平台名称" name="platformName">
        <a-input
          v-model:value="formData.platformName"
          placeholder="请输入平台名称"
        />
      </a-form-item>

      <a-form-item label="管理员手机号" name="adminPhone">
        <a-input
          v-model:value="formData.adminPhone"
          placeholder="请输入管理员手机号"
        />
      </a-form-item>

      <a-form-item label="管理员账号" name="adminAccount">
        <a-input
          v-model:value="formData.adminAccount"
          placeholder="请输入管理员账号"
        />
      </a-form-item>

      <a-form-item label="管理员密码" name="adminPassword" v-if="!isEdit">
        <a-input-password
          v-model:value="formData.adminPassword"
          placeholder="请输入管理员密码"
        />
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="formData.remark"
          placeholder="请输入备注"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>
