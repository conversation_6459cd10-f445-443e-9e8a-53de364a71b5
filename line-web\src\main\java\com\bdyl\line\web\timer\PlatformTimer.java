package com.bdyl.line.web.timer;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.validation.constraints.NotBlank;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.bdyl.boot.tenant.Tenant;
import com.bdyl.boot.tenant.TenantContext;
import com.bdyl.boot.tenant.TenantContextHolder;
import com.bdyl.line.common.constant.enums.DeviceBoxStatusEnum;
import com.bdyl.line.common.constant.enums.TerminalStatusEnum;
import com.bdyl.line.web.entity.TerminalEntity;
import com.bdyl.line.web.model.request.camera.*;
import com.bdyl.line.web.model.request.terminal.TerminalRequest;
import com.bdyl.line.web.model.request.terminal.TerminalSynRequest;
import com.bdyl.line.web.model.response.camera.CameraResponse;
import com.bdyl.line.web.model.response.organ.OrganResponse;
import com.bdyl.line.web.model.response.terminal.TerminalResponse;
import com.bdyl.line.web.remote.PlatformRemoteService;
import com.bdyl.line.web.remote.model.IotDeviceType;
import com.bdyl.line.web.service.CameraService;
import com.bdyl.line.web.service.OrganService;
import com.bdyl.line.web.service.TerminalService;
import com.bdyl.line.web.utils.LineUserContext;

/**
 * 从平台同步数据
 */
@Component
@Slf4j
public class PlatformTimer {

    /**
     * 平台远程服务
     */
    @Autowired
    private PlatformRemoteService platformRemoteService;

    /**
     * 终端服务
     */
    @Autowired
    private TerminalService terminalService;

    /**
     * 组织服务
     */
    @Autowired
    private OrganService organService;

    /**
     * 摄像头service
     */
    @Autowired
    private CameraService cameraService;

    /**
     * 每10分钟同步一次
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    // @Scheduled(fixedRate = 10 * 60 * 1000)
    public void synFromPlatform() {

        try {
            log.info("=====开始从平台同步终端数据=====");
            LineUserContext.setSkipPermission(true);
            TenantContextHolder.setTenantContext(new TenantContext(new Tenant("1", "1"), false));
            startSyn();
        } catch (Exception e) {
            log.error("====从平台同步终端数据异常=====", e);
        } finally {
            LineUserContext.clear();
            TenantContextHolder.resetTenantContext();
        }

    }

    /**
     * 开始同步
     */
    public void startSyn() {
        // 获取业务系统中已有的终端列表（绕过数据权限）
        List<TerminalResponse> lineTerminalList = terminalService.listAllTerminalsForScheduler();

        // 获取平台的终端列表
        List<TerminalSynRequest> platformTerminalList = platformRemoteService.listTerminals();

        // 筛选:
        // 1、平台中存在,但业务系统中没有的进行新增
        // 2、平台中存在,业务系统中也存在的进行更新
        // 3、平台中不存在,业务系统中存在的进行删除

        // 1、平台中存在,但业务系统中没有的进行新增,找出新增列表
        List<TerminalSynRequest> addList = platformTerminalList.stream().filter(item -> lineTerminalList.stream()
            .noneMatch(terminalResponse -> terminalResponse.getCode().equals(item.getCode()))).toList();
        // 2、平台中存在,业务系统中也存在的进行更新,找出更新列表
        List<TerminalResponse> updateList = lineTerminalList.stream().filter(item -> platformTerminalList.stream()
            .anyMatch(terminalResponse -> terminalResponse.getCode().equals(item.getCode()))).toList();
        // 3、平台中不存在,业务系统中存在的进行删除,找出删除列表
        List<TerminalResponse> deleteList = lineTerminalList.stream().filter(item -> platformTerminalList.stream()
            .noneMatch(terminalSynRequest -> terminalSynRequest.getCode().equals(item.getCode()))).toList();
        //
        addTerminals(addList);
        updateTerminals(updateList);
        deleteTerminals(deleteList);
    }

    /**
     * 删除终端
     *
     * @param terminals 终端列表
     */
    private void deleteTerminals(List<TerminalResponse> terminals) {
        if (CollectionUtils.isEmpty(terminals)) {
            return;
        }
        terminals.forEach(terminal -> {
            TerminalEntity terminalEntity = new TerminalEntity();
            BeanUtils.copyProperties(terminal, terminalEntity);
            terminalService.deleteTerminal(terminal.getId());
            synCameras(terminal.getCode(), SynType.DELETE, terminalEntity);
        });

    }

    private void addTerminals(List<TerminalSynRequest> terminals) {
        if (CollectionUtils.isEmpty(terminals)) {
            return;
        }

        terminals.forEach(terminalSynRequest -> {
            OrganResponse organResponse = organService.getByRegionCode(terminalSynRequest.getRegionCode());
            if (organResponse == null) {
                log.error("未找到regionCode为{}的组织", terminalSynRequest.getRegionCode());
                return;
            }
            // 创建终端
            TerminalRequest terminalRequest = new TerminalRequest();
            terminalRequest.setName(terminalSynRequest.getName());
            terminalRequest.setCode(terminalSynRequest.getCode());
            terminalRequest.setLocation(terminalSynRequest.getLocation());
            // todo 平台的状态和本系统的状态不统一
            terminalRequest.setStatus(TerminalStatusEnum.NORMAL.getValue());
            terminalRequest.setBatteryLevel(100);
            // todo 平台的状态和本系统的状态不统一
            terminalRequest.setBoxStatus(DeviceBoxStatusEnum.NORMAL.getValue());

            TerminalEntity terminal = terminalService.createTerminal(terminalRequest, organResponse.getId());

            // 该终端下的摄像头也需要同步
            synCameras(terminalSynRequest.getCode(), SynType.ADD, terminal);
        });
    }

    /**
     * 更新终端
     *
     * @param terminals 终端列表
     */
    private void updateTerminals(List<TerminalResponse> terminals) {
        if (CollectionUtils.isEmpty(terminals)) {
            return;
        }

        terminals.forEach(terminalResponse -> {
            // 更新终端
            TerminalRequest terminalRequest = new TerminalRequest();
            terminalRequest.setId(terminalResponse.getId());
            terminalRequest.setName(terminalResponse.getName());
            terminalRequest.setCode(terminalResponse.getCode());
            terminalRequest.setLocation(terminalResponse.getLocation());
            // todo 平台的状态和本系统的状态不统一
            // terminalRequest.setStatus(TerminalStatusEnum.NORMAL.getValue());
            // terminalRequest.setBatteryLevel(100);
            // todo 平台的状态和本系统的状态不统一
            // terminalRequest.setBoxStatus(DeviceBoxStatusEnum.NORMAL.getValue());

            terminalService.updateTerminal(terminalRequest.getId(), terminalRequest);

            TerminalEntity terminalEntity = new TerminalEntity();
            BeanUtils.copyProperties(terminalResponse, terminalEntity);

            // 该终端下的摄像头也需要同步
            synCameras(terminalResponse.getCode(), SynType.UPDATE, terminalEntity);
        });
    }

    /**
     * 同步摄像头
     *
     * @param code 终端编码
     * @param synType 同步类型
     * @param terminal 终端
     */
    private void synCameras(@NotBlank(message = "终端编号不能为空") String code, SynType synType, TerminalEntity terminal) {

        switch (synType) {
            case ADD, UPDATE:
                createOrUpdateCameraList(code, terminal);
                break;
            case DELETE:
                cameraService.deleteByTerminalId(terminal.getId());
                break;
            default:
                break;
        }

    }

    /**
     * 创建或更新摄像头列表
     *
     * @param terminalCode 终端编码
     * @param terminal 终端
     */
    private void createOrUpdateCameraList(String terminalCode, TerminalEntity terminal) {
        List<CameraSynRequest> cameraSynRequests =
            platformRemoteService.listCamerasByTerminalCode(terminalCode, IotDeviceType.CAMERA).stream()
                .filter(item -> !item.getStatus().equals("DELETED")).toList();
        // 获取业务端该终端下的所有摄像头
        List<CameraResponse> camerasInLine = cameraService.listByTerminalId(terminal.getId());
        Map<String, CameraResponse> lineCameraMap =
            camerasInLine.stream().collect(Collectors.toMap(CameraResponse::getCode, Function.identity()));

        cameraSynRequests.forEach(synRequest -> {
            // 使用绕过数据权限的方法查询摄像头
            CameraResponse cameraResponse = lineCameraMap.get(synRequest.getDeviceCode());

            // 查询摄像头的国标信息
            PlatformVideoServerResp videoServerInfo =
                platformRemoteService.getCameraGbInfoByCode(synRequest.getDeviceCode());

            if (cameraResponse != null) {
                // 更新摄像头数据
                updateCameraInfo(synRequest, videoServerInfo, cameraResponse);
            } else {
                // 给终端创建摄像头
                createCameraInfo(terminal, synRequest, videoServerInfo);
            }
        });
        // 平台端已经删除的摄像头,业务端也需要删除
        lineCameraMap.forEach((cameraCode, cameraResponse) -> {
            if (cameraSynRequests.stream().noneMatch(synRequest -> synRequest.getDeviceCode().equals(cameraCode))) {
                cameraService.deleteCamera(cameraResponse.getId());
            }
        });
    }

    private void createCameraInfo(TerminalEntity terminal, CameraSynRequest synRequest,
        PlatformVideoServerResp videoServerInfo) {

        String channelId = null;
        List<String> streamUrls = null;
        List<String> modelCodes = null;
        if (videoServerInfo != null) {
            PlatformGbStream gbStreamInfo = videoServerInfo.getGbStream();
            if (gbStreamInfo != null) {
                channelId = gbStreamInfo.getChannelId();
                streamUrls = gbStreamInfo.getStreamUrlInfos().stream().map(StreamUrlInfo::getUrl).toList();
            }
            if (CollectionUtils.isNotEmpty(videoServerInfo.getCameraModel())) {
                modelCodes = videoServerInfo.getCameraModel().stream().map(ModelDTO::getModelCode).toList();
            }
        }
        CameraRequest cameraRequest = new CameraRequest();
        cameraRequest.setTerminalId(terminal.getId());
        cameraRequest.setName(synRequest.getAlias());
        cameraRequest.setCode(synRequest.getDeviceCode());
        cameraRequest.setChannel(channelId);
        cameraRequest.setLocation(synRequest.getLocation());
        cameraRequest.setLongitude(synRequest.getLongitude());
        cameraRequest.setLatitude(synRequest.getLatitude());
        cameraRequest.setStatus(synRequest.getStatus());
        cameraRequest.setStreamUrls(streamUrls);
        cameraRequest.setModelCodes(modelCodes);
        cameraRequest.setRemarks(synRequest.getRemarks());
        cameraService.createCamera(cameraRequest, terminal.getOrganId());
    }

    private void updateCameraInfo(CameraSynRequest synRequest, PlatformVideoServerResp videoServerInfo,
        CameraResponse cameraResponse) {

        String channelId = null;
        List<String> streamUrls = null;
        List<String> modelCodes = null;
        if (videoServerInfo != null) {
            PlatformGbStream gbStreamInfo = videoServerInfo.getGbStream();
            if (gbStreamInfo != null) {
                channelId = gbStreamInfo.getChannelId();
                streamUrls = gbStreamInfo.getStreamUrlInfos().stream().map(StreamUrlInfo::getUrl).toList();
            }
            if (CollectionUtils.isNotEmpty(videoServerInfo.getCameraModel())) {
                modelCodes = videoServerInfo.getCameraModel().stream().map(ModelDTO::getModelCode).toList();
            }

        }
        CameraRequest cameraRequest = new CameraRequest();
        cameraRequest.setName(synRequest.getAlias());
        cameraRequest.setChannel(channelId);
        cameraRequest.setLocation(synRequest.getLocation());
        cameraRequest.setLongitude(synRequest.getLongitude());
        cameraRequest.setLatitude(synRequest.getLatitude());
        cameraRequest.setStatus(synRequest.getStatus());
        cameraRequest.setStreamUrls(streamUrls);
        cameraRequest.setModelCodes(modelCodes);
        cameraRequest.setRemarks(synRequest.getRemarks());
        cameraService.updateCamera(cameraResponse.getId(), cameraRequest);
    }

    /**
     * 同步类型
     */
    public enum SynType {
        /**
         * 新增
         */
        ADD,
        /**
         * 更新
         */
        UPDATE,
        /**
         * 删除
         */
        DELETE
    }

}
