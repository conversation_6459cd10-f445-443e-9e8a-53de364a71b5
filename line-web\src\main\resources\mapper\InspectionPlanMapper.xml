<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bdyl.line.web.mapper.InspectionPlanMapper">

    
    <!-- 根据组织ID和计划名称查询计划 -->
    <select id="selectByOrganIdAndName" resultType="com.bdyl.line.web.entity.InspectionPlanEntity">
        SELECT * FROM t_inspection_plan 
        WHERE organ_id = #{organId} 
        AND name = #{name}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
        LIMIT 1
    </select>
    
</mapper>
