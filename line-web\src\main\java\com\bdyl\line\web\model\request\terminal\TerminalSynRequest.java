package com.bdyl.line.web.model.request.terminal;

import jakarta.validation.constraints.NotBlank;

import lombok.Data;

/**
 * 终端请求类
 */
@Data
public class TerminalSynRequest {

    /**
     * 区划编码,用于确认该终端属于哪个组织
     */
    private String regionCode;
    /**
     * 终端名称
     */
    @NotBlank(message = "终端名称不能为空")
    private String name;
    /**
     * 终端编号
     */
    @NotBlank(message = "终端编号不能为空")
    private String code;
    /**
     * 终端位置
     */
    private String location;
    /**
     * 终端状态 {@link com.bdyl.line.common.constant.enums.TerminalStatusEnum}
     */
    private String status;
    /**
     * 蓄电池电量
     */
    private Integer batteryLevel;
    /**
     * 设备箱状态 {@link com.bdyl.line.common.constant.enums.DeviceBoxStatusEnum}}
     */
    private String boxStatus;

    /**
     * 备注
     */
    private String remark;
}
