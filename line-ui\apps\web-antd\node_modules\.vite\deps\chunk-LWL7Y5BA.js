import {
  useRefs_default
} from "./chunk-YCAVULPA.js";
import {
  tooltip_default
} from "./chunk-56CPCJN6.js";
import {
  KeyCode_default
} from "./chunk-KFBVPRL2.js";
import {
  AntdIcon_default
} from "./chunk-O6J4ZVW4.js";
import {
  useInjectFormItemContext
} from "./chunk-HUL2W67K.js";
import {
  vue_types_default
} from "./chunk-NO4XDY4U.js";
import {
  _objectSpread2,
  classNames_default,
  findDOMNode,
  genComponentStyleHook,
  initDefaultProps_default,
  merge,
  resetComponent,
  useConfigInject_default,
  withInstall
} from "./chunk-7LCWIOMH.js";
import {
  _extends
} from "./chunk-LHAI6UAP.js";
import {
  computed,
  createVNode,
  defineComponent,
  onMounted,
  reactive,
  ref,
  watch
} from "./chunk-ZLVVKZUX.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/rate/util.js
function getScroll(w) {
  let ret = w.scrollX;
  const method = "scrollLeft";
  if (typeof ret !== "number") {
    const d = w.document;
    ret = d.documentElement[method];
    if (typeof ret !== "number") {
      ret = d.body[method];
    }
  }
  return ret;
}
function getClientPosition(elem) {
  let x;
  let y;
  const doc = elem.ownerDocument;
  const {
    body
  } = doc;
  const docElem = doc && doc.documentElement;
  const box = elem.getBoundingClientRect();
  x = box.left;
  y = box.top;
  x -= docElem.clientLeft || body.clientLeft || 0;
  y -= docElem.clientTop || body.clientTop || 0;
  return {
    left: x,
    top: y
  };
}
function getOffsetLeft(el) {
  const pos = getClientPosition(el);
  const doc = el.ownerDocument;
  const w = doc.defaultView || doc.parentWindow;
  pos.left += getScroll(w);
  return pos.left;
}

// ../../node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/StarFilled.js
var StarFilled = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" } }] }, "name": "star", "theme": "filled" };
var StarFilled_default = StarFilled;

// ../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.13_typescript@5.8.3_/node_modules/@ant-design/icons-vue/es/icons/StarFilled.js
function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? Object(arguments[i]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var StarFilled2 = function StarFilled3(props, context) {
  var p = _objectSpread({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread({}, p, {
    "icon": StarFilled_default
  }), null);
};
StarFilled2.displayName = "StarFilled";
StarFilled2.inheritAttrs = false;
var StarFilled_default2 = StarFilled2;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/rate/Star.js
var starProps = {
  value: Number,
  index: Number,
  prefixCls: String,
  allowHalf: {
    type: Boolean,
    default: void 0
  },
  disabled: {
    type: Boolean,
    default: void 0
  },
  character: vue_types_default.any,
  characterRender: Function,
  focused: {
    type: Boolean,
    default: void 0
  },
  count: Number,
  onClick: Function,
  onHover: Function
};
var Star_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "Star",
  inheritAttrs: false,
  props: starProps,
  emits: ["hover", "click"],
  setup(props, _ref) {
    let {
      emit
    } = _ref;
    const onHover = (e) => {
      const {
        index
      } = props;
      emit("hover", e, index);
    };
    const onClick = (e) => {
      const {
        index
      } = props;
      emit("click", e, index);
    };
    const onKeyDown = (e) => {
      const {
        index
      } = props;
      if (e.keyCode === 13) {
        emit("click", e, index);
      }
    };
    const cls = computed(() => {
      const {
        prefixCls,
        index,
        value,
        allowHalf,
        focused
      } = props;
      const starValue = index + 1;
      let className = prefixCls;
      if (value === 0 && index === 0 && focused) {
        className += ` ${prefixCls}-focused`;
      } else if (allowHalf && value + 0.5 >= starValue && value < starValue) {
        className += ` ${prefixCls}-half ${prefixCls}-active`;
        if (focused) {
          className += ` ${prefixCls}-focused`;
        }
      } else {
        className += starValue <= value ? ` ${prefixCls}-full` : ` ${prefixCls}-zero`;
        if (starValue === value && focused) {
          className += ` ${prefixCls}-focused`;
        }
      }
      return className;
    });
    return () => {
      const {
        disabled,
        prefixCls,
        characterRender,
        character,
        index,
        count,
        value
      } = props;
      const characterNode = typeof character === "function" ? character({
        disabled,
        prefixCls,
        index,
        count,
        value
      }) : character;
      let star = createVNode("li", {
        "class": cls.value
      }, [createVNode("div", {
        "onClick": disabled ? null : onClick,
        "onKeydown": disabled ? null : onKeyDown,
        "onMousemove": disabled ? null : onHover,
        "role": "radio",
        "aria-checked": value > index ? "true" : "false",
        "aria-posinset": index + 1,
        "aria-setsize": count,
        "tabindex": disabled ? -1 : 0
      }, [createVNode("div", {
        "class": `${prefixCls}-first`
      }, [characterNode]), createVNode("div", {
        "class": `${prefixCls}-second`
      }, [characterNode])])]);
      if (characterRender) {
        star = characterRender(star, props);
      }
      return star;
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/rate/style/index.js
var genRateStarStyle = (token) => {
  const {
    componentCls
  } = token;
  return {
    [`${componentCls}-star`]: {
      position: "relative",
      display: "inline-block",
      color: "inherit",
      cursor: "pointer",
      "&:not(:last-child)": {
        marginInlineEnd: token.marginXS
      },
      "> div": {
        transition: `all ${token.motionDurationMid}, outline 0s`,
        "&:hover": {
          transform: token.rateStarHoverScale
        },
        "&:focus": {
          outline: 0
        },
        "&:focus-visible": {
          outline: `${token.lineWidth}px dashed ${token.rateStarColor}`,
          transform: token.rateStarHoverScale
        }
      },
      "&-first, &-second": {
        color: token.defaultColor,
        transition: `all ${token.motionDurationMid}`,
        userSelect: "none",
        [token.iconCls]: {
          verticalAlign: "middle"
        }
      },
      "&-first": {
        position: "absolute",
        top: 0,
        insetInlineStart: 0,
        width: "50%",
        height: "100%",
        overflow: "hidden",
        opacity: 0
      },
      [`&-half ${componentCls}-star-first, &-half ${componentCls}-star-second`]: {
        opacity: 1
      },
      [`&-half ${componentCls}-star-first, &-full ${componentCls}-star-second`]: {
        color: "inherit"
      }
    }
  };
};
var genRateRtlStyle = (token) => ({
  [`&-rtl${token.componentCls}`]: {
    direction: "rtl"
  }
});
var genRateStyle = (token) => {
  const {
    componentCls
  } = token;
  return {
    [componentCls]: _extends(_extends(_extends(_extends(_extends({}, resetComponent(token)), {
      display: "inline-block",
      margin: 0,
      padding: 0,
      color: token.rateStarColor,
      fontSize: token.rateStarSize,
      lineHeight: "unset",
      listStyle: "none",
      outline: "none",
      // disable styles
      [`&-disabled${componentCls} ${componentCls}-star`]: {
        cursor: "default",
        "&:hover": {
          transform: "scale(1)"
        }
      }
    }), genRateStarStyle(token)), {
      // text styles
      [`+ ${componentCls}-text`]: {
        display: "inline-block",
        marginInlineStart: token.marginXS,
        fontSize: token.fontSize
      }
    }), genRateRtlStyle(token))
  };
};
var style_default = genComponentStyleHook("Rate", (token) => {
  const {
    colorFillContent
  } = token;
  const rateToken = merge(token, {
    rateStarColor: token["yellow-6"],
    rateStarSize: token.controlHeightLG * 0.5,
    rateStarHoverScale: "scale(1.1)",
    defaultColor: colorFillContent
  });
  return [genRateStyle(rateToken)];
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/rate/index.js
var rateProps = () => ({
  prefixCls: String,
  count: Number,
  value: Number,
  allowHalf: {
    type: Boolean,
    default: void 0
  },
  allowClear: {
    type: Boolean,
    default: void 0
  },
  tooltips: Array,
  disabled: {
    type: Boolean,
    default: void 0
  },
  character: vue_types_default.any,
  autofocus: {
    type: Boolean,
    default: void 0
  },
  tabindex: vue_types_default.oneOfType([vue_types_default.number, vue_types_default.string]),
  direction: String,
  id: String,
  onChange: Function,
  onHoverChange: Function,
  "onUpdate:value": Function,
  onFocus: Function,
  onBlur: Function,
  onKeydown: Function
});
var Rate = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ARate",
  inheritAttrs: false,
  props: initDefaultProps_default(rateProps(), {
    value: 0,
    count: 5,
    allowHalf: false,
    allowClear: true,
    tabindex: 0,
    direction: "ltr"
  }),
  // emits: ['hoverChange', 'update:value', 'change', 'focus', 'blur', 'keydown'],
  setup(props, _ref) {
    let {
      slots,
      attrs,
      emit,
      expose
    } = _ref;
    const {
      prefixCls,
      direction
    } = useConfigInject_default("rate", props);
    const [wrapSSR, hashId] = style_default(prefixCls);
    const formItemContext = useInjectFormItemContext();
    const rateRef = ref();
    const [setRef, starRefs] = useRefs_default();
    const state = reactive({
      value: props.value,
      focused: false,
      cleanedValue: null,
      hoverValue: void 0
    });
    watch(() => props.value, () => {
      state.value = props.value;
    });
    const getStarDOM = (index) => {
      return findDOMNode(starRefs.value.get(index));
    };
    const getStarValue = (index, x) => {
      const reverse = direction.value === "rtl";
      let value = index + 1;
      if (props.allowHalf) {
        const starEle = getStarDOM(index);
        const leftDis = getOffsetLeft(starEle);
        const width = starEle.clientWidth;
        if (reverse && x - leftDis > width / 2) {
          value -= 0.5;
        } else if (!reverse && x - leftDis < width / 2) {
          value -= 0.5;
        }
      }
      return value;
    };
    const changeValue = (value) => {
      if (props.value === void 0) {
        state.value = value;
      }
      emit("update:value", value);
      emit("change", value);
      formItemContext.onFieldChange();
    };
    const onHover = (e, index) => {
      const hoverValue = getStarValue(index, e.pageX);
      if (hoverValue !== state.cleanedValue) {
        state.hoverValue = hoverValue;
        state.cleanedValue = null;
      }
      emit("hoverChange", hoverValue);
    };
    const onMouseLeave = () => {
      state.hoverValue = void 0;
      state.cleanedValue = null;
      emit("hoverChange", void 0);
    };
    const onClick = (event, index) => {
      const {
        allowClear
      } = props;
      const newValue = getStarValue(index, event.pageX);
      let isReset = false;
      if (allowClear) {
        isReset = newValue === state.value;
      }
      onMouseLeave();
      changeValue(isReset ? 0 : newValue);
      state.cleanedValue = isReset ? newValue : null;
    };
    const onFocus = (e) => {
      state.focused = true;
      emit("focus", e);
    };
    const onBlur = (e) => {
      state.focused = false;
      emit("blur", e);
      formItemContext.onFieldBlur();
    };
    const onKeyDown = (event) => {
      const {
        keyCode
      } = event;
      const {
        count,
        allowHalf
      } = props;
      const reverse = direction.value === "rtl";
      if (keyCode === KeyCode_default.RIGHT && state.value < count && !reverse) {
        if (allowHalf) {
          state.value += 0.5;
        } else {
          state.value += 1;
        }
        changeValue(state.value);
        event.preventDefault();
      } else if (keyCode === KeyCode_default.LEFT && state.value > 0 && !reverse) {
        if (allowHalf) {
          state.value -= 0.5;
        } else {
          state.value -= 1;
        }
        changeValue(state.value);
        event.preventDefault();
      } else if (keyCode === KeyCode_default.RIGHT && state.value > 0 && reverse) {
        if (allowHalf) {
          state.value -= 0.5;
        } else {
          state.value -= 1;
        }
        changeValue(state.value);
        event.preventDefault();
      } else if (keyCode === KeyCode_default.LEFT && state.value < count && reverse) {
        if (allowHalf) {
          state.value += 0.5;
        } else {
          state.value += 1;
        }
        changeValue(state.value);
        event.preventDefault();
      }
      emit("keydown", event);
    };
    const focus = () => {
      if (!props.disabled) {
        rateRef.value.focus();
      }
    };
    const blur = () => {
      if (!props.disabled) {
        rateRef.value.blur();
      }
    };
    expose({
      focus,
      blur
    });
    onMounted(() => {
      const {
        autofocus,
        disabled
      } = props;
      if (autofocus && !disabled) {
        focus();
      }
    });
    const characterRender = (node, _ref2) => {
      let {
        index
      } = _ref2;
      const {
        tooltips
      } = props;
      if (!tooltips) return node;
      return createVNode(tooltip_default, {
        "title": tooltips[index]
      }, {
        default: () => [node]
      });
    };
    return () => {
      const {
        count,
        allowHalf,
        disabled,
        tabindex,
        id = formItemContext.id.value
      } = props;
      const {
        class: className,
        style
      } = attrs;
      const stars = [];
      const disabledClass = disabled ? `${prefixCls.value}-disabled` : "";
      const character = props.character || slots.character || (() => createVNode(StarFilled_default2, null, null));
      for (let index = 0; index < count; index++) {
        stars.push(createVNode(Star_default, {
          "ref": setRef(index),
          "key": index,
          "index": index,
          "count": count,
          "disabled": disabled,
          "prefixCls": `${prefixCls.value}-star`,
          "allowHalf": allowHalf,
          "value": state.hoverValue === void 0 ? state.value : state.hoverValue,
          "onClick": onClick,
          "onHover": onHover,
          "character": character,
          "characterRender": characterRender,
          "focused": state.focused
        }, null));
      }
      const rateClassName = classNames_default(prefixCls.value, disabledClass, className, {
        [hashId.value]: true,
        [`${prefixCls.value}-rtl`]: direction.value === "rtl"
      });
      return wrapSSR(createVNode("ul", _objectSpread2(_objectSpread2({}, attrs), {}, {
        "id": id,
        "class": rateClassName,
        "style": style,
        "onMouseleave": disabled ? null : onMouseLeave,
        "tabindex": disabled ? -1 : tabindex,
        "onFocus": disabled ? null : onFocus,
        "onBlur": disabled ? null : onBlur,
        "onKeydown": disabled ? null : onKeyDown,
        "ref": rateRef,
        "role": "radiogroup"
      }), [stars]));
    };
  }
});
var rate_default = withInstall(Rate);

export {
  rateProps,
  rate_default
};
//# sourceMappingURL=chunk-LWL7Y5BA.js.map
