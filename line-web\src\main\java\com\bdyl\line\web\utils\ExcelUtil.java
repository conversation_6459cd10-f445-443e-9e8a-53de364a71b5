package com.bdyl.line.web.utils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletResponse;

import com.alibaba.excel.EasyExcel;

import org.springframework.web.multipart.MultipartFile;

/**
 * Excel导入导出工具类
 */
public class ExcelUtil {
    /**
     * 导出Excel
     *
     * @param response HttpServletResponse
     * @param data 导出数据
     * @param clazz 导出类型
     * @param fileName 文件名（不带扩展名）
     * @param <T> 泛型
     * @throws IOException IO异常
     */
    public static <T> void export(HttpServletResponse response, List<T> data, Class<T> clazz, String fileName)
        throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String encodeFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodeFileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), clazz).sheet("Sheet1").doWrite(data);
    }

    /**
     * 读取Excel
     *
     * @param file 导入文件
     * @param clazz 数据类型
     * @param <T> 泛型
     * @return 数据列表
     * @throws IOException IO异常
     */
    public static <T> List<T> read(MultipartFile file, Class<T> clazz) throws IOException {
        try (InputStream is = file.getInputStream()) {
            return EasyExcel.read(is, clazz, null).sheet().head(clazz).doReadSync();
        }
    }

    /**
     * 读取指定sheet的Excel数据
     *
     * @param file 导入文件
     * @param clazz 数据类型
     * @param sheetName sheet名称
     * @param <T> 泛型
     * @return 数据列表
     * @throws IOException IO异常
     */
    public static <T> List<T> readSheet(MultipartFile file, Class<T> clazz, String sheetName) throws IOException {
        try (InputStream is = file.getInputStream()) {
            return EasyExcel.read(is, clazz, null).sheet(sheetName).head(clazz).doReadSync();
        }
    }

    /**
     * 写入Excel到输出流
     *
     * @param os 输出流
     * @param data 数据
     * @param clazz 类型
     * @param sheetName sheet名
     * @param <T> 泛型
     */
    public static <T> void write(OutputStream os, List<T> data, Class<T> clazz, String sheetName) {
        EasyExcel.write(os, clazz).sheet(sheetName).doWrite(data);
    }

    /**
     * 写入多个sheet到Excel
     *
     * @param os 输出流
     * @param sheetDataMap sheet数据映射，key为sheet名称，value为数据列表
     * @param clazz 类型
     * @param <T> 泛型
     */
    public static <T> void writeMultipleSheets(OutputStream os, Map<String, List<T>> sheetDataMap, Class<T> clazz) {
        var writer = EasyExcel.write(os, clazz).build();
        for (Map.Entry<String, List<T>> entry : sheetDataMap.entrySet()) {
            writer.write(entry.getValue(), EasyExcel.writerSheet(entry.getKey()).build());
        }
        writer.finish();
    }
}
