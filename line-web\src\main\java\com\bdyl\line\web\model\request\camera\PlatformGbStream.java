package com.bdyl.line.web.model.request.camera;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/7/9 10:00
 * @ClassName GbStreamEntity
 */
@Data
public class PlatformGbStream {

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 边缘设备编码
     */
    private String edgeCode;

    /**
     * 应用名
     */
    private String app;

    /**
     * 流ID
     */
    private String stream;

    /**
     * 通道ID
     */
    private String channelId;

    /**
     * 是否开启MP4录制
     */
    private Boolean enableMp4;

    /**
     * RTSP地址
     */
    private String rtspUrl;
    /**
     * 媒体流URL信息列表，支持更灵活的URL描述
     */
    private List<StreamUrlInfo> streamUrlInfos;
}
