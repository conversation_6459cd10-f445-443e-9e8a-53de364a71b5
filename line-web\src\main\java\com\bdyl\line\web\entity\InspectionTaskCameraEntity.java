package com.bdyl.line.web.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 巡检任务摄像头关联实体类，对应巡检任务摄像头关联表。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_inspection_task_camera", autoResultMap = true)
public class InspectionTaskCameraEntity extends BaseEntity {
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 组织ID
     */
    private Long organId;

    /**
     * 巡检任务ID
     */
    private Long taskId;

    /**
     * 摄像头ID
     */
    private Long cameraId;

    /**
     * 摄像头名称
     */
    private String cameraName;

    /**
     * 巡检状态 {@link com.bdyl.line.common.constant.enums.InspectionTaskCameraStatusEnum}
     */
    private String status;

    /**
     * 排序序号
     */
    private Integer sortOrder;
}
