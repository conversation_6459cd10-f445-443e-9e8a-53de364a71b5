package com.bdyl.line.web.service.impl;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.bdyl.boot.data.query.Page;
import com.bdyl.boot.exception.BizException;
import com.bdyl.line.common.constant.enums.InspectionCycleEnum;
import com.bdyl.line.common.constant.enums.StatusEnum;
import com.bdyl.line.common.constant.error.LineError;
import com.bdyl.line.web.entity.CameraEntity;
import com.bdyl.line.web.entity.InspectionPlanEntity;
import com.bdyl.line.web.entity.UserEntity;
import com.bdyl.line.web.mapper.CameraMapper;
import com.bdyl.line.web.mapper.InspectionPlanMapper;
import com.bdyl.line.web.mapper.UserMapper;
import com.bdyl.line.web.model.request.inspection.InspectionPlanPageRequest;
import com.bdyl.line.web.model.request.inspection.InspectionPlanRequest;
import com.bdyl.line.web.model.response.camera.CameraResponse;
import com.bdyl.line.web.model.response.inspection.InspectionPlanExportDTO;
import com.bdyl.line.web.model.response.inspection.InspectionPlanResponse;
import com.bdyl.line.web.service.InspectionPlanService;
import com.bdyl.line.web.utils.InspectionPlanValidator;

/**
 * 巡检计划服务实现类
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionPlanServiceImpl extends ServiceImpl<InspectionPlanMapper, InspectionPlanEntity>
    implements InspectionPlanService {

    /**
     * 巡检计划Mapper
     */
    private final InspectionPlanMapper inspectionPlanMapper;
    /**
     * 摄像头Mapper
     */
    private final CameraMapper cameraMapper;
    /**
     * 用户Mapper
     */
    private final UserMapper userMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createPlan(InspectionPlanRequest request, Long currentOrganId) {
        // 验证计划配置
        List<String> validationErrors = InspectionPlanValidator.validate(request);
        if (!validationErrors.isEmpty()) {
            throw new BizException("巡检计划配置错误: " + String.join(", ", validationErrors));
        }

        // 检查计划名称是否重复
        InspectionPlanEntity existingPlan =
            inspectionPlanMapper.selectByOrganIdAndName(currentOrganId, request.getName(), null);
        if (existingPlan != null) {
            throw new BizException(LineError.INSPECTION_PLAN_NAME_EXISTED);
        }

        // 验证负责人是否存在
        UserEntity user = userMapper.selectById(request.getResponsibleUserId());
        if (user == null) {
            throw new BizException(LineError.USER_NOT_EXIST);
        }

        // 验证摄像头是否存在
        List<CameraEntity> cameras = cameraMapper.selectByIds(request.getCameraIds());
        if (cameras.size() != request.getCameraIds().size()) {
            throw new BizException(LineError.CAMERA_NOT_EXIST);
        }
        // 创建计划
        InspectionPlanEntity plan = new InspectionPlanEntity();
        BeanUtils.copyProperties(request, plan);
        plan.setOrganId(currentOrganId);
        plan.setResponsibleUserName(user.getName());
        plan.setStatus(StatusEnum.ENABLE.getValue());

        // 处理时间段配置
        plan.setTimeSlots(request.getTimeSlots());

        return save(plan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePlan(Long id, InspectionPlanRequest request) {
        InspectionPlanEntity existingPlan = getById(id);
        if (existingPlan == null) {
            throw new BizException(LineError.INSPECTION_PLAN_NOT_EXIST);
        }

        // 检查计划名称是否重复
        InspectionPlanEntity duplicatePlan =
            inspectionPlanMapper.selectByOrganIdAndName(existingPlan.getOrganId(), request.getName(), id);
        if (duplicatePlan != null) {
            throw new BizException(LineError.INSPECTION_PLAN_NAME_EXISTED);
        }

        // 验证负责人是否存在
        UserEntity user = userMapper.selectById(request.getResponsibleUserId());
        if (user == null) {
            throw new BizException(LineError.USER_NOT_EXIST);
        }

        // 验证摄像头是否存在
        List<CameraEntity> cameras = cameraMapper.selectByIds(request.getCameraIds());
        if (cameras.size() != request.getCameraIds().size()) {
            throw new BizException(LineError.CAMERA_NOT_EXIST);
        }

        // 更新计划
        BeanUtils.copyProperties(request, existingPlan);
        existingPlan.setResponsibleUserName(user.getName());

        // 处理时间段配置
        existingPlan.setTimeSlots(request.getTimeSlots());

        return updateById(existingPlan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePlan(Long id) {
        InspectionPlanEntity plan = getById(id);
        if (plan == null) {
            throw new IllegalArgumentException("巡检计划不存在");
        }

        // TODO: 检查是否有未完成的任务

        return removeById(id);
    }

    @Override
    public Page<InspectionPlanResponse> pagePlans(InspectionPlanPageRequest request) {
        LambdaQueryWrapper<InspectionPlanEntity> wrapper = buildConditions(request);

        com.baomidou.mybatisplus.extension.plugins.pagination.Page<InspectionPlanEntity> queryPage =
            new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(request.getPage(), request.getSize());
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<InspectionPlanEntity> pageResult =
            inspectionPlanMapper.selectPage(queryPage, wrapper);

        if (pageResult.getRecords() == null || pageResult.getRecords().isEmpty()) {
            return Page.of(request.getPage(), 0, 0, null, Collections.emptyList());
        }

        return Page.of(request.getPage(), request.getSize(), pageResult.getTotal(), Collections.emptyList(),
            convertToResponses(pageResult.getRecords()));
    }

    private List<InspectionPlanResponse> convertToResponses(List<InspectionPlanEntity> records) {

        return records.stream().map(this::convertToResponse).collect(Collectors.toList());
    }

    private LambdaQueryWrapper<InspectionPlanEntity> buildConditions(InspectionPlanPageRequest request) {
        LambdaQueryWrapper<InspectionPlanEntity> wrapper = new LambdaQueryWrapper<>();

        if (request.getName() != null && !request.getName().trim().isEmpty()) {
            wrapper.like(InspectionPlanEntity::getName, request.getName().trim());
        }
        if (request.getStatus() != null && !request.getStatus().trim().isEmpty()) {
            wrapper.eq(InspectionPlanEntity::getStatus, request.getStatus());
        }
        if (request.getCycleType() != null) {
            wrapper.eq(InspectionPlanEntity::getCycleType, request.getCycleType());
        }
        if (request.getStartTime() != null) {
            wrapper.ge(InspectionPlanEntity::getCreateTime, request.getStartTime());
        }
        if (request.getEndTime() != null) {
            wrapper.le(InspectionPlanEntity::getCreateTime, request.getEndTime());
        }

        wrapper.orderByDesc(InspectionPlanEntity::getCreateTime);

        return wrapper;
    }

    @Override
    public InspectionPlanResponse getPlanDetail(Long id) {
        InspectionPlanEntity plan = getById(id);
        if (plan == null) {
            throw new BizException(LineError.INSPECTION_PLAN_NOT_EXIST);
        }

        InspectionPlanResponse response = convertToResponse(plan);

        // 查询摄像头详情
        if (plan.getCameraIds() != null && !plan.getCameraIds().isEmpty()) {
            List<CameraEntity> cameras = cameraMapper.selectByIds(plan.getCameraIds());
            List<CameraResponse> cameraResponses =
                cameras.stream().map(this::convertCameraToResponse).collect(Collectors.toList());
            response.setCameras(cameraResponses);
        }

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePlanStatus(Long id, String status) {
        InspectionPlanEntity plan = getById(id);
        if (plan == null) {
            throw new BizException(LineError.INSPECTION_PLAN_NOT_EXIST);
        }
        // 如果用户需要启用计划，首先查看计划中的摄像头是否都存在
        if (StatusEnum.ENABLE.getValue().equals(status)) {

            List<Long> cameraIds = plan.getCameraIds();
            if (CollectionUtils.isEmpty(cameraIds)) {
                throw new BizException(LineError.CAMERA_NOT_EXIST);
            }
            List<CameraEntity> cameraEntities = cameraMapper.selectByIds(cameraIds);
            if (CollectionUtils.isEmpty(cameraEntities) || cameraEntities.size() != cameraIds.size()) {
                throw new BizException(LineError.CAMERA_NOT_EXIST);
            }
        }

        plan.setStatus(status);
        return

        updateById(plan);
    }

    @Override
    public List<InspectionPlanExportDTO> export(InspectionPlanPageRequest request) {

        LambdaQueryWrapper<InspectionPlanEntity> wrapper = buildConditions(request);
        List<InspectionPlanEntity> plans = inspectionPlanMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(plans)) {
            return Collections.emptyList();
        }

        return plans.stream().map(item -> {
            InspectionPlanExportDTO dto = new InspectionPlanExportDTO();
            dto.setName(item.getName());
            dto.setCycleType(InspectionCycleEnum.fromValue(item.getCycleType()).getName());
            dto.setCycleValue(item.getCycleValue());
            // 获取摄像头列表
            List<String> cameraNames =
                cameraMapper.selectByIds(item.getCameraIds()).stream().map(CameraEntity::getName).toList();
            dto.setCameraNames(cameraNames.toString());
            dto.setCameraCount(cameraNames.size());
            dto.setStartDate(item.getStartDate());
            dto.setResponsibleUserName(item.getResponsibleUserName());
            dto.setStatus(StatusEnum.fromValue(item.getStatus()).getName());
            dto.setDescription(item.getDescription());
            dto.setDayValue(item.getDayValue());
            LocalTime startTime = item.getStartTime();
            LocalTime endTime = item.getEndTime();
            dto.setStartTime(startTime != null ? startTime.format(DateTimeFormatter.ofPattern("HH:mm:ss")) : "");
            dto.setEndTime(endTime != null ? endTime.format(DateTimeFormatter.ofPattern("HH:mm:ss")) : "");
            dto.setCreateTime(item.getCreateTime());
            return dto;
        }).toList();
    }

    private InspectionPlanResponse convertToResponse(InspectionPlanEntity entity) {
        InspectionPlanResponse response = new InspectionPlanResponse();
        BeanUtils.copyProperties(entity, response);
        response.setCameraCount(entity.getCameraIds() != null ? entity.getCameraIds().size() : 0);
        return response;
    }

    private CameraResponse convertCameraToResponse(CameraEntity entity) {
        CameraResponse response = new CameraResponse();
        BeanUtils.copyProperties(entity, response);
        return response;
    }
}
