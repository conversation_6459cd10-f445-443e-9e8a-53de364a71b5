{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/icon/src/icon.js", "../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/icon/index.js", "../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-icon/index.js"], "sourcesContent": ["import { defineComponent, h } from 'vue';\nimport { getConfig, createEvent, useSize } from '../../ui';\nimport XEUtils from 'xe-utils';\nexport default defineComponent({\n    name: 'VxeIcon',\n    props: {\n        name: String,\n        className: String,\n        roll: Boolean,\n        status: String,\n        size: {\n            type: String,\n            default: () => getConfig().icon.size || getConfig().size\n        }\n    },\n    emits: [\n        'click'\n    ],\n    setup(props, context) {\n        const { emit } = context;\n        const xID = XEUtils.uniqueId();\n        const { computeSize } = useSize(props);\n        const $xeIcon = {\n            xID,\n            props,\n            context\n        };\n        const clickEvent = (evnt) => {\n            emit('click', createEvent(evnt, {}));\n        };\n        const dispatchEvent = (type, params, evnt) => {\n            emit(type, createEvent(evnt, { $icon: $xeIcon }, params));\n        };\n        const iconMethods = {\n            dispatchEvent\n        };\n        const iconPrivateMethods = {};\n        Object.assign($xeIcon, iconMethods, iconPrivateMethods);\n        const renderVN = () => {\n            const { name, roll, status, className } = props;\n            const vSize = computeSize.value;\n            return h('i', {\n                class: ['vxe-icon', `vxe-icon-${name}`, `${className || ''}`, {\n                        [`size--${vSize}`]: vSize,\n                        [`theme--${status}`]: status,\n                        roll: roll\n                    }],\n                onClick: clickEvent\n            });\n        };\n        $xeIcon.renderVN = renderVN;\n        return $xeIcon;\n    },\n    render() {\n        return this.renderVN();\n    }\n});\n", "import { VxeUI } from '@vxe-ui/core';\nimport VxeIconComponent from './src/icon';\nimport { dynamicApp } from '../dynamics';\nexport const VxeIcon = Object.assign({}, VxeIconComponent, {\n    install(app) {\n        app.component(VxeIconComponent.name, VxeIconComponent);\n    }\n});\ndynamicApp.use(VxeIcon);\nVxeUI.component(VxeIconComponent);\nexport const Icon = VxeIcon;\nexport default VxeIcon;\n", "import VxeIcon from '../icon';\nexport * from '../icon';\nexport default VxeIcon;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAEA,sBAAoB;AACpB,IAAO,eAAQ,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,OAAO;AAAA,IACH,MAAM;AAAA,IACN,WAAW;AAAA,IACX,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,KAAK,QAAQ,UAAU,EAAE;AAAA,IACxD;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,EACJ;AAAA,EACA,MAAM,OAAO,SAAS;AAClB,UAAM,EAAE,KAAK,IAAI;AACjB,UAAM,MAAM,gBAAAA,QAAQ,SAAS;AAC7B,UAAM,EAAE,YAAY,IAAI,QAAQ,KAAK;AACrC,UAAM,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,UAAM,aAAa,CAAC,SAAS;AACzB,WAAK,SAAS,YAAY,MAAM,CAAC,CAAC,CAAC;AAAA,IACvC;AACA,UAAM,gBAAgB,CAAC,MAAM,QAAQ,SAAS;AAC1C,WAAK,MAAM,YAAY,MAAM,EAAE,OAAO,QAAQ,GAAG,MAAM,CAAC;AAAA,IAC5D;AACA,UAAM,cAAc;AAAA,MAChB;AAAA,IACJ;AACA,UAAM,qBAAqB,CAAC;AAC5B,WAAO,OAAO,SAAS,aAAa,kBAAkB;AACtD,UAAM,WAAW,MAAM;AACnB,YAAM,EAAE,MAAM,MAAM,QAAQ,UAAU,IAAI;AAC1C,YAAM,QAAQ,YAAY;AAC1B,aAAO,EAAE,KAAK;AAAA,QACV,OAAO,CAAC,YAAY,YAAY,IAAI,IAAI,GAAG,aAAa,EAAE,IAAI;AAAA,UACtD,CAAC,SAAS,KAAK,EAAE,GAAG;AAAA,UACpB,CAAC,UAAU,MAAM,EAAE,GAAG;AAAA,UACtB;AAAA,QACJ,CAAC;AAAA,QACL,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AACA,YAAQ,WAAW;AACnB,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO,KAAK,SAAS;AAAA,EACzB;AACJ,CAAC;;;ACrDM,IAAM,UAAU,OAAO,OAAO,CAAC,GAAG,cAAkB;AAAA,EACvD,QAAQ,KAAK;AACT,QAAI,UAAU,aAAiB,MAAM,YAAgB;AAAA,EACzD;AACJ,CAAC;AACD,WAAW,IAAI,OAAO;AACtB,MAAM,UAAU,YAAgB;AACzB,IAAM,OAAO;AACpB,IAAOC,gBAAQ;;;ACTf,IAAO,mBAAQC;", "names": ["XEUtils", "icon_default", "icon_default"]}