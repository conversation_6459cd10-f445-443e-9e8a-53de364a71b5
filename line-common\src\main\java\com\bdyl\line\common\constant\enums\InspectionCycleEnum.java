package com.bdyl.line.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 巡检周期枚举
 *
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@AllArgsConstructor
public enum InspectionCycleEnum {

    /**
     * 小时
     */
    HOUR("HOUR", "小时", "按小时巡检"),
    /**
     * 日
     */
    DAY("DAY", "日", "按日巡检"),
    /**
     * 周
     */
    WEEK("WEEK", "周", "按周巡检"),
    /**
     * 月
     */
    MONTH("MONTH", "月", "按月巡检");

    /**
     * value
     */
    private final String value;
    /**
     * 名称
     */
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据value获取name
     *
     * @param value value
     * @return name
     */
    public static InspectionCycleEnum fromValue(String value) {

        for (InspectionCycleEnum inspectionCycleEnum : InspectionCycleEnum.values()) {
            if (inspectionCycleEnum.getValue().equals(value)) {
                return inspectionCycleEnum;
            }
        }
        throw new IllegalArgumentException("无效的巡检周期值：" + value);
    }
}
