package com.bdyl.line.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 行政区划等级枚举
 */
@Getter
@AllArgsConstructor
public enum RegionLevelEnum {

    /**
     * 平台级
     */
    PLATFORM("PLATFORM", "平台级", "平台级"),
    /**
     * 省
     */
    PROVINCE("PROVINCE", "省级", "省级"),
    /**
     * 市
     */
    CITY("CITY", "市级", "市级");
    // /**
    // * 区
    // */
    // COUNTY("COUNTY", "区级", "区级"),
    // /**
    // * 镇
    // */
    // TOWN("TOWN", "镇级", "镇级"),
    // /**
    // * 村
    // */
    // VILLAGE("VILLAGE", "村级", "村级");

    /**
     * value
     */
    private final String value;
    /**
     * 名称
     */
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据等级获取枚举
     *
     * @param value
     * @return 枚举
     */
    public static RegionLevelEnum fromLevel(String value) {
        for (RegionLevelEnum rl : values()) {
            if (rl.value.equals(value)) {
                return rl;
            }
        }
        throw new IllegalArgumentException("Unknown level: " + value);
    }

    /**
     * 根据名称获取
     *
     * @param name 名称
     * @return 枚举
     */
    public static RegionLevelEnum fromName(String name) {
        for (RegionLevelEnum rl : values()) {
            if (rl.name.equals(name)) {
                return rl;
            }
        }
        throw new IllegalArgumentException("Unknown level: " + name);
    }
}
