import type { App } from 'vue';
import {
  // Need
  <PERSON>ton,
  Input,
  InputNumber,
  Layout,
  TimePicker,
  Modal,
  Card,
  Table,
  Popconfirm,
  Row,
  Col,
  Tabs,
  Select,
  DatePicker,
  Form,
  Divider,
  Pagination,
  Popover,
  List,
  Avatar,
  Tree,
  Progress,
  Radio,
  Switch,
  Checkbox,
  Descriptions,
  Image,
  Upload,
  Drawer,
  Statistic,
  Empty,
  Tag,
  Cascader,
  Watermark,
  Transfer,
  Tooltip,
  TreeSelect,
  Slider,
  Comment,
  Badge,
  Spin,
  Segmented,
  Space
} from 'ant-design-vue';

// const compList = [AntButton.Group];

export function registerGlobComp(app: App) {
  // compList.forEach((comp) => {
  //   app.component(comp.name || comp.displayName, comp);
  // });

  app
    .use(Input)
    .use(InputNumber)
    .use(Button)
    .use(Layout)
    .use(TimePicker)
    .use(Modal)
    .use(Card)
    .use(Table)
    .use(Popconfirm)
    .use(Row)
    .use(Col)
    .use(Tree)
    .use(Tabs)
    .use(Tag)
    .use(Drawer)
    .use(Progress)
    .use(Select)
    .use(DatePicker)
    .use(Popover)
    .use(Divider)
    .use(Pagination)
    .use(List)
    .use(Image)
    .use(Upload)
    .use(Radio)
    .use(Checkbox)
    .use(Descriptions)
    .use(Tooltip)
    .use(Avatar)
    .use(Switch)
    .use(Statistic)
    .use(Empty)
    .use(Cascader)
    .use(Slider)
    .use(Watermark)
    .use(Transfer)
    .use(Badge)
    .use(TreeSelect)
    .use(Comment)
    .use(Spin)
    .use(Form)
    .use(Segmented)
    .use(Space);
}
