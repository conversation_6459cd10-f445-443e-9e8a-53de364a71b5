package com.bdyl.line.web.model.request.department;

import jakarta.validation.constraints.NotNull;

import lombok.Data;

import com.bdyl.line.common.constant.enums.StatusEnum;

/**
 * 部门请求对象，用于部门的创建和更新。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class DepartmentStatusRequest {

    /**
     * 部门ID。
     */
    @NotNull(message = "部门不能为空")
    private Long id;
    /**
     * 部门状态 {@link com.bdyl.line.common.constant.enums.StatusEnum}
     */
    @NotNull(message = "状态不能为空")
    private StatusEnum status;
}
