package com.bdyl.line.web.model.response.terminal;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 属性类型枚举类
 */
@Getter
@AllArgsConstructor
public enum IotPropertyType {
    /**
     * 字符串类型
     */
    STRING("STRING", "String", "字符串类型"),

    /**
     * 数值类型
     */
    NUMBER("NUMBER", "Number", "数值类型"),

    /**
     * 布尔类型
     */
    BOOLEAN("BOOLEAN", "Boolean", "布尔类型"),

    /**
     * JSON类型
     */
    JSON("JSON", "JSON", "JSON类型"),

    /**
     * 数组类型
     */
    ARRAY("ARRAY", "Array", "数组类型"),

    /**
     * 日期时间类型
     */
    DATETIME("DATETIME", "DateTime", "日期时间类型"),

    /**
     * 二进制类型
     */
    BINARY("BINARY", "Binary", "二进制类型");

    /**
     * 值
     */
    private final String value;

    /**
     * 名称
     */
    private final String name;

    /**
     * 描述
     */
    private final String description;
}
