import { requestClient } from '#/api/request';

enum Api {
  depart = '/api/line/department',
}

// 分页查询部门
export interface Departparams {
  page: number;
  size: number;
  name?: string;
  status?: string;
}
export const getDepartmentList_Api = (params: Departparams) => {
  return requestClient.get(`${Api.depart}/page`, { params });
};

// 创建部门
export interface DepartParameter {
  id?: number;
  name: string;
  leaderId: number;
  remark: string;
  status: string;
}
export const addDepartment_Api = (data: DepartParameter) => {
  return requestClient.post(`${Api.depart}`, data);
};

// 修改部门
export const updateDepartment_Api = (id: any, data: DepartParameter) => {
  return requestClient.put(`${Api.depart}/${id}`, data);
};

// 修改部门状态
export const updateDepartmentStatus_Api = (data: any) => {
  return requestClient.put(`${Api.depart}/status`, data);
};

// 删除部门
export const deleteDepartment_Api = (id: any) => {
  return requestClient.delete(`${Api.depart}/${id}`);
};

// 获取部门详情
export const getDepartment_Api = (id: any) => {
  return requestClient.get(`${Api.depart}/${id}`);
};
