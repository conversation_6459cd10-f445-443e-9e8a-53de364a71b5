package com.bdyl.line.web.model.response.inspection;

import lombok.Data;

/**
 * 巡检任务摄像头响应对象
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class InspectionTaskCameraResponse {
    /**
     * 关联ID
     */
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 摄像头ID
     */
    private Long cameraId;

    /**
     * 摄像头名称
     */
    private String cameraName;

    /**
     * 摄像头编码
     */
    private String cameraCode;
    /**
     * 摄像头状态 {@link com.bdyl.line.common.constant.enums.CameraStatusEnum}
     */
    private String cameraStatus;

    /**
     * 巡检状态 {@link com.bdyl.line.common.constant.enums.InspectionStatusEnum}
     */
    private String status;

    /**
     * 排序序号
     */
    private Integer sortOrder;

}
