package com.bdyl.line.web.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 巡检任务实体类，对应巡检任务表。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_inspection_task", autoResultMap = true)
public class InspectionTaskEntity extends BaseEntity {
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 组织ID
     */
    private Long organId;

    /**
     * 巡检计划ID
     */
    private Long planId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 巡检周期类型 {@link com.bdyl.line.common.constant.enums.InspectionCycleEnum}
     */
    private String cycleType;

    /**
     * 周期值
     */
    private Integer cycleValue;

    /**
     * 实际开始时间
     */
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    private LocalDateTime actualEndTime;

    /**
     * 状态 {@link com.bdyl.line.common.constant.enums.InspectionTaskStatusEnum}
     */
    private String status;

    /**
     * 巡检负责人ID
     */
    private Long responsibleUserId;

    /**
     * 巡检负责人姓名
     */
    private String responsibleUserName;

    /**
     * 摄像头总数
     */
    private Integer cameraCount;

    /**
     * 已完成巡检数量
     */
    private Integer completedCount;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 计划开始时间
     */
    private LocalDateTime scheduledStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime scheduledEndTime;
}
