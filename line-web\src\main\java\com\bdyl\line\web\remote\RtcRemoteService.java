package com.bdyl.line.web.remote;

import java.util.UUID;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.bdyl.line.web.config.LineProperties;
import com.bdyl.line.web.service.impl.ImageService;

/**
 * RTC远程调用服务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RtcRemoteService {

    /**
     * 远程调用
     */
    private final RestTemplate restTemplate;

    /**
     * 系统配置
     */
    private final LineProperties lineProperties;

    /**
     * 图片服务
     */
    private final ImageService imageService;

    /**
     * 获取rtc中的所有视频流
     *
     * @return 所有视频流信息
     */
    public Object listAllStreams() {
        String url = lineProperties.getRtcUrl() + "/streams";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            log.info("Getting all streams from RTC server");

            ResponseEntity<Object> response = restTemplate.exchange(url, HttpMethod.GET, entity, Object.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Successfully got all streams from RTC server");
                return response.getBody();
            } else {
                log.warn("Failed to get streams, status: {}", response.getStatusCode());
                return null;
            }
        } catch (Exception e) {
            log.error("Error getting streams from RTC server", e);
            return null;
        }
    }

    /**
     * 删除视频流
     *
     * @param streamName 流名称
     * @return 是否删除成功
     */
    public boolean deleteStream(String streamName) {
        // 构建URL和查询参数
        UriComponentsBuilder builder =
            UriComponentsBuilder.fromHttpUrl(lineProperties.getRtcUrl() + "/streams").queryParam("src", streamName);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            log.info("Deleting stream: {}", streamName);

            ResponseEntity<Void> response =
                restTemplate.exchange(builder.toUriString(), HttpMethod.DELETE, entity, Void.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Successfully deleted stream: {}", streamName);
                return true;
            } else {
                log.warn("Failed to delete stream: {}, status: {}", streamName, response.getStatusCode());
                return false;
            }
        } catch (Exception e) {
            log.error("Error deleting stream: {}", streamName, e);
            return false;
        }
    }

    /**
     * 添加视频流
     *
     * @param name 流名称
     * @param src 流源地址
     * @return 是否添加成功
     */
    public boolean addStream(String name, String src) {
        // 构建URL和查询参数
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(lineProperties.getRtcUrl() + "/streams")
            .queryParam("name", name).queryParam("src", src);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            log.info("Adding stream: name={}, src={}", name, src);

            ResponseEntity<Void> response =
                restTemplate.exchange(builder.toUriString(), HttpMethod.PUT, entity, Void.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Successfully added stream: {}", name);
                return true;
            } else {
                log.warn("Failed to add stream: {}, status: {}", name, response.getStatusCode());
                return false;
            }
        } catch (Exception e) {
            log.error("Error adding stream: {}", name, e);
            return false;
        }
    }

    /**
     * 保存图片到本地并返回相对路径
     *
     * @param code 摄像头编码
     * @return 图片相对路径
     */
    public String saveImage(String code) {
        // 构建URL和查询参数
        UriComponentsBuilder builder =
            UriComponentsBuilder.fromHttpUrl(lineProperties.getRtcUrl() + "/frame.jpeg").queryParam("src", code);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            log.info("Getting image for camera: {}", code);

            ResponseEntity<byte[]> response =
                restTemplate.exchange(builder.toUriString(), HttpMethod.GET, entity, byte[].class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                byte[] imageBytes = response.getBody();

                // 使用 ImageService 保存图片到本地
                String relativePath = imageService.saveImage(imageBytes);
                if (relativePath != null) {
                    log.info("Successfully saved image for camera: {} to {}", code, relativePath);
                    return relativePath;
                } else {
                    log.warn("Failed to save image to local for camera: {}", code);
                    return null;
                }
            } else {
                log.warn("Failed to get image for camera: {}, status: {}", code, response.getStatusCode());
                return null;
            }
        } catch (Exception e) {
            log.error("Error saving image for camera: {}", code, e);
            return null;
        }
    }

    /**
     * 保存视频到本地并返回相对路径
     *
     * @param code 摄像头编码
     * @return 视频相对路径
     */
    public String saveVideo(String code) {
        // 生成视频文件名
        String videoName = "video_" + UUID.randomUUID().toString().replace("-", "");

        // 构建URL和查询参数
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(lineProperties.getRtcUrl() + "/stream.mp4")
            .queryParam("src", code).queryParam("duration", 20).queryParam("filename", videoName + ".mp4");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            log.info("Getting video for camera: {}, filename: {}", code, videoName);

            ResponseEntity<byte[]> response =
                restTemplate.exchange(builder.toUriString(), HttpMethod.GET, entity, byte[].class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                byte[] videoBytes = response.getBody();

                // 使用 ImageService 保存视频到本地
                String relativePath = imageService.saveVideo(videoBytes);
                if (relativePath != null) {
                    log.info("Successfully saved video for camera: {} to {}", code, relativePath);
                    return relativePath;
                } else {
                    log.warn("Failed to save video to local for camera: {}", code);
                    return null;
                }
            } else {
                log.warn("Failed to get video for camera: {}, status: {}", code, response.getStatusCode());
                return null;
            }
        } catch (Exception e) {
            log.error("Error saving video for camera: {}", code, e);
            return null;
        }
    }
}
