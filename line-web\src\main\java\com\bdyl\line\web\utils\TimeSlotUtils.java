package com.bdyl.line.web.utils;

import java.util.List;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import com.bdyl.line.web.model.dto.TimeSlotDTO;

/**
 * 时间段工具类
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public class TimeSlotUtils {

    /**
     * ObjectMapper实例
     */
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.registerModule(new JavaTimeModule());
    }

    /**
     * 将时间段列表转换为JSON字符串
     *
     * @param timeSlots 时间段列表
     * @return JSON字符串
     */
    public static String toJson(List<TimeSlotDTO> timeSlots) {
        if (timeSlots == null || timeSlots.isEmpty()) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writeValueAsString(timeSlots);
        } catch (JsonProcessingException e) {
            log.error("时间段列表转换为JSON失败", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为时间段列表
     *
     * @param json JSON字符串
     * @return 时间段列表
     */
    public static List<TimeSlotDTO> fromJson(String json) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, new TypeReference<List<TimeSlotDTO>>() {});
        } catch (JsonProcessingException e) {
            log.error("JSON转换为时间段列表失败: {}", json, e);
            return null;
        }
    }

    /**
     * 验证时间段列表是否有效
     *
     * @param timeSlots 时间段列表
     * @return true-有效，false-无效
     */
    public static boolean isValid(List<TimeSlotDTO> timeSlots) {
        if (timeSlots == null || timeSlots.isEmpty()) {
            return false;
        }
        return timeSlots.stream().allMatch(TimeSlotDTO::isValid);
    }

}
