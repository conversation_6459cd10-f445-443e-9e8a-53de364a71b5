import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    name: 'AlarmManagement',
    path: '/alarm-management',
    meta: {
      icon: 'mdi:alarm', // 可根据实际需求替换图标（如使用其他mdi图标）
      order: 10, // 建议根据现有路由顺序调整（basemanagement为20，videomonitoring可设为25，此处设为30）
      title: $t('page.alarmmanagement.title'), // 需在国际化文件中添加对应翻译
      authority: ['alert:menu'],
    },
    children: [
      {
        name: 'BusinessAlarm',
        path: 'business-alarm',
        component: () =>
          import('#/views/alarm-management/business-alarm/index.vue'), // 需确保对应视图文件存在
        meta: {
          icon: 'mdi:alarm-light',
          order: 35,
          title: $t('page.alarmmanagement.businessTitle'), // 需在国际化文件中添加对应翻译
          authority: ['bizalert:menu'],
        },
      },
      {
        name: 'IotAlarm',
        path: 'iot-alarm',
        component: () => import('#/views/alarm-management/iot-alarm/index.vue'), // 需确保对应视图文件存在
        meta: {
          icon: 'mdi:alarm-multiple',
          order: 40,
          title: $t('page.alarmmanagement.iotTitle'), // 需在国际化文件中添加对应翻译
          authority: ['iotalert:menu'],
        },
      },
    ],
  },
];

export default routes;
