package com.bdyl.line.web.remote;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import jakarta.validation.constraints.NotBlank;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.bdyl.boot.JsonResult;
import com.bdyl.boot.exception.BizException;
import com.bdyl.line.common.constant.error.CommonError;
import com.bdyl.line.web.config.LineProperties;
import com.bdyl.line.web.model.request.camera.CameraSynRequest;
import com.bdyl.line.web.model.request.camera.PlatformVideoServerResp;
import com.bdyl.line.web.model.request.terminal.TerminalSynRequest;
import com.bdyl.line.web.model.response.bizalert.PlatformBizAlertSynDTO;
import com.bdyl.line.web.model.response.terminal.PlatformTelemetryHistory;
import com.bdyl.line.web.remote.model.IotDeviceType;

/**
 * 平台远程服务
 */
@Component
public class PlatformRemoteService {
    /**
     * 远程调用工具
     */
    private final RestTemplate restTemplate;

    /**
     * 远程调用工具
     */
    private final ObjectMapper objectMapper;
    /**
     * 系统配置
     */
    private final LineProperties lineProperties;

    /**
     * @param restTemplate 远程调用工具
     * @param objectMapper json解析器
     * @param lineProperties 系统配置
     */
    public PlatformRemoteService(RestTemplate restTemplate, ObjectMapper objectMapper, LineProperties lineProperties) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
        this.lineProperties = lineProperties;
    }

    /**
     * 获取终端列表
     *
     * @return 终端列表
     */
    public List<TerminalSynRequest> listTerminals() {
        String url = lineProperties.getPlatformUrl() + "/edge-boxes/list";
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        ResponseEntity<JsonResult<List<TerminalSynRequest>>> response = restTemplate.exchange(url, HttpMethod.GET,
            entity, new ParameterizedTypeReference<JsonResult<List<TerminalSynRequest>>>() {});
        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null
            && response.getBody().getData() != null) {
            JsonResult<List<TerminalSynRequest>> jsonResult = response.getBody();
            if (!"SUCCESS".equals(jsonResult.getCode())) {
                throw new BizException(CommonError.TRY_LATER);
            }
            return jsonResult.getData();
        }
        throw new BizException(CommonError.TRY_LATER);
    }

    /**
     * 根据终端编码获取摄像头列表
     *
     * @param code 终端编码
     * @param type 设备类型
     * @return 摄像头列表
     */
    public List<CameraSynRequest> listCamerasByTerminalCode(@NotBlank(message = "终端编号不能为空") String code,
        IotDeviceType type) {
        String url = lineProperties.getPlatformUrl() + "/devices/list?edgeCode=" + code + "&deviceType=" + type;
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        ResponseEntity<JsonResult<List<CameraSynRequest>>> response = restTemplate.exchange(url, HttpMethod.GET, entity,
            new ParameterizedTypeReference<JsonResult<List<CameraSynRequest>>>() {});
        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null
            && response.getBody().getData() != null) {

            JsonResult<List<CameraSynRequest>> jsonResult = response.getBody();
            if (!"SUCCESS".equals(jsonResult.getCode())) {
                throw new BizException(CommonError.TRY_LATER);
            }

            List<CameraSynRequest> data = jsonResult.getData();
            // 将CameraSynRequest中的字段转成map,获取经纬度和详细地址
            for (CameraSynRequest cameraSynRequest : data) {
                String coordinates = cameraSynRequest.getCoordinates();
                if (StringUtils.isNotBlank(coordinates)) {
                    try {
                        Map<String, Object> map = objectMapper.readValue(coordinates, Map.class);
                        Double lng = (Double) map.get("lng");
                        Double lat = (Double) map.get("lat");
                        String location = (String) map.get("location");
                        cameraSynRequest.setLongitude(lng);
                        cameraSynRequest.setLatitude(lat);
                        cameraSynRequest.setLocation(location);

                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
            return data;
        }
        throw new BizException(CommonError.TRY_LATER);

    }

    /**
     * 查询摄像头的国标信息
     *
     * @param code 摄像头编码
     * @return 摄像头国标信息
     */
    public PlatformVideoServerResp getCameraGbInfoByCode(@NotBlank(message = "摄像头编码不能为空") String code) {

        String url = lineProperties.getPlatformUrl() + "/video-servers/code/" + code;
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        ResponseEntity<JsonResult<PlatformVideoServerResp>> response = restTemplate.exchange(url, HttpMethod.GET,
            entity, new ParameterizedTypeReference<JsonResult<PlatformVideoServerResp>>() {});
        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null
            && response.getBody().getData() != null) {
            JsonResult<PlatformVideoServerResp> jsonResult = response.getBody();
            if (!"SUCCESS".equals(jsonResult.getCode())) {
                return null;

            }
            return jsonResult.getData();
        }
        return null;
    }

    /**
     * 根据设备code查询设备实时数据
     *
     * @param code 设备code
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 设备实时数据
     */
    public List<PlatformTelemetryHistory> listTelemetryByCode(@NotBlank(message = "设备编码不能为空") String code,
        LocalDateTime startTime, LocalDateTime endTime) {

        // 时间格式化为字符串
        // String startTimeStr = startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // String endTimeStr = endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        String startTimeStr = "2025-07-16 09:44:00";
        String endTimeStr = "2025-07-16 09:44:59";
        String url = lineProperties.getPlatformUrl() + "/telemetry-history/device?deviceCode=" + code + "&startTime="
            + startTimeStr + "&endTime=" + endTimeStr;
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        ResponseEntity<JsonResult<List<PlatformTelemetryHistory>>> response = restTemplate.exchange(url, HttpMethod.GET,
            entity, new ParameterizedTypeReference<JsonResult<List<PlatformTelemetryHistory>>>() {});
        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null
            && response.getBody().getData() != null) {
            JsonResult<List<PlatformTelemetryHistory>> jsonResult = response.getBody();
            if (!"SUCCESS".equals(jsonResult.getCode())) {
                return Collections.emptyList();
            }
            return jsonResult.getData();
        }
        return Collections.emptyList();
    }

    /**
     * 根据摄像头查询报警列表
     *
     * @param code 摄像头编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报警列表
     */
    public List<PlatformBizAlertSynDTO> listAlarmByCameraCode(@NotBlank(message = "摄像头编码不能为空") String code,
        LocalDateTime startTime, LocalDateTime endTime) {

        // 时间格式化为字符串
        // String startTimeStr = startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // String endTimeStr = endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String startTimeStr = "2025-07-16 18:00:00";
        String endTimeStr = "2025-07-16 19:00:00";
        String url = lineProperties.getPlatformUrl() + "/alerts/device/" + code + "&startTime=" + startTimeStr
            + "&endTime=" + endTimeStr;
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        ResponseEntity<JsonResult<List<PlatformBizAlertSynDTO>>> response = restTemplate.exchange(url, HttpMethod.GET,
            entity, new ParameterizedTypeReference<JsonResult<List<PlatformBizAlertSynDTO>>>() {});
        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null
            && response.getBody().getData() != null) {
            JsonResult<List<PlatformBizAlertSynDTO>> jsonResult = response.getBody();
            if (!"SUCCESS".equals(jsonResult.getCode())) {
                return Collections.emptyList();
            }
            return jsonResult.getData();
        }
        return Collections.emptyList();
    }

}
