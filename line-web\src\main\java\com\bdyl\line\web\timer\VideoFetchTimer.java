package com.bdyl.line.web.timer;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Iterator;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.bdyl.boot.tenant.Tenant;
import com.bdyl.boot.tenant.TenantContext;
import com.bdyl.boot.tenant.TenantContextHolder;
import com.bdyl.line.web.model.request.camera.GBPlayTSRequest;
import com.bdyl.line.web.remote.GBRemoteService;
import com.bdyl.line.web.service.BizAlertService;
import com.bdyl.line.web.utils.LineUserContext;

/**
 * 视频获取定时任务，内存队列管理待获取视频的报警，定时批量处理。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VideoFetchTimer {
    /**
     * 待获取视频的报警任务
     */
    private static final Queue<PendingVideoTask> PENDING_TASKS = new ConcurrentLinkedQueue<>();

    /**
     * 国标服务
     */
    private final GBRemoteService gbRemoteService;
    /**
     * 业务报警service
     */
    private final BizAlertService bizAlertService;

    /**
     * 添加待处理任务
     *
     * @param alertId 报警ID
     * @param cameraCode 摄像头编码
     * @param delayMillis 延迟毫秒数
     */
    public static void addPendingTask(Long alertId, String cameraCode, long delayMillis) {
        long readyTime = System.currentTimeMillis() + delayMillis;
        PENDING_TASKS.add(new PendingVideoTask(alertId, System.currentTimeMillis(), cameraCode, readyTime));
    }

    /**
     * 定时处理已到期的报警，获取视频并更新报警记录
     */
    @Scheduled(fixedDelay = 10000)
    public void fetchReadyVideos() {
        long now = System.currentTimeMillis();
        Iterator<PendingVideoTask> it = PENDING_TASKS.iterator();
        while (it.hasNext()) {
            PendingVideoTask task = it.next();
            if (task.readyTimeMillis() <= now) {
                int secondDuration = 60;
                long alertTime = task.alertTime;
                // 转换为LocalDateTime
                LocalDateTime alertDateTime =
                    LocalDateTime.ofInstant(Instant.ofEpochMilli(alertTime), ZoneId.systemDefault());
                GBPlayTSRequest gbPlayTSRequest = new GBPlayTSRequest();
                gbPlayTSRequest.setDeviceCode(task.cameraCode());

                gbPlayTSRequest.setDateTime(alertDateTime);
                gbPlayTSRequest.setDurationSecond(secondDuration);
                gbPlayTSRequest.setTsSegmentSecond(30);
                List<String> playbackTs = gbRemoteService.getPlaybackTs(gbPlayTSRequest);
                // 将ts文件转换为mp4，保存到本地
                String videoUrl = null;
                if (!playbackTs.isEmpty()) {
                    videoUrl = tsToMp4(playbackTs);
                }
                try {

                    if (videoUrl != null) {
                        // 跳过权限检查
                        LineUserContext.setSkipPermission(true);
                        TenantContextHolder.setTenantContext(new TenantContext(new Tenant("1", "1"), false));
                        bizAlertService.updateAlertVideo(task.alertId(), videoUrl);
                        log.info("报警[{}] 视频获取成功: {}", task.alertId(), videoUrl);
                    } else {
                        log.warn("报警[{}] 视频获取失败", task.alertId());
                    }
                } catch (Exception e) {
                    log.error("报警[{}] 视频获取异常", task.alertId(), e);
                } finally {
                    LineUserContext.clear();
                    TenantContextHolder.resetTenantContext();
                }
                it.remove();
            }
        }
    }

    /**
     * ts视频列表转换为mp4
     *
     * @param playbackTs ts列表
     * @return mp4文件路径
     */
    public String tsToMp4(List<String> playbackTs) {

        return "/srv/upload/20250718/alert.mp4";
    }

    /**
     * 待处理报警任务
     */
    public record PendingVideoTask(Long alertId, long alertTime, String cameraCode, long readyTimeMillis) {
    }
}
