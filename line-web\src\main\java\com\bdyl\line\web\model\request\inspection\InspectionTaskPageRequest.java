package com.bdyl.line.web.model.request.inspection;

import java.time.LocalDate;

import lombok.Data;
import lombok.EqualsAndHashCode;

import com.bdyl.boot.data.query.PageRequest;

/**
 * 巡检任务分页查询请求对象
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InspectionTaskPageRequest extends PageRequest {
    /**
     * 巡检周期类型 {@link com.bdyl.line.common.constant.enums.InspectionCycleEnum}
     */
    private String cycleType;
    /**
     * 巡检状态 {@link com.bdyl.line.common.constant.enums.InspectionStatusEnum}
     */
    private String status;

    /**
     * 巡检负责人ID
     */
    private Long responsibleUserId;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 计划ID
     */
    private Long planId;
}
