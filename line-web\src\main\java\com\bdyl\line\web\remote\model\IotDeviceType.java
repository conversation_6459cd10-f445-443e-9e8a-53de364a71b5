package com.bdyl.line.web.remote.model;

import lombok.Getter;

/**
 * 设备类型枚举
 */
@Getter
public enum IotDeviceType {
    /**
     * 摄像头
     */
    CAMERA("CAMERA", "摄像头", "摄像头设备"),

    /**
     * 传感器
     */
    SENSOR("SENSOR", "传感器", "传感器设备"),

    /**
     * 电池板
     */
    BATTERY("BATTERY", "电池板", "电池板设备"),

    /**
     * 其他
     */
    OTHER("OTHER", "其他", "其他类型设备");

    /**
     * 值
     */
    private final String value;

    /**
     * 名称
     */
    private final String name;

    /**
     * 描述
     */
    private final String description;

    IotDeviceType(String value, String name, String description) {
        this.value = value;
        this.name = name;
        this.description = description;
    }
}
