package com.bdyl.line.web.ws;

import java.net.URI;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import jakarta.websocket.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.ApplicationEventPublisher;

import com.bdyl.line.web.event.CameraMessageEvent;

/**
 * 摄像头WebSocket客户端，用于接收指定摄像头的WebSocket消息。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@ClientEndpoint
public class CameraWebSocketClient {
    /**
     * 摄像头设备编码
     */
    private final String deviceCode;
    /**
     * 摄像头session
     */
    private Session session;
    /**
     * websocket的地址
     */
    private final String wsUrl;

    /**
     * 最大重连次数，-1为无限重试
     */
    private static final int MAX_RETRY = -1;
    /**
     * 重连间隔（秒）
     */
    private static final int RETRY_INTERVAL_SECONDS = 5;
    /**
     * 当前重试次数
     */
    private int retryCount = 0;
    /**
     * 重连调度器
     */
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    /**
     * JSON序列化/反序列化工具
     */
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 事件发布器，用于发布WebSocket消息事件
     */
    private static ApplicationEventPublisher publisher;

    public static void setPublisher(ApplicationEventPublisher publisher) {
        CameraWebSocketClient.publisher = publisher;
    }

    /**
     * 标记是否为手动关闭
     */
    private volatile boolean manualClose = false;

    /**
     * 构造函数，初始化摄像头设备编码和WebSocket地址
     *
     * @param deviceCode 摄像头设备编码
     * @param plateWsUrl ws地址
     */
    public CameraWebSocketClient(String deviceCode, String plateWsUrl) {
        this.deviceCode = deviceCode;
        // 判断plateWsUrl是否以斜杠结尾，如果不是，则添加斜杠
        if (!plateWsUrl.endsWith("/")) {
            plateWsUrl += "/";
        }
        this.wsUrl = plateWsUrl + deviceCode;
    }

    /**
     * 建立WebSocket连接
     */
    public void connect() {
        manualClose = false; // 每次连接前重置为非手动关闭
        try {
            WebSocketContainer container = ContainerProvider.getWebSocketContainer();
            container.connectToServer(this, URI.create(wsUrl));
            log.info("[{}] WebSocket连接已建立: {}", deviceCode, wsUrl);
        } catch (Exception e) {
            log.error("[{}] WebSocket连接失败: {}", deviceCode, wsUrl, e);
        }
    }

    /**
     * 关闭WebSocket连接
     */
    public void close() {
        manualClose = true;
        if (session != null && session.isOpen()) {
            try {
                session.close();
                log.info("[{}] WebSocket连接已关闭", deviceCode);
            } catch (Exception e) {
                log.error("[{}] WebSocket关闭异常", deviceCode, e);
            }
        }
        scheduler.shutdownNow(); // 主动关闭线程池，防止资源泄漏
    }

    /**
     * 发送消息到WebSocket服务器
     *
     * @param session WebSocket会话
     */
    @OnOpen
    public void onOpen(Session session) {
        this.session = session;
        this.retryCount = 0; // 连接成功后重置重试次数
        log.info("[{}] WebSocket连接已打开", deviceCode);
        session.addMessageHandler(String.class, message -> {
            Thread.startVirtualThread(() -> {
                log.info("[{}] 收到WebSocket消息: {}", deviceCode, message);
                WebsocketMessage wsMsg = null;
                try {
                    wsMsg = objectMapper.readValue(message, WebsocketMessage.class);
                } catch (Exception e) {
                    log.error("[{}] 消息解析失败", deviceCode, e);
                    return;
                }
                if (publisher != null && wsMsg != null) {
                    publisher.publishEvent(new CameraMessageEvent(this, deviceCode, wsMsg));
                }
            });
        });
    }

    /**
     * 处理WebSocket消息
     *
     * @param session WebSocket会话
     * @param thr 异常信息
     */
    @OnError
    public void onError(Session session, Throwable thr) {
        log.error("[{}] WebSocket发生错误，准备重连...", deviceCode, thr);
        if (!manualClose) {
            tryReconnect();
        }
    }

    /**
     * 处理WebSocket关闭事件
     *
     * @param session WebSocket会话
     * @param closeReason 关闭原因
     */
    @OnClose
    public void onClose(Session session, CloseReason closeReason) {
        log.info("[{}] WebSocket连接关闭: {}", deviceCode, closeReason);
        if (!manualClose) {
            tryReconnect();
        }
    }

    /**
     * 尝试重连
     */
    private void tryReconnect() {
        if (MAX_RETRY == -1 || retryCount < MAX_RETRY) {
            retryCount++;
            scheduler.schedule(this::connect, RETRY_INTERVAL_SECONDS, TimeUnit.SECONDS);
            log.info("[{}] {}秒后第{}次尝试重连...", deviceCode, RETRY_INTERVAL_SECONDS, retryCount);
        } else {
            log.warn("[{}] 已达到最大重连次数({})，不再重连。", deviceCode, MAX_RETRY);
        }
    }
}
