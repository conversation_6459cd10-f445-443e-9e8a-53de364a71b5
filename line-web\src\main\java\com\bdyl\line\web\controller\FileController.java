package com.bdyl.line.web.controller;

import java.util.HashMap;
import java.util.Map;

import lombok.AllArgsConstructor;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.bdyl.boot.JsonResult;
import com.bdyl.line.web.service.impl.ImageService;

/**
 * 文件上传下载
 */
@AllArgsConstructor
@RestController
@RequestMapping("/file")
public class FileController {

    /**
     * 图片服务
     */
    private final ImageService imageService;

    /**
     * 文件上传
     *
     * @param file 文件
     * @return 文件的地址
     */
    @PostMapping("/upload")
    public JsonResult<String> upload(@RequestPart MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return JsonResult.failed("400", "上传文件不能为空", null);
        }

        // 检查文件名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            return JsonResult.failed("400", "文件名不能为空", null);
        }

        String path = imageService.saveFile(file);
        if (path == null) {
            return JsonResult.failed("500", "文件保存失败，请检查文件类型是否支持", null);
        }

        return JsonResult.success(path);
    }

    /**
     * 获取支持的文件类型信息
     *
     * @return 支持的文件类型和限制信息
     */
    @GetMapping("/info")
    public JsonResult<Map<String, Object>> getFileInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("maxFileSize", "100MB");
        info.put("maxFileSizeBytes", 100 * 1024 * 1024);
        info.put("supportedImageTypes", ".jpg, .jpeg, .png, .gif, .bmp, .webp");
        info.put("supportedVideoTypes", ".mp4, .avi, .mov, .wmv, .flv, .mkv, .webm");
        info.put("supportedDocumentTypes", ".pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .txt");

        return JsonResult.success(info);
    }

}
