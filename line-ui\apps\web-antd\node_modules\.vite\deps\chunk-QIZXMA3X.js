import {
  getSlotVNs
} from "./chunk-JL3MBCJ6.js";
import {
  checkDynamic,
  dynamicApp,
  dynamicStore
} from "./chunk-TLDIGKI7.js";
import {
  VxeUI,
  createEvent,
  getConfig,
  getI18n,
  getIcon,
  require_xe_utils,
  useSize
} from "./chunk-TV7URO3H.js";
import {
  computed,
  createCommentVNode,
  defineComponent,
  h,
  reactive,
  watch
} from "./chunk-ZLVVKZUX.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/loading/src/loading.js
var import_xe_utils = __toESM(require_xe_utils());
var loading_default = defineComponent({
  name: "VxeLoading",
  props: {
    modelValue: Boolean,
    icon: {
      type: String,
      default: () => getConfig().loading.icon
    },
    showIcon: {
      type: <PERSON><PERSON><PERSON>,
      default: () => getConfig().loading.showIcon
    },
    text: {
      type: String,
      default: () => getConfig().loading.text
    },
    showText: {
      type: Boolean,
      default: () => getConfig().loading.showText
    },
    status: String,
    size: {
      type: String,
      default: () => getConfig().loading.size || getConfig().size
    }
  },
  setup(props, context) {
    const { slots, emit } = context;
    const xID = import_xe_utils.default.uniqueId();
    const { computeSize } = useSize(props);
    const reactData = reactive({
      initialized: false
    });
    const computeMaps = {
      computeSize
    };
    const $xeLoading = {
      xID,
      props,
      context,
      reactData,
      getComputeMaps: () => computeMaps
    };
    const computeLoadingIcon = computed(() => {
      return props.icon || getIcon().LOADING;
    });
    const computeLoadingText = computed(() => {
      const { text } = props;
      return import_xe_utils.default.isString(text) ? text : getI18n("vxe.loading.text");
    });
    const handleInit = () => {
      if (!reactData.initialized) {
        reactData.initialized = !!reactData.initialized;
      }
    };
    const dispatchEvent = (type, params, evnt) => {
      emit(type, createEvent(evnt, { $loading: $xeLoading }, params));
    };
    const loadingMethods = {
      dispatchEvent
    };
    const loadingPrivateMethods = {};
    Object.assign($xeLoading, loadingMethods, loadingPrivateMethods);
    const renderVN = () => {
      const { modelValue, showIcon, status } = props;
      const { initialized } = reactData;
      const vSize = computeSize.value;
      const defaultSlot = slots.default;
      const textSlot = slots.text;
      const iconSlot = slots.icon;
      const loadingIcon = computeLoadingIcon.value;
      const loadingText = computeLoadingText.value;
      if (!initialized && !modelValue) {
        return createCommentVNode();
      }
      return h("div", {
        class: ["vxe-loading", {
          [`size--${vSize}`]: vSize,
          [`theme--${status}`]: status,
          "is--visible": modelValue
        }]
      }, defaultSlot ? [
        h("div", {
          class: "vxe-loading--wrapper"
        }, getSlotVNs(defaultSlot({})))
      ] : [
        h("div", {
          class: "vxe-loading--chunk"
        }, [
          showIcon && (iconSlot || loadingIcon) ? h("div", {
            class: "vxe-loading--icon"
          }, iconSlot ? getSlotVNs(iconSlot({})) : [
            h("i", {
              class: loadingIcon
            })
          ]) : h("div", {
            class: "vxe-loading--spinner"
          }),
          textSlot || loadingText ? h("div", {
            class: "vxe-loading--text"
          }, textSlot ? getSlotVNs(textSlot({})) : `${loadingText}`) : null
        ])
      ]);
    };
    watch(() => props.modelValue, () => {
      handleInit();
    });
    handleInit();
    $xeLoading.renderVN = renderVN;
    return $xeLoading;
  },
  render() {
    return this.renderVN();
  }
});

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/loading/index.js
var VxeLoading = Object.assign({}, loading_default, {
  install(app) {
    app.component(loading_default.name, loading_default);
  }
});
var LoadingController = {
  open(options) {
    const opts = Object.assign({}, options);
    dynamicStore.globalLoading = {
      modelValue: true,
      text: opts.text,
      icon: opts.icon
    };
    checkDynamic();
  },
  close() {
    dynamicStore.globalLoading = null;
  }
};
dynamicApp.use(VxeLoading);
VxeUI.component(loading_default);
VxeUI.loading = LoadingController;
var Loading = VxeLoading;
var loading_default2 = VxeLoading;

export {
  VxeLoading,
  LoadingController,
  Loading,
  loading_default2 as loading_default
};
//# sourceMappingURL=chunk-QIZXMA3X.js.map
