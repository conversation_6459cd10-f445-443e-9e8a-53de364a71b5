<script lang="ts" setup>
import { ref, onMounted } from 'vue';

import { getVideoByTerminal_Api } from '#/api/core/terminal';

import EasyPlayer from '#/components/EasyPlayer/index.vue';

const open = ref<boolean>(false);
const modalTitle = ref<string>('');
const videoUrl = ref<string>('');
const batteryLevel = ref<number>(0);
const cameraRegions = ref<any[]>([]);

// 打开弹窗
const openModal = async (record: any) => {
  open.value = true;
  modalTitle.value = '实时视频： ' + record.name;

  batteryLevel.value = record.batteryLevel;

  const res = await getVideoByTerminal_Api(record.id);
  if (res) {
    const preferredUrl =
      res.streamUrls.find((url: string) => url.endsWith('.flv')) ||
      res.streamUrls.find((url: string) => url.endsWith('.ts')) ||
      res.streamUrls.find((url: string) => url.endsWith('.mp4')) ||
      res.streamUrls.find((url: string) => url.endsWith('.m3u8'));
    videoUrl.value = preferredUrl;

    cameraRegions.value = res.cameraRegions;
  }
};
// 关闭
const handleCancel = () => {
  videoUrl.value = '';
  open.value = false;
};
defineExpose({ openModal }); // 提供 open 方法，用于打开弹窗
onMounted(() => {});
</script>
<template>
  <div>
    <a-modal
      v-model:open="open"
      :title="modalTitle"
      width="1000px"
      @cancel="handleCancel"
      :destroyOnClose="true"
      :footer="null"
    >
      <EasyPlayer
        v-if="videoUrl"
        id="easy-player-terminal"
        :video-url="videoUrl"
        style="width: 100%; height: 535px"
        :show-battery="true"
        :batteryLevel="batteryLevel"
        :points="cameraRegions"
      />
    </a-modal>
  </div>
</template>
