<template>
  <a-modal
    v-model:open="dialogVisible"
    :title="titlePrefix + '导入'"
    width="600px"
    destroy-on-close
    @close="handleDialogClose"
  >
    <div class="batch-import">
      <!-- 下载与导入区域 -->
      <div class="import-section">
        <div class="import-action-item">
          <span class="action-label">说明：请先</span>
          <a-button @click="handleDownloadTemplate">下载模板</a-button>
          <span class="action-label ml-2">并按照模板填写信息，再上传系统</span>
        </div>
        <div class="import-action-item">
          <span class="action-label"></span>
          <a-upload
            ref="uploadRef"
            :action="baseUrl + uploadUrl"
            :headers="headers"
            :data="uploadData"
            :before-upload="beforeUpload"
            :show-upload-list="false"
            accept=".xls,.xlsx"
            @change="handleUploadChange"
          >
            <a-button type="primary" :loading="uploading">上传文件</a-button>
          </a-upload>
        </div>
      </div>

      <!-- 导入进度 -->
      <div v-if="uploading" class="upload-progress">
        <a-progress :percent="uploadPercentage" :stroke-width="8" />
      </div>

      <!-- 导入结果 -->
      <div v-if="importResult" class="import-result">
        <div class="section-title">导入结果</div>
        <div class="result-content">
          <div class="result-item">
            <span class="result-label">导入结果</span>
            <span class="result-value">{{ getResultStatus() }}</span>
          </div>
          <div class="result-item">
            <span class="result-label">异常数据</span>
            <span class="result-value">
              <a-button
                v-if="importResult.failCount && importResult.failCount > 0"
                type="link"
                size="small"
                @click="handleDownloadErrorData"
              >
                下载异常数据
              </a-button>
              <span v-else>无异常数据</span>
            </span>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <a-button @click="handleDialogClose">关闭</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import type { UploadChangeParam } from 'ant-design-vue';
import { useAccessStore } from '@vben/stores';
import { downloadFileFromBlob, downloadFileFromUrl } from '@vben/utils';
import { requestClient } from '#/api/request';

const baseUrl = import.meta.env.VITE_GLOB_API_URL;
const downloadErrorUrl = import.meta.env.VITE_GLOB_LOAD_ERROR_URL;

// 定义组件属性
const props = defineProps({
  // 是否显示对话框
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 上传API地址
  uploadUrl: {
    type: String,
    required: true,
  },
  // 模板下载地址
  templateUrl: {
    type: String,
    required: true,
  },
  // 自定义上传参数
  uploadData: {
    type: Object,
    default: () => ({}),
  },
  // 标题前缀，例如"部门"、"设备"等
  titlePrefix: {
    type: String,
    default: '数据',
  },
  // 异常数据表头映射
  errorHeaderMap: {
    type: Object,
    default: () => ({
      rowIndex: '行号',
      errorMsg: '错误信息',
    }),
  },
});

// 定义组件事件
const emit = defineEmits(['update:modelValue', 'success', 'error']);

// 对话框可见状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
});

// 上传组件引用
const uploadRef = ref();

// 上传状态
const uploading = ref(false);
const uploadPercentage = ref(0);

// 导入结果
const importResult = ref<{
  success: boolean;
  successCount?: number;
  failCount?: number;
  failFileUrl?: string;
  errorMessage?: string;
} | null>(null);

// HTTP请求头
const headers = computed(() => {
  const accessStore = useAccessStore();
  return {
    Authorization: 'Bearer ' + accessStore.accessToken,
    'X-Tenant-Realm': 'system',
  };
});

// 对话框关闭处理
const handleDialogClose = () => {
  resetUpload();
  dialogVisible.value = false;
};

// 获取结果状态文本
const getResultStatus = () => {
  if (!importResult.value) return '';

  if (importResult.value.success) {
    if (importResult.value.failCount) {
      return `本次导入${Number(importResult.value.successCount) + Number(importResult.value.failCount)}条数据，成功${importResult.value.successCount}条`;
    } else {
      return `全部导入成功，共${importResult.value.successCount}条数据`;
    }
  } else {
    return '导入失败';
  }
};

// 下载模板
const handleDownloadTemplate = () => {
  try {
    requestClient.download(props.templateUrl).then((res) => {
      downloadFileFromBlob({
        source: res,
        fileName: `${props.titlePrefix}导入模板.xlsx`,
      });
    });
  } catch (error) {
    console.error('下载模板失败:', error);
    message.error('下载模板失败，请稍后重试');
  }
};

// 上传前校验
const beforeUpload = (file: File) => {
  // 检查文件类型
  const isExcel =
    file.type === 'application/vnd.ms-excel' ||
    file.type ===
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

  if (!isExcel) {
    message.error('只能上传Excel文件（.xls或.xlsx格式）');
    return false;
  }

  // 设置上传状态
  uploading.value = true;
  uploadPercentage.value = 0;
  importResult.value = null;

  return true;
};

// ant-design上传状态变化处理
const handleUploadChange = (info: UploadChangeParam) => {
  const { file } = info;

  if (file.status === 'uploading') {
    uploading.value = true;
    uploadPercentage.value = Math.round(file.percent || 0);
  } else if (file.status === 'done') {
    uploading.value = false;
    uploadPercentage.value = 100;
    handleSuccess(file.response);
  } else if (file.status === 'error') {
    uploading.value = false;
    uploadPercentage.value = 0;
    handleError(file.error);
  }
};

// 上传成功
const handleSuccess = (response: any) => {
  uploading.value = false;
  uploadPercentage.value = 100;

  // 解析响应结果
  if (response.code === 'SUCCESS') {
    importResult.value = {
      success: true,
      successCount: response.data.successCount,
      failCount: response.data.failCount,
      failFileUrl: response.data.failFileUrl,
    };

    emit('success', importResult.value);

    if (response.data.failCount === 0) {
      message.success(`${props.titlePrefix}导入成功`);
    } else {
      message.warning(
        `${props.titlePrefix}导入成功${response.data.successCount}条，有${response.data.failCount}条数据导入失败`,
      );
    }
  } else {
    importResult.value = {
      success: false,
      errorMessage: response.msg || '导入失败',
    };

    emit('error', importResult.value);
    message.error(importResult.value.errorMessage);
  }
};

// 上传失败
const handleError = (error: any) => {
  uploading.value = false;
  uploadPercentage.value = 0;

  const errorMsg = error.message || '导入失败';

  importResult.value = {
    success: false,
    errorMessage: errorMsg,
  };

  emit('error', importResult.value);
  message.error(errorMsg);
};

// 下载异常数据
const handleDownloadErrorData = () => {
  downloadFileFromUrl({
    source: downloadErrorUrl + importResult.value?.failFileUrl,
    fileName: `${props.titlePrefix}导入异常数据.xlsx`,
  });
};

// 重置上传状态
const resetUpload = () => {
  uploading.value = false;
  uploadPercentage.value = 0;
  importResult.value = null;
};

// 暴露方法给父组件
defineExpose({
  resetUpload,
});
</script>

<style scoped lang="scss">
.batch-import {
  .section-title {
    font-weight: bold;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
  }

  .import-section {
    margin-bottom: 20px;
  }

  .import-action-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .action-label {
      width: 100px;
      text-align: right;
      margin-right: 8px;
      white-space: nowrap;

      &.required::before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
      }
    }
  }

  .upload-progress {
    margin: 16px 0;
  }

  .import-result {
    margin-top: 20px;

    .result-content {
      .result-item {
        display: flex;
        margin-bottom: 12px;

        .result-label {
          width: 120px;
          text-align: right;
          margin-right: 12px;
        }

        .result-value {
          flex: 1;
        }
      }

      .error-list {
        margin-top: 12px;
        padding: 12px;
        background-color: #fff5f5;
        border-radius: 4px;

        .error-list-title {
          font-weight: bold;
          margin-bottom: 8px;
          color: #f56c6c;
        }

        .error-items {
          margin: 0;
          padding-left: 20px;

          .error-item {
            margin-bottom: 4px;
            color: #f56c6c;
            font-size: 13px;
          }
        }
      }
    }
  }

  .dialog-footer {
    margin-top: 24px;
    text-align: right;
  }
}
</style>
