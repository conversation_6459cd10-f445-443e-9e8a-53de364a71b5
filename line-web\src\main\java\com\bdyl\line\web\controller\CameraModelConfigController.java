package com.bdyl.line.web.controller;

import java.util.List;

import jakarta.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.*;

import com.bdyl.boot.JsonResult;
import com.bdyl.line.web.model.request.CameraModelConfigRequest;
import com.bdyl.line.web.model.response.camera.CameraModelConfigResponse;
import com.bdyl.line.web.service.CameraModelConfigService;

/**
 * 摄像头-模型配置管理
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@RestController
@RequestMapping("/camera/model-config")
@RequiredArgsConstructor
public class CameraModelConfigController {

    /**
     * 摄像头模型配置中service
     */
    private final CameraModelConfigService cameraModelConfigService;

    /**
     * 查询摄像头所有模型配置
     *
     * @param cameraId 摄像头ID
     * @return 模型配置列表
     */
    @GetMapping("/{cameraId}")
    public JsonResult<List<CameraModelConfigResponse>> listByCameraId(@PathVariable Long cameraId) {
        return JsonResult.success(cameraModelConfigService.listByCameraId(cameraId));
    }

    /**
     * 更新模型区域
     *
     * @param request 区域更新请求
     * @return 更新结果
     */
    @PutMapping("/region")
    public JsonResult<Void> draw(@Valid @RequestBody CameraModelConfigRequest request) {
        cameraModelConfigService.draw(request);
        return JsonResult.success(null);
    }

    /**
     * 更新模型置信度
     *
     * @param request 置信度更新请求
     * @return 更新结果
     */
    @PutMapping("/confidence")
    public JsonResult<Void> updateConfidence(@Valid @RequestBody CameraModelConfigRequest request) {
        cameraModelConfigService.updateConfidence(request);
        return JsonResult.success(null);
    }

    /**
     * 获取模型配置详情
     *
     * @param cameraId 摄像头ID
     * @param modelCode 模型编码
     * @return 配置详情
     */
    @GetMapping("/detail")
    public JsonResult<CameraModelConfigResponse> getDetail(@RequestParam Long cameraId,
        @RequestParam String modelCode) {
        return JsonResult.success(cameraModelConfigService.getDetail(cameraId, modelCode));
    }
}
