<script lang="ts" setup>
import { nextTick, onUnmounted, reactive, ref, watch } from 'vue';

import { message } from 'ant-design-vue';
import * as fabricJS from 'fabric';

import {
  getCameraVideoUrl_Api,
  getCameraModelConfig_Api,
  updateCameraModelConfig_Api,
  updateCameraModelConfigConfidence_Api,
  type ModelConfigData,
  type RegionPoints,
} from '#/api/core/camera';
import EasyPlayer from '#/components/EasyPlayer/index.vue';

import { useDictStore } from '#/store';

const dictStore = useDictStore();

const emit = defineEmits(['success']);

const open = ref<boolean>(false);
const loading = ref<boolean>(false);
const cameraData = ref<any>({});

// 视频相关
const videoUrl = ref('');
const videoLoading = ref(false);
const playerRef = ref();

// 模型配置
const modelConfigs = ref<ModelConfigData[]>([]);
const currentModelCode = ref<any>('');

// 动态模型选项
const modelOptions = ref<Array<{ label: string; value: string; color: any }>>(
  [],
);

// 置信度配置 - 动态配置
const confidenceConfig = reactive<any>({});

// 置信度修改加载状态
const confidenceLoading = reactive<Record<string, boolean>>({});

// Canvas 相关
const canvas = ref<fabricJS.Canvas | null>(null);
const isDrawing = ref(false);
const currentPoints = ref<RegionPoints[]>([]);
const drawnRegions = ref<Record<string, RegionPoints[]>>({});

// 打开弹窗
const openModal = async (record: any) => {
  cameraData.value = record;
  open.value = true;

  // 加载视频
  await loadVideo();

  // 加载模型配置
  await loadModelConfigs();

  // 初始化Canvas
  nextTick(() => {
    setTimeout(() => {
      initCanvas();
    }, 500);
  });
};

// 关闭弹窗
const closeModal = () => {
  open.value = false;
  cameraData.value = {};
  videoUrl.value = '';
  modelConfigs.value = [];
  modelOptions.value = [];
  currentModelCode.value = '';
  currentPoints.value = [];
  drawnRegions.value = {};

  // 清理Canvas
  if (canvas.value) {
    canvas.value.dispose();
    canvas.value = null;
  }
};

// 加载视频
const loadVideo = async () => {
  if (!cameraData.value.code) return;

  try {
    videoLoading.value = true;
    const res = await getCameraVideoUrl_Api(cameraData.value.code);

    if (res && res.streamUrls && res.streamUrls.length > 0) {
      const preferredUrl =
        res.streamUrls.find((url: string) => url.endsWith('.flv')) ||
        res.streamUrls.find((url: string) => url.endsWith('.mp4')) ||
        res.streamUrls.find((url: string) => url.endsWith('.ts')) ||
        res.streamUrls.find((url: string) => url.endsWith('.m3u8'));
      videoUrl.value = preferredUrl || res.streamUrls[0];
    } else {
      message.warning('该摄像头暂无视频流或视频流已断开');
      videoUrl.value = '';
    }
  } catch (error) {
    console.error('获取视频流失败:', error);
    // message.error('获取视频流失败');
    videoUrl.value = '';
  } finally {
    videoLoading.value = false;
  }
};

const colors = [
  '#722ed1', // 紫色
  '#1890ff', // 蓝色
  '#52c41a', // 绿色
  '#ff4d4f', // 红色
  '#faad14', // 橙色
  '#13c2c2', // 青色
  '#eb2f96', // 粉色
  '#2f54eb', // 深蓝
  '#fa8c16', // 橙红
  '#a0d911', // 青柠
];

// 加载模型配置
const loadModelConfigs = async () => {
  if (!cameraData.value.id) return;

  try {
    const res = await getCameraModelConfig_Api(cameraData.value.id.toString());
    modelConfigs.value = res || [];

    // 动态构建模型选项
    const uniqueModelCodes = new Set<string>();
    modelOptions.value = [];

    modelConfigs.value.forEach((config, index) => {
      if (!uniqueModelCodes.has(config.modelCode)) {
        uniqueModelCodes.add(config.modelCode);
        modelOptions.value.push({
          label: dictStore.getDictLable('BizAlertTypeEnum', config.modelCode),
          value: config.modelCode,
          color: colors[index],
        });
      }

      // 初始化置信度配置和加载状态
      confidenceConfig[config.modelCode] = config.confidence;
      confidenceLoading[config.modelCode] = false;
      if (config.regionPoints && config.regionPoints.length > 0) {
        drawnRegions.value[config.modelCode] = config.regionPoints;
      }
    });

    // 设置默认选中的模型
    if (modelOptions.value.length > 0 && !currentModelCode.value) {
      currentModelCode.value = modelOptions.value[0]?.value;
    }
  } catch (error) {
    console.error('获取模型配置失败:', error);
    modelConfigs.value = [];
    modelOptions.value = [];
  }
};

// 初始化Canvas
const initCanvas = () => {
  const canvasElement = document.getElementById('drawCanvas');
  if (!canvasElement) {
    console.error('Canvas元素不存在');
    return;
  }

  try {
    canvas.value = new fabricJS.Canvas('drawCanvas', {
      width: 960,
      height: 540,
      selection: false,
    });

    // 绑定鼠标事件
    canvas.value.on('mouse:down', onMouseDown);
    canvas.value.on('mouse:dblclick', onCanvasDoubleClick);

    // 渲染已有的区域
    renderExistingRegions();
  } catch (error) {
    console.error('Canvas初始化失败:', error);
  }
};

// 渲染已有区域
const renderExistingRegions = () => {
  if (!canvas.value) return;

  Object.keys(drawnRegions.value).forEach((modelCode) => {
    const points = drawnRegions.value[modelCode];
    if (points && points.length > 2) {
      const modelOption = modelOptions.value.find(
        (opt: any) => opt.value === modelCode,
      );
      const color = modelOption?.color;

      const polygon = new fabricJS.Polygon(
        points.map((p) => ({ x: p.x * 960, y: p.y * 540 })),
        {
          fill: color + '20',
          stroke: color,
          strokeWidth: 2,
          selectable: false,
          evented: false,
          modelCode,
        },
      );

      canvas.value?.add(polygon);
    }
  });

  canvas.value.renderAll();
};

// 鼠标按下事件
const onMouseDown = (event: any) => {
  if (!isDrawing.value || !currentModelCode.value) return;

  const pointer = canvas.value!.getPointer(event.e);
  const point: RegionPoints = {
    x: pointer.x / 960, // 转换为百分比
    y: pointer.y / 540,
  };

  currentPoints.value.push(point);

  // 绘制点
  const circle = new fabricJS.Circle({
    left: pointer.x - 3,
    top: pointer.y - 3,
    radius: 3,
    fill: getModelColor(currentModelCode.value),
    selectable: false,
    evented: false,
  });

  canvas.value!.add(circle);
  canvas.value!.renderAll();
};

// 获取模型颜色
const getModelColor = (modelCode: string) => {
  const modelOption = modelOptions.value.find(
    (opt: any) => opt.value === modelCode,
  );
  return modelOption?.color;
};

// 更新单个模型的置信度
const updateModelConfidence = async (modelCode: string) => {
  try {
    confidenceLoading[modelCode] = true;

    await updateCameraModelConfigConfidence_Api({
      cameraId: cameraData.value.id,
      modelCode,
      confidence: confidenceConfig[modelCode] || 0.45,
      regionPoints: drawnRegions.value[modelCode] || [],
    });

    message.success(
      `${dictStore.getDictLable('BizAlertTypeEnum', modelCode)}置信度修改成功`,
    );
  } catch (error) {
    console.error('更新置信度失败:', error);
    // message.error('置信度修改失败');
  } finally {
    confidenceLoading[modelCode] = false;
  }
};

// 开始绘制
const startDrawing = () => {
  if (!currentModelCode.value) {
    message.warning('请先选择模型');
    return;
  }

  isDrawing.value = true;
  currentPoints.value = [];
};

// 完成绘制
const finishDrawing = () => {
  if (currentPoints.value.length < 3) {
    message.warning('至少需要3个点才能形成区域');
    return;
  }

  // 保存当前模型的区域
  drawnRegions.value[currentModelCode.value] = [...currentPoints.value];

  // 绘制多边形
  const modelOption = modelOptions.value.find(
    (opt: any) => opt.value === currentModelCode.value,
  );
  const color = modelOption?.color;

  const polygon = new fabricJS.Polygon(
    currentPoints.value.map((p) => ({ x: p.x * 960, y: p.y * 540 })),
    {
      fill: color + '20',
      stroke: color,
      strokeWidth: 2,
      selectable: false,
      evented: false,
      modelCode: currentModelCode.value,
    },
  );

  canvas.value!.add(polygon);
  canvas.value!.renderAll();

  isDrawing.value = false;
  currentPoints.value = [];
  message.success('区域绘制完成');
};

// 清空当前模型的区域
const clearCurrentModel = () => {
  if (!currentModelCode.value) {
    message.warning('请先选择模型');
    return;
  }

  // 从Canvas中移除对应的图形
  const objects = canvas.value!.getObjects();
  objects.forEach((obj: any) => {
    if (obj.modelCode === currentModelCode.value) {
      canvas.value!.remove(obj);
    }
  });

  // 清除点
  objects.forEach((obj) => {
    if (obj.type === 'circle') {
      canvas.value!.remove(obj);
    }
  });

  // 清除数据
  delete drawnRegions.value[currentModelCode.value];
  currentPoints.value = [];

  canvas.value!.renderAll();
  message.success('已清空当前模型的绘制区域');
};

// 保存配置
const saveConfig = async () => {
  try {
    loading.value = true;

    // 只保存区域配置
    for (const modelCode of Object.keys(drawnRegions.value)) {
      await updateCameraModelConfig_Api({
        cameraId: cameraData.value.id,
        modelCode,
        confidence: confidenceConfig[modelCode] || 0.45,
        regionPoints: drawnRegions.value[modelCode] || [],
      });
    }

    message.success('区域配置保存成功');
    closeModal();
    emit('success');
  } catch (error) {
    console.error('保存配置失败:', error);
    // message.error('保存配置失败');
  } finally {
    loading.value = false;
  }
};

// 双击完成绘制
const onCanvasDoubleClick = () => {
  if (isDrawing.value) {
    finishDrawing();
  }
};

// 监听模型切换
watch(currentModelCode, () => {
  if (canvas.value) {
    renderExistingRegions();
  }
});

// 组件卸载时清理
onUnmounted(() => {
  if (canvas.value) {
    canvas.value.dispose();
  }
});

// 暴露方法
defineExpose({
  openModal,
});
</script>

<template>
  <a-modal
    v-model:open="open"
    :title="cameraData.name || ''"
    width="1400px"
    :mask-closable="false"
    @cancel="closeModal"
  >
    <div class="video-config-content">
      <!-- 左侧视频播放区域 -->
      <div class="video-section">
        <div class="video-container">
          <div class="video-player">
            <EasyPlayer
              v-if="videoUrl"
              id="video-config"
              ref="playerRef"
              :is-live="true"
              :video-url="videoUrl"
              :showLocation="false"
            />
            <a-spin v-else :spinning="videoLoading">
              <a-empty
                class="no-video"
                description="摄像头已掉线或者网络连接失败"
              />
            </a-spin>
          </div>

          <!-- 绘制Canvas覆盖层 -->
          <div class="canvas-overlay">
            <canvas id="drawCanvas" width="960" height="540"></canvas>
          </div>
        </div>
      </div>

      <!-- 右侧配置区域 -->
      <div class="config-section">
        <!-- 置信度配置 -->
        <div class="confidence-config">
          <h4>置信度设置</h4>
          <div class="confidence-list">
            <div
              v-for="model in modelOptions"
              :key="model.value"
              class="confidence-item"
            >
              <div class="model-info">
                <div
                  class="model-color"
                  :style="{ backgroundColor: model.color }"
                ></div>
                <span class="model-name">{{ model.label }}</span>
              </div>
              <a-slider
                v-model:value="confidenceConfig[model.value]"
                :min="0"
                :max="1"
                :step="0.01"
                :tooltip-formatter="(value: any) => value.toFixed(2)"
                class="confidence-slider"
              />
              <span class="confidence-value">
                {{ confidenceConfig[model.value].toFixed(2) }}
              </span>
              <a-button
                type="primary"
                size="small"
                @click="updateModelConfidence(model.value)"
                :loading="confidenceLoading[model.value]"
              >
                修改
              </a-button>
            </div>
          </div>
        </div>

        <!-- 监测区域绘制 -->
        <div class="region-config">
          <h4>监测区域绘制</h4>

          <div class="model-selector">
            <a-select
              v-model:value="currentModelCode"
              placeholder="选择模型"
              style="width: 200px"
            >
              <a-select-option
                v-for="model in modelOptions"
                :key="model.value"
                :value="model.value"
              >
                <div class="model-option">
                  <div
                    class="model-color"
                    :style="{ backgroundColor: model.color }"
                  ></div>
                  {{ model.label }}
                </div>
              </a-select-option>
            </a-select>
          </div>

          <div class="drawing-controls">
            <a-space v-if="!isDrawing && !drawnRegions[currentModelCode]">
              <span>点击</span>
              <a-button type="primary" @click="startDrawing">
                开始绘制
              </a-button>
              <span>鼠标单击视频区域，双击结束绘制</span>
              <!-- <a-button
                danger
                :disabled="!currentModelCode || !drawnRegions[currentModelCode]"
                @click="clearCurrentModel"
              >
                清空
              </a-button> -->
            </a-space>
            <a-space v-else-if="drawnRegions[currentModelCode]">
              <div>
                <a-space>
                  监测范围
                  <div
                    :style="{
                      backgroundColor: getModelColor(currentModelCode),
                      width: '40px',
                      height: '5px',
                    }"
                  ></div>
                </a-space>
              </div>
              <div class="ml-5">
                绘制有误，点击&nbsp;&nbsp;
                <a-button danger @click="clearCurrentModel"> 清空 </a-button>
              </div>
            </a-space>
            <a-space v-else>
              <span>区域绘制中...</span>
            </a-space>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button type="primary" :loading="loading" @click="saveConfig">
        保存配置
      </a-button>
    </template>
  </a-modal>
</template>

<style lang="scss" scoped>
.video-config-content {
  display: flex;
  gap: 20px;
  height: 550px;
}

.video-section {
  display: flex;
  flex-direction: column;

  .video-title {
    margin-bottom: 12px;

    h4 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
    }

    .video-code {
      color: #666;
      font-size: 12px;
    }
  }

  .video-container {
    width: 960px;
    height: 540px;
    position: relative;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;

    .video-player {
      width: 100%;
      height: 100%;

      .no-video {
        height: 540px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fafafa;
      }
    }

    .canvas-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: auto;

      canvas {
        width: 100%;
        height: 100%;
        cursor: crosshair;
      }
    }
  }
}

.config-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;

  h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
  }

  h5 {
    margin: 0 0 8px 0;
    font-size: 13px;
    font-weight: 500;
    color: #666;
  }
}

.confidence-config {
  .confidence-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .confidence-item {
    display: flex;
    align-items: center;
    gap: 12px;

    .model-info {
      display: flex;
      align-items: center;
      gap: 6px;
      min-width: 120px;

      .model-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
      }

      .model-name {
        font-size: 12px;
        color: #333;
      }
    }

    .confidence-slider {
      flex: 1;
      margin: 0 8px;
    }

    .confidence-value {
      min-width: 40px;
      font-size: 12px;
      color: #666;
      text-align: right;
      margin-right: 8px;
    }
  }
}

.region-config {
  .model-selector {
    margin-bottom: 12px;

    .model-option {
      display: flex;
      align-items: center;
      gap: 6px;

      .model-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
      }
    }
  }

  .drawing-controls {
    margin-bottom: 12px;
  }

  .drawing-tips {
    margin-bottom: 16px;

    .tips-alert {
      :deep(.ant-alert-message) {
        font-size: 12px;
      }

      :deep(.ant-alert-description) {
        font-size: 11px;
        line-height: 1.4;
      }
    }
  }

  .drawn-regions {
    .region-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .region-item {
      padding: 8px 12px;
      background: #f5f5f5;
      border-radius: 4px;

      .region-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .region-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
        }

        .region-name {
          flex: 1;
          font-size: 12px;
          color: #333;
        }

        .region-points {
          font-size: 11px;
          color: #999;
        }
      }
    }
  }
}

:deep(.ant-select-dropdown) {
  .model-option {
    display: flex;
    align-items: center;
    gap: 6px;

    .model-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;
    }
  }
}
</style>
