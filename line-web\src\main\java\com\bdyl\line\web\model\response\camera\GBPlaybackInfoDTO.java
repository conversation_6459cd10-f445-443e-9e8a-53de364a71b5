package com.bdyl.line.web.model.response.camera;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

/**
 * 回放信息
 */
@Data
@Builder
public class GBPlaybackInfoDTO {
    /**
     * 录像文件名
     */
    private String m3u8Url;
    /**
     * 录像时长
     */
    private long duration; // 毫秒
    /**
     * 录像开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    /**
     * 录像结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
