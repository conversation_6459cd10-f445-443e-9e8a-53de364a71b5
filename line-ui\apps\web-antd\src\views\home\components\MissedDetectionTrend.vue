<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

interface Props {
  data: Array<{
    month: number;
    missedPercent: number;
  }>;
}

const props = defineProps<Props>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

const renderChart = () => {
  if (!props.data || props.data.length === 0) return;

  const months = props.data.map((item) => `${item.month}月`);
  const rates = props.data.map((item) => item.missedPercent);

  renderEcharts({
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}%',
    },
    grid: {
      top: '3%',
      left: '3%',
      right: '4%',
      bottom: '5%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: months,
      axisLine: {
        lineStyle: {
          color: '#84868e',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%',
      },
      axisLine: {
        lineStyle: {
          color: '#84868e',
        },
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
        },
      },
    },
    series: [
      {
        name: '漏检率趋势',
        type: 'line',
        smooth: true,
        data: rates,
        itemStyle: {
          color: '#ff4d4f',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(255, 77, 79, 0.3)',
              },
              {
                offset: 1,
                color: 'rgba(255, 77, 79, 0.05)',
              },
            ],
          },
        },
        lineStyle: {
          width: 3,
        },
        symbol: 'circle',
        symbolSize: 6,
      },
    ],
  });
};

watch(() => props.data, renderChart, { deep: true });

onMounted(() => {
  renderChart();
});
</script>

<template>
  <EchartsUI style="height: 100%" ref="chartRef" />
</template>
