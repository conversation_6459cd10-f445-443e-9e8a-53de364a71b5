package com.bdyl.line.web.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

import com.bdyl.boot.data.query.Page;
import com.bdyl.line.web.entity.InspectionTaskEntity;
import com.bdyl.line.web.model.request.inspection.InspectionTaskPageRequest;
import com.bdyl.line.web.model.response.inspection.InspectionTaskCameraResponse;
import com.bdyl.line.web.model.response.inspection.InspectionTaskExportDTO;
import com.bdyl.line.web.model.response.inspection.InspectionTaskResponse;

/**
 * 巡检任务服务接口
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface InspectionTaskService extends IService<InspectionTaskEntity> {

    /**
     * 生成巡检任务(定时调用)
     */
    void generateTasks();

    /**
     * 分页查询巡检任务
     *
     * @param request 分页请求
     * @return 分页结果
     */
    Page<InspectionTaskResponse> pageTasks(InspectionTaskPageRequest request);

    /**
     * 查询任务摄像头列表
     *
     * @param id 任务ID
     * @return 摄像头列表
     */
    List<InspectionTaskCameraResponse> getTaskCameras(Long id);

    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     */
    void updateTaskProgress(Long taskId);

    /**
     * 处理漏检任务 根据巡检周期动态判断任务是否漏检
     */
    void handleMissedTasks();

    /**
     * 导出巡检任务
     *
     * @param request 筛选条件
     * @return 导出结果
     */
    List<InspectionTaskExportDTO> export(InspectionTaskPageRequest request);
}
