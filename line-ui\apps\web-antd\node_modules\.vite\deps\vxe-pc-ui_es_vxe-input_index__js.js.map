{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/input/index.js", "../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-input/index.js"], "sourcesContent": ["import { VxeUI } from '@vxe-ui/core';\nimport VxeInputConstructor from './src/input';\nimport { dynamicApp } from '../dynamics';\nexport const VxeInput = Object.assign(VxeInputConstructor, {\n    install(app) {\n        app.component(VxeInputConstructor.name, VxeInputConstructor);\n    }\n});\ndynamicApp.use(VxeInput);\nVxeUI.component(VxeInputConstructor);\nexport const Input = VxeInput;\nexport default VxeInput;\n", "import VxeInput from '../input';\nexport * from '../input';\nexport default VxeInput;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGO,IAAM,WAAW,OAAO,OAAO,eAAqB;AAAA,EACvD,QAAQ,KAAK;AACT,QAAI,UAAU,cAAoB,MAAM,aAAmB;AAAA,EAC/D;AACJ,CAAC;AACD,WAAW,IAAI,QAAQ;AACvB,MAAM,UAAU,aAAmB;AAC5B,IAAM,QAAQ;AACrB,IAAOA,iBAAQ;;;ACTf,IAAO,oBAAQC;", "names": ["input_default", "input_default"]}