<template>
  <div class="map-popup-window">
    <!-- 关闭按钮 -->
    <div class="close-btn" @click="closePopup">
      <svg
        width="14"
        height="14"
        viewBox="0 0 14 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M13 1L1 13M1 1L13 13"
          stroke="#999"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>

    <div class="item-info">
      <div class="info-row">
        <span class="info-label">区域：</span>
        <span class="info-value"> {{ popupData.location }} </span>
      </div>
      <div class="info-row">
        <span class="info-label">位置：</span>
        <span class="info-value">
          {{ formatCoordinates() }}
        </span>
      </div>
      <div class="info-row">
        <span class="info-label">视频监控：</span>
        <span class="info-value"> {{ popupData.name }} </span>
      </div>
      <div class="info-row" v-if="popupData.status !== undefined">
        <span class="info-label">设备状态：</span>
        <span
          class="info-value"
          :class="
            popupData.status === 'ONLINE' ? 'text-success' : 'text-danger'
          "
        >
          {{ dictStore.getDictLable('CameraStatusEnum', popupData.status) }}
        </span>
      </div>
      <div class="info-row">
        <span class="info-label">终端状态：</span>
        <span class="info-value">
          {{
            dictStore.getDictLable(
              'TerminalStatusEnum',
              popupData.terminalStatus,
            )
          }}
        </span>
      </div>
      <div class="info-row">
        <span class="info-label">蓄电池电量：</span>
        <span class="info-value"> {{ popupData.batteryLevel }} </span>
      </div>
      <div
        class="info-row"
        style="color: red"
        v-if="popupData.currentAlertType"
      >
        <span class="info-label" style="color: red">当前报警：</span>
        <span class="info-value">
          {{
            dictStore.getDictDescription(
              'BizAlertTypeEnum',
              popupData.currentAlertType,
            )
          }}
        </span>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="btn-box">
      <Space>
        <Button @click="toTerminalDetail">物联数据</Button>
        <Button type="primary" @click="handleViewVideo">查看视频</Button>
      </Space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Space, Button } from 'ant-design-vue';

import { useDictStore } from '#/store';

const dictStore = useDictStore();

const props = defineProps({
  popupData: {
    type: Object,
    default: () => ({}),
  },
  onPlayVideo: {
    type: Function,
    default: null,
  },
  onToTerminalDetail: {
    type: Function,
    default: null,
  },
});

const emit = defineEmits(['closePopup', 'playVideo']);

// 关闭弹窗
const closePopup = () => {
  emit('closePopup', props.popupData);
};

// 查看视频
const handleViewVideo = () => {
  // 优先使用props中的回调函数
  if (props.onPlayVideo && typeof props.onPlayVideo === 'function') {
    props.onPlayVideo(props.popupData);
  } else {
    // 同时触发父组件的视频播放事件
    emit('playVideo', props.popupData);
  }
};

// 跳转到终端详情
const toTerminalDetail = () => {
  // 优先使用props中的回调函数
  if (
    props.onToTerminalDetail &&
    typeof props.onToTerminalDetail === 'function'
  ) {
    props.onToTerminalDetail(props.popupData);
  }
};

// 格式化坐标
const formatCoordinates = () => {
  const lat = props.popupData.latitude;
  const lon = props.popupData.longitude;

  if (lat && lon) {
    return `${lon.toFixed(6)}, ${lat.toFixed(6)}`;
  }

  return '未知';
};
</script>

<style scoped lang="scss">
.map-popup-window {
  width: 200px;
  display: flex;
  flex-direction: column;
  position: relative;

  .item-name {
    font-size: 16px;
    font-weight: bold;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
  }

  .item-info {
    padding: 2px 0;

    .info-row {
      display: flex;
      margin: 2px 0;

      .info-label {
        min-width: 50px;
        color: #909399;
      }

      .info-value {
        flex: 1;
        word-break: break-all;
      }
    }
  }

  .text-success {
    color: #67c23a;
  }

  .text-danger {
    color: #f56c6c;
  }

  .close-btn {
    position: absolute;
    top: -5px;
    right: -15px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    transition: all 0.2s ease;
    z-index: 10;

    &:hover {
      background-color: rgba(255, 255, 255, 1);
      transform: scale(1.1);

      svg path {
        stroke: #ff4d4f;
      }
    }

    svg {
      transition: all 0.2s ease;
    }
  }
}
</style>
