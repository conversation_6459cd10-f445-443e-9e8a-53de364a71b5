package com.bdyl.line.web.service.impl;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.bdyl.line.web.config.LineProperties;

/**
 * @ClassName ImageService
 * <AUTHOR>
 * @Date 2024/6/3 11:25
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ImageService {

    /**
     * 允许的图片文件扩展名
     */
    private static final List<String> ALLOWED_IMAGE_EXTENSIONS =
        Arrays.asList(".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp");

    /**
     * 允许的视频文件扩展名
     */
    private static final List<String> ALLOWED_VIDEO_EXTENSIONS =
        Arrays.asList(".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv", ".webm");

    /**
     * 允许的文档文件扩展名
     */
    private static final List<String> ALLOWED_DOCUMENT_EXTENSIONS =
        Arrays.asList(".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt");

    /**
     * 最大文件大小 (100MB)
     */
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024;

    /**
     * 系统配置
     */
    private final LineProperties lineProperties;

    /**
     * 保存字节数组为图片
     *
     * @param bytes 字节数组
     * @return 图片相对路径
     */
    public String saveImage(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            log.warn("图片字节数组为空");
            return null;
        }

        String uploadDir = lineProperties.getUploadDir();
        String relativeUploadDir = lineProperties.getRelativeUploadDir();

        String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String fileName = UUID.randomUUID().toString().replaceAll("-", "") + ".jpg";

        try {
            // 创建目录结构
            Path dirPath = Paths.get(uploadDir, datePath);
            Files.createDirectories(dirPath);

            // 保存图片文件
            Path filePath = dirPath.resolve(fileName);
            Files.write(filePath, bytes);

            String relativePath = relativeUploadDir + "/" + datePath + "/" + fileName;
            log.info("图片保存成功: {}", relativePath);
            return relativePath;

        } catch (IOException e) {
            log.error("保存图片失败", e);
            return null;
        }
    }

    /**
     * 保存字节数组为视频
     *
     * @param bytes 字节数组
     * @return 视频相对路径
     */
    public String saveVideo(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            log.warn("视频字节数组为空");
            return null;
        }

        String uploadDir = lineProperties.getUploadDir();
        String relativeUploadDir = lineProperties.getRelativeUploadDir();

        String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String fileName = UUID.randomUUID().toString().replaceAll("-", "") + ".mp4";

        try {
            // 创建目录结构
            Path dirPath = Paths.get(uploadDir, datePath);
            Files.createDirectories(dirPath);

            // 保存视频文件
            Path filePath = dirPath.resolve(fileName);
            Files.write(filePath, bytes);

            String relativePath = relativeUploadDir + "/" + datePath + "/" + fileName;
            log.info("视频保存成功: {}", relativePath);
            return relativePath;

        } catch (IOException e) {
            log.error("保存视频失败", e);
            return null;
        }
    }

    /**
     * 保存上传的文件
     *
     * @param file 上传的文件
     * @return 文件相对路径
     */
    public String saveFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            log.warn("上传文件为空");
            return null;
        }

        // 验证文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            log.warn("文件大小超过限制: {} bytes, 最大允许: {} bytes", file.getSize(), MAX_FILE_SIZE);
            return null;
        }

        // 获取原始文件名和扩展名
        String originalFilename = file.getOriginalFilename();
        String fileExtension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
        }

        // 验证文件类型
        if (!isAllowedFileType(fileExtension)) {
            log.warn("不支持的文件类型: {}, 原始文件名: {}", fileExtension, originalFilename);
            return null;
        }

        String uploadDir = lineProperties.getUploadDir();
        String relativeUploadDir = lineProperties.getRelativeUploadDir();

        String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String fileName = UUID.randomUUID().toString().replaceAll("-", "") + fileExtension;

        try {
            // 创建目录结构
            Path dirPath = Paths.get(uploadDir, datePath);
            Files.createDirectories(dirPath);

            // 保存文件
            Path filePath = dirPath.resolve(fileName);
            file.transferTo(filePath.toFile());

            String relativePath = relativeUploadDir + "/" + datePath + "/" + fileName;
            log.info("文件保存成功: {}, 原始文件名: {}", relativePath, originalFilename);
            return relativePath;

        } catch (IOException e) {
            log.error("保存文件失败: {}", originalFilename, e);
            return null;
        }
    }

    /**
     * 检查文件类型是否被允许
     *
     * @param fileExtension 文件扩展名
     * @return 是否允许
     */
    private boolean isAllowedFileType(String fileExtension) {
        if (fileExtension == null || fileExtension.isEmpty()) {
            return false;
        }

        return ALLOWED_IMAGE_EXTENSIONS.contains(fileExtension) || ALLOWED_VIDEO_EXTENSIONS.contains(fileExtension)
            || ALLOWED_DOCUMENT_EXTENSIONS.contains(fileExtension);
    }

}
