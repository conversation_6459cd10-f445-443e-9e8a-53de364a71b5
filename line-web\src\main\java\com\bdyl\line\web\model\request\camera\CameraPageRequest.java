package com.bdyl.line.web.model.request.camera;

import lombok.Data;

import com.bdyl.boot.data.query.PageRequest;

/**
 * 摄像头请求对象，用于创建和更新摄像头。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class CameraPageRequest extends PageRequest {
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 组织ID
     */
    private Long organId;
    /**
     * 终端ID
     */
    private Long terminalId;
    /**
     * 摄像头名称
     */
    private String name;
    /**
     * 摄像头编码
     */
    private String code;

    /**
     * 场景ID
     */
    private Long sceneId;

    /**
     * 摄像头状态 {@link com.bdyl.line.common.constant.enums.CameraStatusEnum}
     */
    private String status;

    /**
     * 摄像头位置
     */
    private String location;
}
