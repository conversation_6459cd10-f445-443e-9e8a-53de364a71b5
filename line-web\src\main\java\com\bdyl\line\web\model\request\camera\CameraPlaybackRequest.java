package com.bdyl.line.web.model.request.camera;

import jakarta.validation.constraints.NotBlank;

import lombok.Data;

/**
 * 视频回放请求DTO
 */
@Data
public class CameraPlaybackRequest {

    /**
     * 摄像头编码
     */
    @NotBlank(message = "摄像头编码不能为空不能为空")
    private String deviceCode;

    /**
     * 开始时间
     */
    @NotBlank(message = "开始时间不能为空")
    private String startTime;

    /**
     * 结束时间
     */
    @NotBlank(message = "结束时间不能为空")
    private String endTime;
}
