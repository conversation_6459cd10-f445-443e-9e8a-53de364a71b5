<template>
  <div :id="mapId" class="h-full w-full"></div>
</template>

<script setup lang="ts">
import {
  createApp,
  ref,
  defineProps,
  defineEmits,
  watch,
  onMounted,
  nextTick,
} from 'vue';

import PointPopup from './pointPopup.vue';

const props = defineProps({
  mapCenter: {
    type: Array,
    default: () => {},
  },
});

const mapId = ref('');

// 地图底图
const url = `https://t0.tianditu.gov.cn/img_w/wmts?layer=img&style=default&tilematrixset=w&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}&tk=${import.meta.env.VITE_TIANDITU_KEY}`;

// 添加天地图注记层
const labelUrl = `https://t0.tianditu.gov.cn/cia_w/wmts?layer=cia&style=default&tilematrixset=w&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}&tk=${import.meta.env.VITE_TIANDITU_KEY}`;

let map: any = {};

const emit = defineEmits([
  'mapInited',
  'clickPositionChange',
  'markerClick',
  'drawCreated',
]);

const addressMatchService = ref();

// 初始化地图
const initMap = () => {
  // 初始化地图信息
  map = L.map(mapId.value, {
    crs: L.CRS.EPSG3857, // 修改为Web墨卡托投影
    center: { lat: 30.65066, lng: 104.09024 },
    zoom: 12,
    zoomControl: true,
    maxZoom: 18,
    minZoom: 1,
  });

  // 添加底图
  L.tileLayer(url, {
    maxZoom: 18,
    tileSize: 256,
  }).addTo(map);

  // 添加注记层
  L.tileLayer(labelUrl, {
    maxZoom: 18,
    tileSize: 256,
  }).addTo(map);

  //初始化完成
  emit('mapInited', map);

  //事件对象注册
  registerEvent();

  //地址匹配服务创建
  createAddressMatchService();
};

const createAddressMatchService = () => {
  const addressUrl =
    'https://iserver.supermap.io/iserver/services/addressmatch-Address/restjsr/v1/address';
  addressMatchService.value = new L.supermap.AddressMatchService(addressUrl);
};

const registerEvent = () => {
  // 监听地图点击事件
  map.on('click', function (e) {
    // 获取点击的坐标信息
    const lngLat = e.latlng;
    // 在控制台输出坐标
    console.log(lngLat);
    emit('clickPositionChange', lngLat);
  });
};

/**
 * 创建marker
 * @param {Array<{latitude: number, longitude?: number, longtitude?: number, icon?: {iconUrl: string}}> } list 数据列表
 * @param {boolean} hasPopup 是否显示弹窗
 * @param {Component} popupComponent 弹窗组件
 * @param {boolean} hasMouseover 是否显示鼠标悬停事件
 * @returns {Array<L.Marker>} marker数组
 */
const createMarker = (
  list: any,
  hasPopup = true,
  popupComponent: any,
  openPopupType: 'click' | 'mouseover' = 'click',
  eventCallbacks: any = {},
) => {
  return list.map((element: any) => {
    const marker = L.marker([
      element.latitude || element.lat,
      element.longitude || element.longtitude || element.lng,
    ]);
    if (element.markerOptions && element.markerOptions.icon) {
      marker.setIcon(
        L.icon({
          ...(element.markerOptions.icon || {}),
        }),
      );
    }

    marker.on(openPopupType, () => {
      emit('markerClick', {
        marker: marker,
        data: element,
      });
      if (hasPopup) {
        // 移除旧的 popup
        marker.unbindPopup();
        // 创建新的 popup
        const popup = createPopup(element, popupComponent, eventCallbacks);
        marker.bindPopup(popup);
        // 打开新创建的 popup
        marker.openPopup();

        // 如果是鼠标悬停模式，为弹窗添加鼠标事件
        if (openPopupType === 'mouseover') {
          // 延迟执行，确保弹窗已经渲染到DOM中
          setTimeout(() => {
            const popupElement = popup.getElement();
            if (popupElement) {
              // 鼠标进入弹窗时清除关闭定时器
              popupElement.addEventListener('mouseover', () => {
                if (marker._closeTimer) {
                  clearTimeout(marker._closeTimer);
                  marker._closeTimer = null;
                }
              });

              // 鼠标离开弹窗时设置关闭定时器
              popupElement.addEventListener('mouseout', () => {
                marker._closeTimer = setTimeout(() => {
                  marker.closePopup();
                }, 100); // 100ms延迟，避免鼠标快速移动时误关闭
              });
            }
          }, 50);
        }
      }
    });

    // 如果是鼠标悬停模式，添加标记的鼠标移出事件
    if (openPopupType === 'mouseover' && hasPopup) {
      marker.on('mouseout', () => {
        // 设置延迟关闭，给用户时间移动到弹窗上
        marker._closeTimer = setTimeout(() => {
          marker.closePopup();
        }, 200); // 200ms延迟
      });
    }

    marker.addTo(map);

    marker.data = element; // 方便定位marker
    return marker;
  });
};

/**
 * 创建弹窗
 * @param {L.Marker} marker 地图marker
 * @param {Object} data 数据
 * @param {Component} popupComponent 弹窗组件
 * @param {Object} eventCallbacks 事件回调
 * @returns {L.Popup} 弹窗
 */
const createPopup = (data: any, popupComponent = PointPopup, eventCallbacks: any = {}) => {
  const dom = document.createElement('div');

  // 先创建弹窗对象
  const popupContent = L.popup({
    ...(data.popupOptions || {}),
    closeButton: false,
    autoClose: false,
    closeOnClick: false,
    closeOnEscapeKey: false,
    offset:
      data.popupOptions && data.popupOptions.offset
        ? L.point(...data.popupOptions.offset)
        : L.point(0, 0),
    autoPanPaddingTopLeft:
      data.popupOptions && data.popupOptions.autoPanPaddingTopLeft
        ? L.point(...data.popupOptions.autoPanPaddingTopLeft)
        : L.point(0, 0),
  });

  // 创建弹窗内容
  const htmlElement = createApp(popupComponent, {
    popupData: data,
    onClosePopup: (_data: any) => {
      // 找到对应的marker并关闭弹窗
      map.eachLayer((layer: any) => {
        if (layer instanceof L.Marker && layer.getPopup() === popupContent) {
          layer.closePopup();
        }
      });
    },
    ...eventCallbacks,
  }).mount(dom);

  // 将弹窗内容添加到弹窗对象中
  popupContent.setContent(htmlElement.$el);
  return popupContent;
};

//正向地址匹配
const addressCode1 = (address: string) => {
  return new Promise((reslove, reject) => {
    const geoCodeParam = new L.supermap.GeoCodingParameter({
      address, // 地址
      fromIndex: 0, // 设置返回对象的起始索引值
      toIndex: 10, // 设置返回对象的结束索引值
      filters: '成都市,高新区', // 过滤条件
      prjCoordSys: { epsgcode: 4326 }, // 坐标设置
      maxReturn: 5, // 最大返回结果数
    });
    addressMatchService.value
      .code(geoCodeParam)
      .then(function (obj) {
        // 获取服务端返回的结果
        reslove(obj.result);
      })
      .catch((error) => {
        console.log(error);
        debugger;
      });
  });
};

/**
 * 添加热力图图层
 * @param {Array<{latitude: number, longitude?: number, longtitude?: number, icon?: {iconUrl: string}}> } heatPoints 热力点数据
 * @param {Object} options 热力图配置
 * @returns {L.Layer} 热力图图层
 */
const createHeatMap = (
  heatPoints: any[],
  options = { heatRadius: 30, minOpacity: 0.5 },
) => {
  // 调用渲染方法，传入半径，透明度参数，添加到地图
  return L.heatLayer(heatPoints, {
    radius: options.heatRadius,
    minOpacity: options.minOpacity,
  }).addTo(map);
};

/**
 * 加载绘制工具
 * @param {Object} options 绘制工具配置
 * @returns {L.FeatureGroup} 绘制工具图层
 */
let editableLayers: any;
let drawControl: any;
const createDrawControl = ({ options, multiple = false }) => {
  editableLayers = new L.FeatureGroup();

  map.addLayer(editableLayers);

  // 绘制控件参数配置
  const resultOptions = {
    position: options.position,
    draw: options.draw,
    edit: options.edit
      ? Object.assign(
          {
            featureGroup: editableLayers,
            remove: true,
          },
          options.edit,
        )
      : options.edit,
  };

  drawControl = new L.Control.Draw(resultOptions);

  map.addControl(drawControl);

  handleMapEvent(drawControl._container, map);

  map.on(L.Draw.Event.CREATED, (e) => {
    if (!multiple) {
      editableLayers.clearLayers();
    }
    editableLayers.addLayer(e.layer);
    emit('drawCreated', e);
  });

  map.on(L.Draw.Event.EDITED, (e) => {
    e.layers.eachLayer((layer) => {
      emit('drawCreated', {
        layer: layer,
      });
    });
  });

  return editableLayers;
};

/**
 * 清除绘制工具
 */
const clearDrawControl = () => {
  if (editableLayers) {
    map.removeLayer(editableLayers);
    editableLayers = undefined;
  }
  if (drawControl) {
    map.removeControl(drawControl);
    drawControl = undefined;
  }
};

/**
 * 处理地图事件
 * @param {HTMLElement} div 地图容器
 * @param {L.Map} map 地图实例
 * @returns {void} 无返回值
 */
const handleMapEvent = (div, map) => {
  if (!div || !map) {
    return;
  }
  div.addEventListener('mouseover', function () {
    map.scrollWheelZoom.disable();
    map.doubleClickZoom.disable();
  });
  div.addEventListener('mouseout', function () {
    map.scrollWheelZoom.enable();
    map.doubleClickZoom.enable();
  });
};

/**
 * 创建图形
 * @param {string} type 图形类型 'polygon'|'polyline'|'circle'|'circlemarker'
 * @param {Array<{lat: number, lng: number}> | {lat: number, lng: number}} latlngs 坐标点数组或圆心坐标
 * @param {Object} options 配置选项
 * @returns {L.Layer} 图层对象
 */
const createOverlay = (
  type: 'polygon' | 'polyline' | 'circle' | 'circlemarker',
  latlngs: any,
  options: any = {},
) => {
  switch (type) {
    case 'polygon':
      return L.polygon(latlngs, options);
    case 'polyline':
      return L.polyline(latlngs, options);
    case 'circle':
      return L.circle(latlngs, {
        radius: options.radius ?? 1000,
        ...options,
      });
    case 'circlemarker':
      if (Array.isArray(latlngs)) {
        const layerGroup = L.layerGroup();
        latlngs.forEach((latlng) => {
          layerGroup.addLayer(
            L.circleMarker(latlng, {
              radius: options.radius ?? 10,
              ...options,
            }),
          );
        });
        return layerGroup;
      } else {
        return L.circleMarker(latlngs, {
          radius: options.radius ?? 10,
          ...options,
        });
      }
    default:
      console.error('不支持的图形类型');
      return null;
  }
};

/**
 * 适配地图边界
 * @param {Array<L.Layer>} layers 图层数组
 * @returns {void} 无返回值
 */
const fitBounds = (layers: any[]) => {
  map.fitBounds(
    layers.reduce(
      (bounds, layer) => bounds.extend(layer.getBounds()),
      L.latLngBounds(),
    ),
  );
};

/**
 * 在地图上绘制 3 层圆（同心圆）
 * @param {Array} center 圆心坐标 [纬度, 经度]
 * @param {Array} radii 半径数组，单位：米（从内到外）
 * @param {Array} colors 颜色数组，从内到外依次填充
 * @returns {Array} 返回 3 个圆对象
 */
const addTripleCircle = (center, radii) => {
  console.log(radii, 3123123);
  // **清除之前的圆层**
  map.eachLayer((layer) => {
    if (layer instanceof L.Circle) {
      map.removeLayer(layer);
    }
  });

  if (!radii || radii.length === 0) {
    console.error('❌ radii 参数不能为空！');
    return [];
  }

  // **确保半径从小到大排序**
  const sortedCircles = radii
    .slice(0, 3) // 只取最多 3 个值
    .sort((a, b) => b.radius - a.radius); // **从小到大排序**
  console.log(sortedCircles);
  // **绘制同心圆（保证最大的在底层，最小的在上层）**
  const circles = sortedCircles.map(({ radius, color }) =>
    L.circle(center, {
      radius,
      color: 'transparent', // 无边框
      fillColor: color, // 使用传入的颜色
      fillOpacity: 1, // 完全不透明，避免颜色重叠混合
      weight: 0, // 无边框
    }).addTo(map),
  );

  // **动态计算缩放级别**
  const maxRadius = sortedCircles[sortedCircles.length - 1]?.radius || 0; // 获取最大半径
  let zoom =
    map.getBoundsZoom([
      [center[0] + maxRadius * 0.0000089, center[1] - maxRadius * 0.0000089], // 近似边界
      [center[0] - maxRadius * 0.0000089, center[1] + maxRadius * 0.0000089],
    ]) - 1; // 适当放大视角

  zoom = Math.min(map.getMaxZoom(), zoom); // 防止超过最大缩放级别

  // **调整视角，使圆心放大**
  map.setView(center, zoom, {
    animate: true,
    duration: 1.5,
    easeLinearity: 0.25,
  });

  return circles;
};

/**
 * 在地图上绘制不同颜色的点阵
 * @param {Array} center 圆心坐标 [纬度, 经度]
 * @param {Object} color 每种颜色的数量 { yellow: 50, orange: 30, red: 10 }
 * @param {Number} spacing 点间距（单位：米）
 */
const addCircle = (center, color, radius, zoomLevel = null, tooltipText) => {
  // **仅绘制新圆，不清除旧圆**
  const circle = L.circle(center, {
    radius: radius, // 圆的半径（单位：米）
    color: 'transparent',
    fillColor: color,
    fillOpacity: 0.9,
  }).addTo(map);

  // **鼠标移入时显示提示框**
  circle.bindTooltip(tooltipText, {
    permanent: false, // 非永久显示，鼠标移入时才显示
    direction: 'top', // 提示框显示在圆的上方
    opacity: 0.9,
  });

  // **平滑移动并放大**
  if (zoomLevel !== null) {
    map.flyTo(center, zoomLevel, {
      animate: true,
      duration: 1.5, // 动画时间
      easeLinearity: 0.25, // 平滑度
    });
  } else {
    // 如果没传zoomLevel，默认放大3级
    let zoom = Math.min(map.getMaxZoom(), map.getZoom());
    map.flyTo(center, zoom, {
      animate: true,
      duration: 1.5,
      easeLinearity: 0.25,
    });
  }

  return circle;
};

const clearingCompensation = () => {
  // **清除之前的点层**
  map.eachLayer((layer) => {
    if (layer instanceof L.Circle) {
      map.removeLayer(layer);
    }
  });
};

/**
 * 测量距离
 * @param {L.Layer} layer 图层
 * @returns {void} 无返回值
 */
const measureDistance = (layer) => {
  // 如果没有图层,直接返回
  if (!layer) return;

  // 获取图层的所有坐标点
  const latlngs = layer.getLatLngs();

  // 计算总距离
  let totalDistance = 0;
  for (let i = 1; i < latlngs.length; i++) {
    totalDistance += latlngs[i - 1].distanceTo(latlngs[i]);
  }

  // 转换为公里并保留2位小数
  const distanceInKm = (totalDistance / 1000).toFixed(2);

  // 在线段中点添加距离标签
  const midPoint = layer.getCenter();
  const tooltip = L.tooltip({
    permanent: true,
    direction: 'center',
    className: 'distance-tooltip',
  })
    .setContent(`${distanceInKm} km`)
    .setLatLng(midPoint);

  tooltip.addTo(map);

  // 将tooltip与图层关联,以便后续清除
  layer.bindTooltip(tooltip);
};

/**
 * 判断点是否在多边形内
 * @param {number} ALon 经度
 * @param {number} ALat 纬度
 * @param {Array} APoints 多边形顶点数组
 * @returns {boolean} 是否在多边形内
 */
const IsPtInPoly = (point: any, polygon: any) => {
  const turfPoint = turf.point(point);
  const turfPolygon = turf.polygon([[...polygon, polygon[0]]]);
  return turf.booleanPointInPolygon(turfPoint, turfPolygon);
};

watch(
  () => props.mapCenter,
  (newV) => {
    map.setView(newV);
  },
);

onMounted(() => {
  mapId.value = `map-container-${new Date().getTime()}`;
  nextTick(() => {
    initMap();
  });
});

//正向地址匹配（根据地名获取经纬度）
const addressCode = (address: string) => {
  return new Promise((resolve, reject) => {
    // 调用天地图地理编码接口
    fetch(
      `http://api.tianditu.gov.cn/geocoder?ds={'keyWord':'${address}'}&tk=${import.meta.env.VITE_TIANDITU_KEY}`,
    )
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        console.log('data-----------', data);

        if (data && data.location) {
          map.setView([data.location.lat, data.location.lon], 15);
          // 返回位置信息
          resolve({
            lon: data.location.lon,
            lat: data.location.lat,
          });
        } else {
          reject(new Error('未找到搜索结果，请尝试其他关键词'));
        }
      })
      .catch((error) => {
        console.error('地理编码失败:', error);
        reject(error);
      });
  });
};

// 逆向地址匹配（根据经纬度获取地名）
const reverseGeocode = (lng: string, lat: string) => {
  return new Promise((resolve, reject) => {
    // 调用天地图逆地理编码接口
    fetch(
      `http://api.tianditu.gov.cn/geocoder?postStr={'lon':'${lng}','lat':'${lat}','ver':'1'}&type=geocode&tk=${import.meta.env.VITE_TIANDITU_KEY}`,
    )
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        if (data && data.result) {
          // 返回地址信息
          resolve(data.result);
        } else {
          reject(new Error('未找到该坐标对应的地址信息'));
        }
      })
      .catch((error) => {
        console.error('逆地理编码失败:', error);
        reject(error);
      });
  });
};

defineExpose({
  createMarker,
  createHeatMap,
  createDrawControl,
  createOverlay,
  fitBounds,
  addTripleCircle,
  addCircle,
  clearingCompensation,
  measureDistance,
  clearDrawControl,
  addressCode, // 正向地址匹配
  reverseGeocode, // 逆向地址匹配
  IsPtInPoly, // 判断点是否在多边形内
});
</script>

<style lang="scss" scoped></style>
