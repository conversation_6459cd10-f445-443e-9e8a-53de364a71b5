<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import * as InspectionPlanApi from '#/api/core/inspection-plan';
import { getUsers_Api } from '#/api/core/user';
import tableComp from '#/components/TableComp/table.vue';
import { useDictStore } from '#/store/dict';
import { formatDate } from '@vben/utils';
import { downloadFileFromBlob } from '@vben/utils';

import CameraMapModal from './components/CameraMapModal.vue';
import InspectionModal from './components/InspectionModal.vue';

// 查询条件
const queryParam = reactive({
  responsibleUserId: undefined as string | undefined, // 负责人ID
  cycleType: undefined as string | undefined, // 巡检周期类型
  status: undefined as string | undefined, // 状态
  startDate: undefined as string | undefined, // 应巡查开始时间
  endDate: undefined as string | undefined, // 应巡查结束时间
  page: 1,
  size: 10,
});

const loading = ref(false);
const cameraMapRef = ref();
const inspectionModalRef = ref();

const dictStore = useDictStore();

// 巡检周期选项
const cycleOptions = dictStore.getDictOptions('InspectionCycleEnum');

// 任务状态选项
const statusOptions = dictStore.getDictOptions('InspectionTaskStatusEnum');

// 用户选项
const userOptions = ref<any[]>([]);

// 表格数据源
const tableData = reactive<any>({
  data: {},
});

// 获取用户列表
const getUserList = async () => {
  try {
    const res = await getUsers_Api({ page: 1, size: 1000 });
    userOptions.value = res?.data || [];
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
};

// 表格相关配置
const columns = [
  { title: '任务名称', dataIndex: 'taskName', width: 200 },
  {
    title: '巡检周期',
    dataIndex: 'cycleType',
    width: 120,
    customRender: ({ text }: { text: string }) => {
      const option = cycleOptions.find((item: any) => item.dictValue === text);
      return option?.dictLabel || '-';
    },
  },
  {
    title: '应巡检时间',
    dataIndex: 'scheduledStartTime',
    width: 200,
    customRender: ({ text, record }: { text: string; record: any }) => {
      if (!text) {
        return '-';
      }
      return (
        formatDate(text, 'YYYY-MM-DD HH:mm') +
        '~' +
        formatDate(record.scheduledEndTime, 'YYYY-MM-DD HH:mm')
      );
    },
  },
  { title: '状态', dataIndex: 'status', width: 100 },
  { title: '巡检负责人', dataIndex: 'responsibleUserName', width: 120 },
  { title: '操作', dataIndex: 'operation', width: 120, fixed: 'right' },
];

// 加载巡检任务列表
const loadInspectionTasks = async () => {
  loading.value = true;
  try {
    const res = await InspectionPlanApi.getTaskList_Api(queryParam);
    // 更新表格数据源
    const temp = {
      data: res.data || [],
      page: queryParam.page,
      size: queryParam.size,
      total: res.total || 0,
    };
    tableData.data = temp;
  } catch (error) {
    console.error('获取巡检任务列表失败', error);
    message.error('获取巡检任务列表失败');
  } finally {
    loading.value = false;
  }
};

const success = (data: any) => {
  queryParam.page = data.pi;
  queryParam.size = data.ps;
  loadInspectionTasks();
};

// 查询
const searchTable = () => {
  queryParam.page = 1;
  loadInspectionTasks();
};

// 重置
const resetTable = () => {
  queryParam.responsibleUserId = undefined;
  queryParam.cycleType = undefined;
  queryParam.status = undefined;
  queryParam.startDate = undefined;
  queryParam.endDate = undefined;
  queryParam.page = 1;
  queryParam.size = 10;
  loadInspectionTasks();
};

// 查看摄像头分布
const handleViewCameras = (taskId: number) => {
  const task = tableData.data.data.find((item: any) => item.id === taskId);
  if (task) {
    cameraMapRef.value?.openModal(task);
  }
};

// 去巡检
const handleInspection = (record: any) => {
  inspectionModalRef.value?.openModal(record, 'inspection');
};

// 查看记录
const handleViewRecord = (record: any) => {
  inspectionModalRef.value?.openModal(record, 'view');
};

// 用户选择处理
const handleUserChange = (_userId: number) => {
  // 触发查询
  searchTable();
};

// 时间范围选择处理
const handleDateRangeChange = (dates: any) => {
  if (dates && dates.length === 2) {
    queryParam.startDate = dates[0];
    queryParam.endDate = dates[1];
  } else {
    queryParam.startDate = undefined;
    queryParam.endDate = undefined;
  }
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    PENDING: 'orange',
    IN_PROGRESS: 'blue',
    COMPLETED: 'green',
    OVERDUE: 'red',
    CANCELLED: 'gray',
  };
  return colorMap[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const option = statusOptions.find((item: any) => item.dictValue === status);
  return option ? option.dictLabel : status;
};

// 导出
const handleExport = async () => {
  const params = {
    responsibleUserId: queryParam.responsibleUserId,
    cycleType: queryParam.cycleType,
    status: queryParam.status,
    startDate: queryParam.startDate,
    endDate: queryParam.endDate,
  };
  const res = await InspectionPlanApi.exportInspectionTask_Api(params);
  downloadFileFromBlob({ source: res, fileName: '巡检任务.xlsx' });
};

onMounted(() => {
  getUserList();
  loadInspectionTasks();
});
</script>

<template>
  <Page>
    <a-card class="table_header_search mb-5" size="small">
      <a-row :gutter="20">
        <a-col :span="5">
          <label>负责人：</label>
          <div class="table_header_wrp_cont">
            <a-select
              v-model:value="queryParam.responsibleUserId"
              allow-clear
              placeholder="请选择负责人"
              style="width: 100%"
              show-search
              :filter-option="false"
              @change="handleUserChange"
            >
              <a-select-option
                v-for="user in userOptions"
                :key="user.id"
                :value="user.id.toString()"
              >
                {{ user.realName || user.username || user.name }}
              </a-select-option>
            </a-select>
          </div>
        </a-col>
        <a-col :span="5">
          <label>巡检周期：</label>
          <div class="table_header_wrp_cont">
            <a-select
              v-model:value="queryParam.cycleType"
              allow-clear
              placeholder="请选择巡检周期"
              style="width: 100%"
            >
              <a-select-option
                v-for="option in cycleOptions"
                :key="option.dictValue"
                :value="option.dictValue"
              >
                {{ option.dictLabel }}
              </a-select-option>
            </a-select>
          </div>
        </a-col>
        <a-col :span="5">
          <label>状态：</label>
          <div class="table_header_wrp_cont">
            <a-select
              v-model:value="queryParam.status"
              allow-clear
              placeholder="请选择状态"
              style="width: 100%"
            >
              <a-select-option
                v-for="option in statusOptions"
                :key="option.dictValue"
                :value="option.dictValue"
              >
                {{ option.dictLabel }}
              </a-select-option>
            </a-select>
          </div>
        </a-col>
        <a-col :span="8">
          <label>应巡查时间：</label>
          <div class="table_header_wrp_cont">
            <a-range-picker
              :value="[queryParam.startDate, queryParam.endDate]"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
            />
          </div>
        </a-col>
        <a-col :span="4">
          <a-button type="primary" class="searchBtn" @click="searchTable">
            查询
          </a-button>
          <a-button class="refBtn" @click="resetTable">重置</a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- 表格 -->
    <a-card size="small">
      <div class="table_action_btn_wrp">
        <a-button class="addBtn" @click="handleExport"> 导出 </a-button>
      </div>
      <tableComp
        :columns="columns"
        :data-source="tableData.data"
        :loading="loading"
        @success="success"
      >
        <!-- 应巡检时间列 -->
        <template #scheduledStartTime="{ record }">
          {{
            record.scheduledStartTime
              ? formatDate(record.scheduledStartTime, 'YYYY-MM-DD HH:mm') +
                '~' +
                formatDate(record.scheduledEndTime, 'YYYY-MM-DD HH:mm')
              : '-'
          }}
        </template>

        <!-- 状态列 -->
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>

        <!-- 光缆列 -->
        <template #cameraCount="{ record }">
          <a-button
            type="link"
            size="small"
            @click="handleViewCameras(record.id)"
          >
            查看
          </a-button>
        </template>

        <!-- 区域列 -->
        <template #planName="{ record }">
          {{ record.planName || '-' }}
        </template>

        <!-- 操作列  -->
        <template #operation="{ record }">
          <a-space>
            <a-button
              v-if="
                record.status === 'PENDING' || record.status === 'IN_PROGRESS'
              "
              type="link"
              size="small"
              @click="handleInspection(record)"
            >
              去巡检
            </a-button>
            <a-button
              v-if="record.status === 'COMPLETED'"
              type="link"
              size="small"
              @click="handleViewRecord(record)"
            >
              查看记录
            </a-button>
            <span v-if="record.status === 'OVERDUE'">-</span>
          </a-space>
        </template>
      </tableComp>
    </a-card>

    <!-- 摄像头地图弹窗 -->
    <CameraMapModal ref="cameraMapRef" />

    <!-- 巡检弹窗 -->
    <InspectionModal ref="inspectionModalRef" @success="loadInspectionTasks" />
  </Page>
</template>

<style lang="scss" scoped>
.table_header_search {
  .table_header_wrp_cont {
    margin-top: 8px;
  }

  label {
    font-weight: 500;
    color: #333;
  }

  .searchBtn {
    margin-right: 8px;
  }

  .refBtn {
    background: #f5f5f5;
    border-color: #d9d9d9;
    color: #666;

    &:hover {
      background: #e6f7ff;
      border-color: #1890ff;
      color: #1890ff;
    }
  }
}
</style>
