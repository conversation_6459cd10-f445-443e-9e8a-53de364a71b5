package com.bdyl.line.web.controller;

import java.io.IOException;
import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bdyl.boot.JsonResult;
import com.bdyl.boot.data.query.Page;
import com.bdyl.line.web.model.request.inspection.InspectionTaskPageRequest;
import com.bdyl.line.web.model.response.inspection.InspectionTaskCameraResponse;
import com.bdyl.line.web.model.response.inspection.InspectionTaskExportDTO;
import com.bdyl.line.web.model.response.inspection.InspectionTaskResponse;
import com.bdyl.line.web.service.InspectionTaskService;
import com.bdyl.line.web.utils.ExcelUtil;

/**
 * 巡检任务管理
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@RestController
@RequestMapping("/inspection/task")
@RequiredArgsConstructor
public class InspectionTaskController {

    /**
     * 巡检任务service
     */
    private final InspectionTaskService inspectionTaskService;

    /**
     * 分页查询巡检任务
     *
     * @param request 分页请求
     * @return 分页结果
     */
    @GetMapping("/page")
    public JsonResult<Page<InspectionTaskResponse>> page(@Valid InspectionTaskPageRequest request) {
        return JsonResult.success(inspectionTaskService.pageTasks(request));
    }

    /**
     * 获取任务下的所有摄像头
     *
     * @param id 任务ID
     * @return 摄像头列表
     */
    @GetMapping("/{id}")
    public JsonResult<List<InspectionTaskCameraResponse>> getTaskCameras(@PathVariable Long id) {
        return JsonResult.success(inspectionTaskService.getTaskCameras(id));
    }

    /**
     * 导出
     *
     * @param request 筛选条件
     * @param response 响应
     */
    @GetMapping("/export")
    public void export(InspectionTaskPageRequest request, HttpServletResponse response) throws IOException {

        List<InspectionTaskExportDTO> export = inspectionTaskService.export(request);
        ExcelUtil.export(response, export, InspectionTaskExportDTO.class, "巡检任务");
    }

}
