package com.bdyl.line.common.constant.error;

import lombok.AllArgsConstructor;
import lombok.Getter;

import com.bdyl.boot.exception.Error;

/**
 * 业务通用error
 */
@Getter
@AllArgsConstructor
public enum CommonError implements Error {

    /**
     * 系统错误
     */
    ERROR("ERROR", "系统错误"),
    /**
     * 用户未登录
     */
    UN_AUTH("UN_AUTHENTICATION", "用户未登录"),
    /**
     * 非法请求
     */
    BAD_REQUEST("BAD_REQUEST", "非法请求"),
    /**
     * 禁止访问
     */
    ACCESS_DENIED("ACCESS_DENY", "禁止访问"),
    /**
     * 资源不存在
     */
    NOT_FOUND("NOT_FOUND", "资源不存在"),

    /**
     * 请求参数错误
     */
    PARAM_ERROR("PARAM_ERROR", "请求参数错误"),

    /**
     * 请求方法不支持
     */
    METHOD_NOT_ALLOWED("METHOD_NOT_ALLOWED", "请求方法不支持"),

    /**
     * 服务器繁忙，请稍候重试
     */
    TRY_LATER("TRY_LATER", "服务器繁忙，请稍候重试或联系管理员"),

    /**
     * 功能暂未实现
     */
    NOT_IMPLEMENTED("NOT_IMPLEMENTED", "功能暂未实现"),
    /**
     * 客户端不匹配
     */
    CLIENT_NOT_MATCH("CLIENT_NOT_MATCH", "客户应用不匹配"),
    /**
     * 微信code无效
     */
    INVALID_WECHAT_CODE("INVALID_WECHAT_CODE", "无效微信code"),
    /**
     * 获取微信用户信息失败
     */
    GET_WECHAT_USERINFO_FAILED("GET_WECHAT_USERINFO_FAILED", "获取微信微信用户信息失败"),
    /**
     * 数据重复
     */
    DATA_DUPLICATE("DATA_DUPLICATE", "数据已经存在"),
    /**
     * 不支持
     */
    NOT_SUPPORT("NOT_SUPPORT", "不支持的操作");

    /**
     * 错误码
     */
    private final String code;

    /**
     * 错误信息
     */
    private final String message;
}
