package com.bdyl.line.web.model.request.camera;

import lombok.Data;

/**
 * 设备查询条件数据传输对象
 */
@Data
public class GBCameraQueryDTO {

    /**
     * 设备编码（支持模糊查询）
     */
    private String deviceCode;

    /**
     * 设备名称（支持模糊查询）
     */
    private String name;

    /**
     * 厂商（支持模糊查询）
     */
    private String manufacturer;

    /**
     * 型号（支持模糊查询）
     */
    private String model;

    /**
     * 在线状态
     */
    private Boolean online;

    /**
     * 父设备/上级平台ID
     */
    private String parentId;
}
