import type { UserInfo } from '@vben/types';

import { requestClient } from '#/api/request';

enum Api {
  user = '/api/line/user',
}

/**
 * 获取用户信息
 */
export async function getUserInfoApi() {
  return requestClient.get<UserInfo>('/api/line/auth/current');
}

// 分页查询用户
export interface Userparams {
  page: number;
  size: number;
  organType?: string;
  organId?: number;
  name?: string;
  phone?: string;
  account?: string;
  departmentId?: number;
}
export const getUsers_Api = (params: Userparams) => {
  return requestClient.get(`${Api.user}/page`, { params });
};

// 创建用户
export interface UserParameter {
  id?: number;
  tenantId?: number;
  organId: number;
  organType: string;
  regionCode?: string;
  name?: string;
  phone?: string;
  account?: string;
  password?: string;
  roleIds?: number[];
  departmentId?: number;
}
export const addUser_Api = (data: UserParameter) => {
  return requestClient.post(`${Api.user}`, data);
};

// 修改用户
export const updateUser_Api = (id: string, data: UserParameter) => {
  return requestClient.put(`${Api.user}/${id}`, data);
};

// 修改用户状态
export const updateUserStatus_Api = (data: any) => {
  return requestClient.put(`${Api.user}/status`, data);
};

// 删除用户
export const deleteUser_Api = (id: any) => {
  return requestClient.delete(`${Api.user}/${id}`);
};

// 获取用户详情
export const getUser_Api = (id: any) => {
  return requestClient.get(`${Api.user}/${id}`);
};

// 重置密码
export const resetUserPassword_Api = (data: any) => {
  return requestClient.post(`/api/uaa/password/admin-reset`, data);
};

// 用户导出
export const exportUser_Api = (params: any) => {
  return requestClient.download(`${Api.user}/export`, { params });
};

// 获取组织树
export const getRegionByOrgan_Api = () => {
  return requestClient.get(`/api/line/organ/tree`);
};
