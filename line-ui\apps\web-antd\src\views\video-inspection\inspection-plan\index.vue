<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { message, Modal } from 'ant-design-vue';

import * as InspectionPlanApi from '#/api/core/inspection-plan';
import { getCameraByIds_Api } from '#/api/core/camera';
import tableComp from '#/components/TableComp/table.vue';
import LocationMapModal from '#/components/LocationMapModal/index.vue';

import InspectionPlanModal from './components/InspectionPlanModal.vue';
import { useDictStore } from '#/store/dict';
import { formatDate } from '@vben/utils';
import { downloadFileFromBlob } from '@vben/utils';

// 查询条件
const queryParam = reactive({
  cycleType: undefined as string | undefined, // 巡检周期类型
  responsibleUserId: undefined as number | undefined, // 负责人ID
  page: 1,
  size: 10,
  startTime: undefined,
  endTime: undefined,
});

// 创建时间范围选择
const createTimeRange = ref<[any, any] | undefined>(undefined);

// 显示设备位置弹窗
const showLocationModal = ref<boolean>(false);

const loading = ref(false);
const modalFormRef = ref();

const dictStore = useDictStore();

// 巡检周期选项
const cycleOptions = dictStore.getDictOptions('InspectionCycleEnum');

// 表格数据源
const tableData = reactive<any>({
  data: {},
});

// 表格相关配置
const columns = [
  { title: '计划名称', dataIndex: 'name' },
  {
    title: '巡检周期',
    dataIndex: 'cycleType',
    customRender: ({ text }: { text: string }) => {
      const option = cycleOptions.find((item: any) => item.dictValue === text);
      return option?.dictLabel || '-';
    },
  },
  {
    title: '设备',
    dataIndex: 'cameraIds',
    align: 'center',
  },
  { title: '启动日期', dataIndex: 'startDate' },
  { title: '巡检负责人', dataIndex: 'responsibleUserName' },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    customRender: ({ text }: { text: string }) => {
      return formatDate(text, 'YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '启用状态',
    dataIndex: 'status',
    customRender: ({ text }: { text: string }) => {
      return text === 'ENABLE' ? '启用' : '禁用';
    },
  },
  { title: '操作', dataIndex: 'operation' },
];

// 加载巡检计划列表
const loadInspectionPlans = async () => {
  loading.value = true;
  try {
    // 构建API参数，确保类型正确
    const apiParams = {
      ...queryParam,
      responsibleUserId: queryParam.responsibleUserId?.toString(),
    };
    const res = await InspectionPlanApi.getInspectionPlanList_Api(apiParams);
    // 更新表格数据源
    const temp = {
      data: res.data || [],
      page: queryParam.page,
      size: queryParam.size,
      total: res.total || 0,
    };
    tableData.data = temp;
  } catch (error) {
    console.error('获取巡检计划列表失败', error);
  } finally {
    loading.value = false;
  }
};

const success = (data: any) => {
  queryParam.page = data.pi;
  queryParam.size = data.ps;
  loadInspectionPlans();
};

// 处理时间范围变化
const handleTimeRangeChange = (dates: [any, any] | null) => {
  if (dates && dates.length === 2) {
    queryParam.startTime = dates[0]?.format('YYYY-MM-DD HH:mm:ss');
    queryParam.endTime = dates[1]?.format('YYYY-MM-DD HH:mm:ss');
  } else {
    queryParam.startTime = undefined;
    queryParam.endTime = undefined;
  }
};

// 查询
const searchTable = () => {
  queryParam.page = 1;
  loadInspectionPlans();
};

// 重置
const resetTable = () => {
  queryParam.cycleType = undefined;
  queryParam.responsibleUserId = undefined;
  queryParam.startTime = undefined;
  queryParam.endTime = undefined;
  createTimeRange.value = undefined;
  queryParam.page = 1;
  queryParam.size = 10;
  loadInspectionPlans();
};

// 新增巡检计划
const handleAdd = () => {
  modalFormRef.value.openModal();
};

// 查看详情
const handleDetail = (record: any) => {
  modalFormRef.value.openModal(record, true);
  // 设置为只读模式，这里需要在组件中实现
};

// 编辑巡检计划
const handleEdit = (record: any) => {
  modalFormRef.value.openModal(record);
};

// 删除巡检计划
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除巡检计划 "${record.name}" 吗？`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        await InspectionPlanApi.delInspectionPlan_Api(record.id);
        message.success('删除成功');
        loadInspectionPlans();
      } catch (error) {
        message.error('删除失败');
        console.error('删除巡检计划失败', error);
      }
    },
  });
};

// 更改启用状态
const handleStatusChange = async (id: string, status: string) => {
  try {
    await InspectionPlanApi.updateInspectionPlanStatus_Api(id, status);
    message.success(`${status === 'ENABLE' ? '启用' : '禁用'}成功`);
    loadInspectionPlans();
  } catch (error) {
    message.error(`${status === 'ENABLE' ? '启用' : '禁用'}失败`);
    console.error('更新巡检计划状态失败', error);
    loadInspectionPlans(); // 刷新数据，恢复原状态
  }
};

// 巡检计划保存成功回调
const handleSaveSuccess = () => {
  loadInspectionPlans();
};

// 查看所选设备位置
const cameras = ref<any[]>([]);
const checkDeviceLocation = async (cameraIds: any[]) => {
  const res = await getCameraByIds_Api(cameraIds);
  showLocationModal.value = true;
  cameras.value = res;
};

// 导出
const handleExport = async () => {
  const params = {
    cycleType: queryParam.cycleType,
    startTime: queryParam.startTime,
    endTime: queryParam.endTime,
  };
  const res = await InspectionPlanApi.exportInspectionPlan_Api(params);
  downloadFileFromBlob({ source: res, fileName: '巡检计划.xlsx' });
};

// 初始化加载数据
onMounted(() => {
  loadInspectionPlans();
});
</script>

<template>
  <Page>
    <a-card class="table_header_search mb-5" size="small">
      <a-row :gutter="20">
        <a-col :span="5">
          <label>巡检周期：</label>
          <div class="table_header_wrp_cont">
            <a-select
              v-model:value="queryParam.cycleType"
              allow-clear
              placeholder="请选择巡检周期"
              :options="cycleOptions"
              :field-names="{
                label: 'dictLabel',
                value: 'dictValue',
              }"
              style="width: 100%"
            />
          </div>
        </a-col>
        <a-col :span="6">
          <label>创建时间：</label>
          <div class="table_header_wrp_cont">
            <a-range-picker
              v-model:value="createTimeRange"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              :placeholder="['开始时间', '结束时间']"
              style="width: 100%"
              @change="handleTimeRangeChange"
            />
          </div>
        </a-col>
        <a-col :span="8">
          <a-button type="primary" class="searchBtn" @click="searchTable">
            查询
          </a-button>
          <a-button class="refBtn" @click="resetTable">重置</a-button>
        </a-col>
      </a-row>
    </a-card>

    <a-card size="small">
      <div class="table_action_btn_wrp">
        <a-button class="addBtn" type="primary" @click="handleAdd"
          >新建</a-button
        >
        <a-button class="addBtn" @click="handleExport"> 导出 </a-button>
      </div>

      <tableComp
        :loading="loading"
        :columns="columns"
        :data-source="tableData.data"
        @change="success"
      >
        <!-- 计划名称 -->
        <template #name="{ record }">
          <a-tooltip
            v-if="record.name && record.name.length > 20"
            :title="record.name"
          >
            <span>{{ record.name.substring(0, 20) }}...</span>
          </a-tooltip>
          <span v-else>{{ record.name || '-' }}</span>
        </template>
        <!-- 设备 -->
        <template #cameraIds="{ record }">
          <a-button type="link" @click="checkDeviceLocation(record.cameraIds)"
            >查看</a-button
          >
        </template>
        <!-- 启用禁用 -->
        <template #status="{ record }">
          <a-switch
            v-model:checked="record.status"
            checkedValue="ENABLE"
            unCheckedValue="DISABLE"
            @change="handleStatusChange(record.id, record.status)"
          />
        </template>
        <!-- 操作列 -->
        <template #operation="{ record }">
          <a-button type="link" @click="handleDetail(record)">详情</a-button>
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-popconfirm
            title="巡检计划删除后不可恢复，请确认后继续?"
            @confirm="handleDelete(record)"
          >
            <a-button type="link" danger :disabled="record.status === 'ENABLE'"
              >删除</a-button
            >
          </a-popconfirm>
        </template>
      </tableComp>
    </a-card>

    <!-- 巡检计划弹窗 -->
    <InspectionPlanModal ref="modalFormRef" @success="handleSaveSuccess" />

    <!-- 地图定位弹窗 -->
    <LocationMapModal v-model:open="showLocationModal" :cameras="cameras" />
  </Page>
</template>

<style lang="scss" scoped></style>
