package com.bdyl.line.web.utils;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.bdyl.line.web.model.request.inspection.InspectionPlanRequest;

/**
 * 巡检计划验证工具类
 *
 * <AUTHOR>
 * @since 1.0
 */
public class InspectionPlanValidator {

    /**
     * 验证巡检计划配置
     *
     * @param request 巡检计划请求
     * @return 验证错误信息列表，空列表表示验证通过
     */
    public static List<String> validate(InspectionPlanRequest request) {
        List<String> errors = new ArrayList<>();

        if (request == null) {
            errors.add("巡检计划请求不能为空");
            return errors;
        }

        String cycleType = request.getCycleType();
        if (cycleType == null) {
            errors.add("巡检周期类型不能为空");
            return errors;
        }

        switch (cycleType) {
            case "HOUR":
                // 小时类型使用时间段列表
                validateHourlyPlan(request, errors);
                break;
            case "DAY":
                // 天类型使用开始时间和结束时间
                validateDailyPlan(request, errors);
                break;
            case "WEEK":
                validateWeeklyPlan(request, errors);
                break;
            case "MONTH":
                validateMonthlyPlan(request, errors);
                break;
            default:
                errors.add("不支持的巡检周期类型: " + cycleType);
                break;
        }

        return errors;
    }

    /**
     * 验证小时类型计划（时间段列表配置）
     */
    private static void validateHourlyPlan(InspectionPlanRequest request, List<String> errors) {
        if (CollectionUtils.isEmpty(request.getTimeSlots())) {
            errors.add("小时类型巡检计划必须配置时间段");
            return;
        }

        if (!TimeSlotUtils.isValid(request.getTimeSlots())) {
            errors.add("时间段配置无效");
        }

        // 小时类型也需要验证周期值
        if (request.getCycleValue() == null || request.getCycleValue() <= 0) {
            errors.add("小时类型巡检计划必须配置有效的周期值");
        }
    }

    /**
     * 验证天类型计划（开始时间和结束时间）
     */
    private static void validateDailyPlan(InspectionPlanRequest request, List<String> errors) {
        if (request.getStartTime() == null) {
            errors.add("天类型巡检计划必须配置开始时间");
        }

        if (request.getEndTime() == null) {
            errors.add("天类型巡检计划必须配置结束时间");
        }

        // 可以支持跨天的时间段，所以这里不强制要求开始时间小于结束时间
        // 时间配置验证通过
    }

    /**
     * 验证周类型计划（间隔周数 + 星期几 + 时间）
     */
    private static void validateWeeklyPlan(InspectionPlanRequest request, List<String> errors) {
        if (request.getCycleValue() == null || request.getCycleValue() <= 0) {
            errors.add("周类型巡检计划必须配置有效的间隔周数");
        }

        if (request.getDayValue() == null || request.getDayValue() < 1 || request.getDayValue() > 7) {
            errors.add("周类型巡检计划必须配置有效的星期几(1-7)");
        }

        if (request.getStartTime() == null) {
            errors.add("周类型巡检计划必须配置开始时间");
        }

        if (request.getEndTime() == null) {
            errors.add("周类型巡检计划必须配置结束时间");
        }
    }

    /**
     * 验证月类型计划（间隔月数 + 每月几号 + 时间）
     */
    private static void validateMonthlyPlan(InspectionPlanRequest request, List<String> errors) {
        if (request.getCycleValue() == null || request.getCycleValue() <= 0) {
            errors.add("月类型巡检计划必须配置有效的间隔月数");
        }

        if (request.getDayValue() == null || request.getDayValue() < 1 || request.getDayValue() > 31) {
            errors.add("月类型巡检计划必须配置有效的每月几号(1-31)");
        }

        if (request.getStartTime() == null) {
            errors.add("月类型巡检计划必须配置开始时间");
        }

        if (request.getEndTime() == null) {
            errors.add("月类型巡检计划必须配置结束时间");
        }
    }

    /**
     * 获取计划配置的描述信息
     *
     * @param request 巡检计划请求
     * @return 配置描述
     */
    public static String getConfigDescription(InspectionPlanRequest request) {
        if (request == null || request.getCycleType() == null) {
            return "无效配置";
        }

        String cycleType = request.getCycleType();
        String description = "未知配置";

        if ("HOUR".equals(cycleType)) {
            description = getHourlyDescription(request);
        } else if ("DAY".equals(cycleType)) {
            description = String.format("每天 %s-%s", request.getStartTime(), request.getEndTime());
        } else if ("WEEK".equals(cycleType)) {
            description = String.format("每%d周的星期%d %s-%s", request.getCycleValue(), request.getDayValue(),
                request.getStartTime(), request.getEndTime());
        } else if ("MONTH".equals(cycleType)) {
            description = String.format("每%d月的%d号 %s-%s", request.getCycleValue(), request.getDayValue(),
                request.getStartTime(), request.getEndTime());
        }

        return description;
    }

    /**
     * 获取小时级计划描述
     */
    private static String getHourlyDescription(InspectionPlanRequest request) {
        if (request.getTimeSlots() != null && !request.getTimeSlots().isEmpty()) {
            return "小时级 " + request.getTimeSlots().size() + "个时间段";
        } else {
            return "小时级 无时间段配置";
        }
    }
}
