package com.bdyl.line.web.model.response.dict;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/6/5 17:36
 * @ClassName DictData
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DictData {
    /**
     * 字典值
     */
    private String dictValue;

    /**
     * 字典标签
     */
    private String dictLabel;

    /**
     * 字典描述
     */
    private String description;

    /**
     * 排序号
     */
    private Integer order;
}
