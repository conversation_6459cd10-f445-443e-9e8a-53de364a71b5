package com.bdyl.line.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 核实状态枚举
 *
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@AllArgsConstructor
public enum VerifyStatusEnum {

    /**
     * 未核实
     */
    UNVERIFIED("UNVERIFIED", "未核实", "报警信息尚未核实"),
    /**
     * 真实
     */
    REAL("REAL", "真实", "报警信息属实"),
    /**
     * 误报
     */
    FALSE_ALARM("FALSE_ALARM", "误报", "报警信息为误报");

    /**
     * value
     */
    private final String value;
    /**
     * 名称
     */
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据枚举值获取枚举类
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static VerifyStatusEnum fromValue(String value) {
        for (VerifyStatusEnum type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown type: " + value);
    }

}
