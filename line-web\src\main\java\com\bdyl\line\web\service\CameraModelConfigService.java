package com.bdyl.line.web.service;

import java.util.List;

import com.bdyl.line.web.entity.CameraEntity;
import com.bdyl.line.web.model.request.CameraModelConfigRequest;
import com.bdyl.line.web.model.response.camera.CameraModelConfigResponse;

/**
 * 摄像头模型配置服务
 */
public interface CameraModelConfigService {
    /**
     * 创建摄像头模型配置
     *
     * @param cameraEntity 摄像头实体
     */
    void createBatch(CameraEntity cameraEntity);

    /**
     * 同步配置（新增或删除部分）
     *
     * @param cameraEntity 摄像头实体
     */
    void syncModelConfig(CameraEntity cameraEntity);

    /**
     * 画线
     *
     * @param request 画线request
     */
    void draw(CameraModelConfigRequest request);

    /**
     * 获取摄像头的模型配置列表
     *
     * @param cameraId 摄像头ID
     * @return 摄像头模型配置列表
     */

    List<CameraModelConfigResponse> listByCameraId(Long cameraId);

    /**
     * 更新摄像头模型配置的置信度
     *
     * @param request 摄像头模型配置请求
     */
    void updateConfidence(CameraModelConfigRequest request);

    /**
     * 获取某个摄像头某个模型的配置详情
     *
     * @param cameraId 摄像头ID
     * @param modelCode 摄像头编码
     * @return 配置详情
     */
    CameraModelConfigResponse getDetail(Long cameraId, String modelCode);
}
