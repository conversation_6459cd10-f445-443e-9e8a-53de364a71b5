<script setup lang="ts">
import { h, onMounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { message } from 'ant-design-vue';

import { getBusinessAlarmList_Api } from '#/api';
import DictTag from '#/components/DictTag/index.vue';
import tableComp from '#/components/TableComp/table.vue';
import { useDictStore } from '#/store/dict';
import { formatDate } from '@vben/utils';

import Form from './components/form.vue';

const { getDictOptions } = useDictStore();

const alertTypeOptions = getDictOptions('BizAlertTypeEnum').map((item: any) => {
  return { label: item.description, value: item.dictValue };
});

// 查询条件
const queryParam = reactive<any>({
  type: undefined, // 报警类型
  region: undefined, // 区域
  sceneName: undefined, // 场景
  cameraName: undefined, // 摄像头名称
  verifyStatus: undefined, // 核实状态
  createTime: [], // 报警时间
  page: 1,
  size: 10,
});

const loading = ref(false);
const formModalRef = ref();

// 表格数据源
const tableData = reactive<any>({
  data: {},
});

// 表格相关配置
const columns = [
  {
    title: '报警类型',
    dataIndex: 'type',
    customRender: ({ record }: { record: any }) => {
      return (
        alertTypeOptions.find((item: any) => item.value === record.type)
          .label || '-'
      );
    },
  },
  { title: '摄像头名称', dataIndex: 'cameraName' },
  { title: '区域', dataIndex: 'region' },
  { title: '地址', dataIndex: 'address' },
  {
    title: '场景',
    dataIndex: 'sceneNames',
    customRender: ({ record }: { record: any }) => {
      return (record.sceneNames || []).join(',');
    },
  },
  {
    title: '核实状态',
    dataIndex: 'verifyStatus',
    customRender: ({ record }: { record: any }) => {
      return h(DictTag, {
        dictName: 'VerifyStatusEnum',
        dictValue: record.verifyStatus,
      });
    },
  },
  {
    title: '报警时间',
    dataIndex: 'createTime',
    width: '180px',
    customRender: ({ record }: { record: any }) => {
      return formatDate(record.createTime, 'YYYY-MM-DD HH:mm:ss');
    },
  },
  { title: '操作', dataIndex: 'operation', width: '150px' },
];

// 加载业务告警列表
const loadAlarmList = async () => {
  loading.value = true;
  try {
    queryParam.startTime = queryParam.createTime
      ? queryParam.createTime[0]?.format('YYYY-MM-DD HH:mm:ss')
      : undefined;
    queryParam.endTime = queryParam.createTime
      ? queryParam.createTime[1]?.format('YYYY-MM-DD HH:mm:ss')
      : undefined;

    const res = await getBusinessAlarmList_Api(queryParam);

    // 更新表格数据源
    const temp = {
      data: res.data || [],
      page: res.page || 1,
      size: res.size || 10,
      total: res.total || 0,
    };
    tableData.data = temp;
  } catch (error) {
    console.error('获取业务告警列表失败', error);
    message.error('获取业务告警列表失败');
  } finally {
    loading.value = false;
  }
};

// 分页变化
const handleTableChange = (data: any) => {
  queryParam.page = data.pi;
  queryParam.size = data.ps;
  loadAlarmList();
};

// 查询
const searchTable = () => {
  queryParam.page = 1;
  loadAlarmList();
};

// 重置
const resetTable = () => {
  queryParam.type = undefined;
  queryParam.region = undefined;
  queryParam.sceneName = undefined;
  queryParam.cameraName = undefined;
  queryParam.verifyStatus = undefined;
  queryParam.createTime = [];
  queryParam.page = 1;
  loadAlarmList();
};

// 核实告警
const handleVerify = (record: any) => {
  formModalRef.value?.open({ type: 'toCheck', data: record });
};

// 查看告警
const handleView = (record: any) => {
  formModalRef.value?.open({ type: 'toView', data: record });
};

// 表格操作成功回调
const success = () => {
  loadAlarmList();
};

onMounted(() => {
  loadAlarmList();
});
</script>

<template>
  <Page>
    <a-card class="table_header_search mb-5">
      <a-row :gutter="20">
        <a-col :span="6">
          <label>报警类型：</label>
          <div class="table_header_wrp_cont">
            <a-select
              v-model:value="queryParam.type"
              allow-clear
              placeholder="请选择报警类型"
              style="width: 100%"
            >
              <a-select-option
                v-for="item in alertTypeOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
        </a-col>
        <a-col :span="6">
          <label>区域：</label>
          <div class="table_header_wrp_cont">
            <a-input
              v-model:value="queryParam.region"
              allow-clear
              placeholder="请输入区域"
            />
          </div>
        </a-col>
        <a-col :span="6">
          <label>场景：</label>
          <div class="table_header_wrp_cont">
            <a-input
              v-model:value="queryParam.sceneName"
              allow-clear
              placeholder="请输入场景"
            />
          </div>
        </a-col>
        <a-col :span="6">
          <label>核实状态：</label>
          <div class="table_header_wrp_cont">
            <a-select
              v-model:value="queryParam.verifyStatus"
              allow-clear
              placeholder="请选择核实状态"
              style="width: 100%"
            >
              <a-select-option
                v-for="item in getDictOptions('VerifyStatusEnum')"
                :key="item.dictValue"
                :value="item.dictValue"
              >
                {{ item.dictLabel }}
              </a-select-option>
            </a-select>
          </div>
        </a-col>
        <a-col :span="6">
          <label>报警时间：</label>
          <div class="table_header_wrp_cont">
            <a-range-picker
              v-model:value="queryParam.createTime"
              :placeholder="['开始时间', '结束时间']"
              format="YYYY-MM-DD HH:mm:ss"
              show-time
              style="width: 100%"
            />
          </div>
        </a-col>
        <a-col :span="8">
          <label>摄像头名称：</label>
          <div class="table_header_wrp_cont">
            <a-input
              v-model:value="queryParam.cameraName"
              allow-clear
              placeholder="请输入摄像头名称"
            />
          </div>
        </a-col>

        <a-col :span="4">
          <a-space>
            <a-button type="primary" class="searchBtn" @click="searchTable">
              查询
            </a-button>
            <a-button class="refBtn" @click="resetTable">重置</a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <a-card size="small">
      <table-Comp
        :columns="columns"
        :data-source="tableData.data"
        :loading="loading"
        :scroll="{ x: 1200 }"
        @is-loading-fuc="(e) => (loading = e)"
        @success="handleTableChange"
      >
        <template #operation="{ record }">
          <a-space>
            <a-button
              v-if="record.verifyStatus === 'UNVERIFIED'"
              type="link"
              size="small"
              @click="handleVerify(record)"
            >
              核实
            </a-button>
            <a-button
              v-if="record.verifyStatus !== 'UNVERIFIED'"
              type="link"
              size="small"
              @click="handleView(record)"
            >
              查看
            </a-button>
          </a-space>
        </template>
      </table-Comp>
    </a-card>

    <!-- 表单弹窗 -->
    <Form ref="formModalRef" @success="success" />
  </Page>
</template>

<style lang="scss" scoped>
.wrap_con {
  display: flex;
  height: 100%;
  gap: 10px;
}

.left_cont {
  width: 260px;
  flex-shrink: 0;
}

.right_cont {
  flex: 1;
  min-width: 0;
}

.table_header_search {
  .table_header_wrp_cont {
    margin-top: 5px;
  }

  label {
    font-weight: 500;
    color: #333;
  }
}

.table_action_btn_wrp {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

.searchBtn {
  background: #1890ff;
  border-color: #1890ff;
}

.refBtn {
  background: #fff;
  border-color: #d9d9d9;
  color: #666;
}

.addBtn {
  background: #52c41a;
  border-color: #52c41a;
  color: #fff;
}
</style>
