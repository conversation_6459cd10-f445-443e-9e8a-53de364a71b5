<script setup lang="ts">
import { h, onMounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { message } from 'ant-design-vue';

import {
  getBusinessAlarmList_Api,
  getSceneAllList_Api,
  getRegion_Api,
  exportAlarm_Api,
} from '#/api';
import DictTag from '#/components/DictTag/index.vue';
import tableComp from '#/components/TableComp/table.vue';
import { useDictStore } from '#/store/dict';
import { downloadFileFromBlob, formatDate } from '@vben/utils';

import Form from './components/form.vue';

const { getDictOptions } = useDictStore();

const alertTypeOptions = getDictOptions('BizAlertTypeEnum').map((item: any) => {
  return { label: item.description, value: item.dictValue };
});

// 查询条件
const queryParam = reactive<any>({
  type: undefined, // 报警类型
  region: undefined, // 区域
  sceneId: undefined, // 场景
  cameraName: undefined, // 摄像头名称
  verifyStatus: undefined, // 核实状态
  createTime: [], // 报警时间
  page: 1,
  size: 10,
});

const loading = ref(false);
const formModalRef = ref();

// 表格数据源
const tableData = reactive<any>({
  data: {},
});

// 表格相关配置
const columns = [
  {
    title: '报警类型',
    dataIndex: 'type',
    customRender: ({ record }: { record: any }) => {
      return (
        alertTypeOptions.find((item: any) => item.value === record.type)
          .label || '-'
      );
    },
  },
  { title: '摄像头名称', dataIndex: 'cameraName' },
  { title: '区域', dataIndex: 'region' },
  { title: '地址', dataIndex: 'address' },
  {
    title: '场景',
    dataIndex: 'sceneNames',
    customRender: ({ record }: { record: any }) => {
      return (record.sceneNames || []).join(',');
    },
  },
  {
    title: '核实状态',
    dataIndex: 'verifyStatus',
    customRender: ({ record }: { record: any }) => {
      return h(DictTag, {
        dictName: 'VerifyStatusEnum',
        dictValue: record.verifyStatus,
      });
    },
  },
  {
    title: '报警时间',
    dataIndex: 'createTime',
    width: '180px',
    customRender: ({ record }: { record: any }) => {
      return formatDate(record.createTime, 'YYYY-MM-DD HH:mm:ss');
    },
  },
  { title: '操作', dataIndex: 'operation', width: '150px' },
];

// 加载业务告警列表
const loadAlarmList = async () => {
  loading.value = true;
  try {
    queryParam.startTime = queryParam.createTime
      ? queryParam.createTime[0]?.format('YYYY-MM-DD HH:mm:ss')
      : undefined;
    queryParam.endTime = queryParam.createTime
      ? queryParam.createTime[1]?.format('YYYY-MM-DD HH:mm:ss')
      : undefined;
    const params = {
      ...queryParam,
      region:
        queryParam.region && queryParam.region.length
          ? queryParam.region[queryParam.region.length - 1]
          : undefined,
    };

    const res = await getBusinessAlarmList_Api(params);

    // 更新表格数据源
    const temp = {
      data: res.data || [],
      page: res.page || 1,
      size: res.size || 10,
      total: res.total || 0,
    };
    tableData.data = temp;
  } catch (error) {
    console.error('获取业务告警列表失败', error);
    message.error('获取业务告警列表失败');
  } finally {
    loading.value = false;
  }
};

// 加载场景数据
const sceneOptions = ref([]);
const loadSceneList = async () => {
  const res = await getSceneAllList_Api();
  sceneOptions.value = (res || []).map((item: any) => {
    return {
      label: item.name,
      value: item.id,
    };
  });
};

// 分页变化
const handleTableChange = (data: any) => {
  queryParam.page = data.pi;
  queryParam.size = data.ps;
  loadAlarmList();
};

// 查询
const searchTable = () => {
  queryParam.page = 1;
  loadAlarmList();
};

// 重置
const resetTable = () => {
  queryParam.type = undefined;
  queryParam.region = undefined;
  queryParam.sceneId = undefined;
  queryParam.cameraName = undefined;
  queryParam.verifyStatus = undefined;
  queryParam.createTime = [];
  queryParam.page = 1;
  loadAlarmList();
};

// 核实告警
const handleVerify = (record: any) => {
  formModalRef.value?.open({ type: 'toCheck', data: record });
};

// 查看告警
const handleView = (record: any) => {
  formModalRef.value?.open({ type: 'toView', data: record });
};

// 表格操作成功回调
const success = () => {
  loadAlarmList();
};

// 动态加载区域数据
const loadRegionData = async (selectedOptions: any[]) => {
  const targetOption = selectedOptions[selectedOptions.length - 1];
  targetOption.loading = true;

  try {
    const params = {
      parentCode: targetOption.code,
    };
    const children = await getRegion_Api(params);

    targetOption.loading = false;
    if (children && children.length > 0) {
      // 为子区域设置isLeaf属性
      const childrenWithLeaf = children.map((item: any) => ({
        ...item,
        isLeaf: false, // 默认都不是叶子节点，加载时再判断
        value: item.code,
        label: item.name,
        code: item.code,
      }));
      targetOption.children = childrenWithLeaf;
    } else {
      targetOption.isLeaf = true;
    }
  } catch (error) {
    targetOption.loading = false;
    console.error('加载区域数据失败', error);
    message.error('加载区域数据失败');
  }
};

// 加载初始区域数据
const regionLoading = ref(false);
const areaOptions = ref([]);
const loadInitialRegions = async () => {
  try {
    regionLoading.value = true;
    const params = { parentCode: undefined }; // parentCode为空查询顶级
    const regions = await getRegion_Api(params);

    // 转换区域数据格式，适配cascader组件
    return regions.map((item: any) => ({
      ...item,
      isLeaf: false, // 默认一级区域都不是叶子节点
      value: item.code,
      label: item.name,
      code: item.code,
    }));
  } catch (error) {
    console.error('加载区域数据失败', error);
    message.error('加载区域数据失败');
    return [];
  } finally {
    regionLoading.value = false;
  }
};

// 导出
const handleExport = async () => {
  queryParam.startTime = queryParam.createTime
    ? queryParam.createTime[0]?.format('YYYY-MM-DD HH:mm:ss')
    : undefined;
  queryParam.endTime = queryParam.createTime
    ? queryParam.createTime[1]?.format('YYYY-MM-DD HH:mm:ss')
    : undefined;
  const params = {
    ...queryParam,
    region:
      queryParam.region && queryParam.region.length
        ? queryParam.region[queryParam.region.length - 1]
        : undefined,
  };
  const res = await exportAlarm_Api(params);
  downloadFileFromBlob({ source: res, fileName: '业务告警数据.xlsx' });
};

onMounted(async () => {
  loadAlarmList();
  loadSceneList();
  // 加载初始区域数据
  areaOptions.value = await loadInitialRegions();
});
</script>

<template>
  <Page>
    <a-card class="table_header_search mb-5">
      <a-row :gutter="20">
        <a-col :span="6">
          <label>报警类型：</label>
          <div class="table_header_wrp_cont">
            <a-select
              v-model:value="queryParam.type"
              allow-clear
              placeholder="请选择报警类型"
              style="width: 100%"
            >
              <a-select-option
                v-for="item in alertTypeOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
        </a-col>
        <a-col :span="6">
          <label>区域：</label>
          <div class="table_header_wrp_cont">
            <a-cascader
              v-model:value="queryParam.region"
              :options="areaOptions"
              :load-data="loadRegionData"
              placeholder="请选择区域"
              style="width: 100%"
              change-on-select
              :loading="regionLoading"
            />
          </div>
        </a-col>
        <a-col :span="6">
          <label>场景：</label>
          <div class="table_header_wrp_cont">
            <a-select
              v-model:value="queryParam.sceneId"
              style="width: 100%"
              allow-clear
              :options="sceneOptions"
              placeholder="请选择场景"
            />
          </div>
        </a-col>
        <a-col :span="6">
          <label>核实状态：</label>
          <div class="table_header_wrp_cont">
            <a-select
              v-model:value="queryParam.verifyStatus"
              allow-clear
              placeholder="请选择核实状态"
              style="width: 100%"
            >
              <a-select-option
                v-for="item in getDictOptions('VerifyStatusEnum')"
                :key="item.dictValue"
                :value="item.dictValue"
              >
                {{ item.dictLabel }}
              </a-select-option>
            </a-select>
          </div>
        </a-col>
        <a-col :span="6">
          <label>报警时间：</label>
          <div class="table_header_wrp_cont">
            <a-range-picker
              v-model:value="queryParam.createTime"
              :placeholder="['开始时间', '结束时间']"
              format="YYYY-MM-DD HH:mm:ss"
              show-time
              style="width: 100%"
            />
          </div>
        </a-col>
        <a-col :span="8">
          <label>摄像头名称：</label>
          <div class="table_header_wrp_cont">
            <a-input
              v-model:value="queryParam.cameraName"
              allow-clear
              placeholder="请输入摄像头名称"
            />
          </div>
        </a-col>

        <a-col :span="4">
          <a-space>
            <a-button type="primary" class="searchBtn" @click="searchTable">
              查询
            </a-button>
            <a-button class="refBtn" @click="resetTable">重置</a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <a-card size="small">
      <div class="table_action_btn_wrp">
        <a-button class="addBtn" @click="handleExport"> 导出 </a-button>
      </div>
      <table-Comp
        :columns="columns"
        :data-source="tableData.data"
        :loading="loading"
        :scroll="{ x: 1200 }"
        @is-loading-fuc="(e) => (loading = e)"
        @success="handleTableChange"
      >
        <template #operation="{ record }">
          <a-space>
            <a-button
              v-if="record.verifyStatus === 'UNVERIFIED'"
              type="link"
              size="small"
              @click="handleVerify(record)"
            >
              核实
            </a-button>
            <a-button
              v-if="record.verifyStatus !== 'UNVERIFIED'"
              type="link"
              size="small"
              @click="handleView(record)"
            >
              查看
            </a-button>
          </a-space>
        </template>
      </table-Comp>
    </a-card>

    <!-- 表单弹窗 -->
    <Form ref="formModalRef" @success="success" />
  </Page>
</template>

<style lang="scss" scoped>
.wrap_con {
  display: flex;
  height: 100%;
  gap: 10px;
}

.left_cont {
  width: 260px;
  flex-shrink: 0;
}

.right_cont {
  flex: 1;
  min-width: 0;
}

.table_header_search {
  .table_header_wrp_cont {
    margin-top: 5px;
  }

  label {
    font-weight: 500;
    color: #333;
  }
}

.table_action_btn_wrp {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

.searchBtn {
  background: #1890ff;
  border-color: #1890ff;
}

.refBtn {
  background: #fff;
  border-color: #d9d9d9;
  color: #666;
}
</style>
