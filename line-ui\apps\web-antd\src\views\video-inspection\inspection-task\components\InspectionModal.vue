<script setup lang="ts">
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';

import {
  getCameraList_Api,
  submitInspectionTask_Api,
  getCameraInspectionRecordList_Api,
  type recordResult,
} from '#/api/core/inspection-plan';
import { getCameraVideoUrl_Api } from '#/api/core/camera';
import { upload_file } from '#/api/upload';
import EasyPlayer from '#/components/EasyPlayer/index.vue';
import { IconifyIcon } from '@vben/icons';

import html2canvas from 'html2canvas';

const imageUrl = import.meta.env.VITE_GLOB_LOAD_ERROR_URL;

const emit = defineEmits(['success']);

// 弹窗状态
const open = ref(false);
const loading = ref(false);
const mode = ref<'inspection' | 'view'>('inspection');

// 任务信息
const taskInfo = ref<any>({});

// 摄像头列表
const cameras = ref<any[]>([]);
const filteredCameras = ref<any[]>([]);
const searchKeyword = ref('');
const selectedCamera = ref<any>(null);
const selectedCameraIndex = ref(0);

// 视频相关
const videoUrl = ref('');
const cameraRegions = ref<any>([]);
const videoLoading = ref(false);
const playerRef = ref();

// 表单数据
const formData = reactive({
  inspectionResult: undefined as boolean | undefined, // true: 正常, false: 异常
  remarks: '',
});

// 巡检记录数据
const inspectionRecord = ref<any>(null);
const recordLoading = ref(false);

// 表单验证规则
const rules = {
  inspectionResult: [
    { required: true, message: '请选择巡检结果', trigger: 'change' },
  ],
};

const formRef = ref();

// 获取任务下的摄像头列表
const getCameraList = async (taskId: string) => {
  try {
    loading.value = true;
    const res: any = await getCameraList_Api(taskId);
    cameras.value = res || [];
    filteredCameras.value = cameras.value;

    if (cameras.value.length > 0) {
      selectedCamera.value = cameras.value[0];
      selectedCameraIndex.value = 0;
      await loadCameraVideo();
      await loadInspectionRecord(selectedCamera.value);
    }
  } catch (error) {
    console.error('获取摄像头列表失败', error);
    message.error('获取摄像头列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索摄像头
const searchCameras = () => {
  if (!searchKeyword.value.trim()) {
    filteredCameras.value = cameras.value;
  } else {
    filteredCameras.value = cameras.value.filter((camera: any) =>
      camera.cameraName
        .toLowerCase()
        .includes(searchKeyword.value.toLowerCase()),
    );
  }

  // 如果当前选中的摄像头不在过滤结果中，选择第一个
  if (filteredCameras.value.length > 0) {
    const currentCameraInFiltered = filteredCameras.value.find(
      (camera: any) => camera.id === selectedCamera.value?.id,
    );
    if (!currentCameraInFiltered) {
      selectCamera(filteredCameras.value[0], 0);
    }
  }
};

// 上一个摄像头
const prevCamera = () => {
  if (filteredCameras.value.length === 0) return;

  const currentIndex = filteredCameras.value.findIndex(
    (camera: any) => camera.id === selectedCamera.value?.id,
  );
  const prevIndex =
    currentIndex > 0 ? currentIndex - 1 : filteredCameras.value.length - 1;
  selectCamera(filteredCameras.value[prevIndex], prevIndex);
};

// 下一个摄像头
const nextCamera = () => {
  if (filteredCameras.value.length === 0) return;

  const currentIndex = filteredCameras.value.findIndex(
    (camera: any) => camera.id === selectedCamera.value?.id,
  );
  const nextIndex =
    currentIndex < filteredCameras.value.length - 1 ? currentIndex + 1 : 0;
  selectCamera(filteredCameras.value[nextIndex], nextIndex);
};

// 加载摄像头视频
const loadCameraVideo = async () => {
  // 查看记录或已巡检不加载摄像头视频
  if (
    mode.value === 'view' ||
    (mode.value === 'inspection' &&
      selectedCamera.value?.status === 'COMPLETED')
  )
    return;

  if (!selectedCamera.value) return;

  if (!selectedCamera.value.cameraCode) return;

  try {
    videoLoading.value = true;
    const res = await getCameraVideoUrl_Api(selectedCamera.value.cameraCode);

    if (res && res.streamUrls && res.streamUrls.length > 0) {
      const preferredUrl =
        res.streamUrls.find((url: string) => url.endsWith('.flv')) ||
        res.streamUrls.find((url: string) => url.endsWith('.mp4')) ||
        res.streamUrls.find((url: string) => url.endsWith('.ts')) ||
        res.streamUrls.find((url: string) => url.endsWith('.m3u8'));
      videoUrl.value = preferredUrl || res.streamUrls[0];
      cameraRegions.value = res.cameraRegions;
    } else {
      message.warning('该摄像头暂无视频流或视频流已断开');
      videoUrl.value = '';
    }
  } catch (error) {
    console.error('获取视频流失败:', error);
    message.error('获取视频流失败');
    videoUrl.value = '';
  } finally {
    videoLoading.value = false;
  }
};

// 选择摄像头
const selectCamera = async (camera: any, index: number) => {
  selectedCamera.value = camera;
  selectedCameraIndex.value = index;
  await loadCameraVideo();

  // 重置巡检记录数据
  inspectionRecord.value = null;

  // 如果是查看模式，或者是去巡检模式但摄像头状态为COMPLETED，加载巡检记录
  if (
    mode.value === 'view' ||
    (mode.value === 'inspection' && camera.status === 'COMPLETED')
  ) {
    await loadInspectionRecord(camera);
  }
};

// 加载巡检记录
const loadInspectionRecord = async (camera: any) => {
  if (!camera || !camera.id) {
    return;
  }

  try {
    recordLoading.value = true;
    const res = await getCameraInspectionRecordList_Api(camera.id.toString());

    if (res) {
      inspectionRecord.value = res;
    } else {
      inspectionRecord.value = null;
    }
  } catch (error) {
    console.error('获取巡检记录失败:', error);
    message.error('获取巡检记录失败');
    inspectionRecord.value = null;
  } finally {
    recordLoading.value = false;
  }
};

// 获取摄像头状态颜色
const getCameraStatusColor = (camera: any) => {
  if (camera.status === 'COMPLETED') {
    // 已完成巡检，判断巡检结果
    if (inspectionRecord.value?.isNormal) {
      // 摄像头未报警，判断摄像头状态
      return camera.cameraStatus === 'ONLINE' ? '#52c41a' : '#d9d9d9'; // 正常绿色，摄像头离线灰色
    } else {
      return '#ff4d4f'; // 异常红色
    }
  } else {
    // 未完成巡检，只判断摄像头状态
    return camera.cameraStatus === 'ONLINE' ? '#52c41a' : '#d9d9d9'; // 在线绿色，离线灰色
  }
};

// 抓拍并提交
const captureAndSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate();

    if (!selectedCamera.value) {
      message.error('请选择摄像头');
      return;
    }

    loading.value = true;

    // 使用自定义截屏功能（包含画线数据）
    const blob = await captureVideoWithLines();

    if (!blob) {
      message.error('抓拍失败，请重试');
      return;
    }

    // 创建文件对象
    const file = new File([blob], `inspection_${Date.now()}.jpg`, {
      type: 'image/jpeg',
    });

    // 上传图片
    const uploadResult = await new Promise<string>((resolve, reject) => {
      upload_file({
        file,
        onSuccess: (data: any) => {
          resolve(data.url || data);
        },
        onError: (error: Error) => {
          reject(error);
        },
      });
    });

    // 提交巡检结果
    const submitData: recordResult = {
      taskCameraId: selectedCamera.value.id,
      isNormal: formData.inspectionResult!,
      remarks: formData.remarks || '',
      image: uploadResult,
    };

    await submitInspectionTask_Api(submitData);

    await getCameraList(taskInfo.value.id);
    message.success('巡检提交成功');
    // closeModal();
    emit('success');
  } catch (error) {
    console.error('提交失败:', error);
    message.error('提交失败');
  } finally {
    loading.value = false;
  }
};

// 抓拍视频画面（包含画线数据）
const captureVideoWithLines = async (): Promise<Blob | null> => {
  try {
    // 如果没有videoUrl，截取.video-box的图片
    if (!videoUrl.value) {
      return await captureVideoBox();
    }

    if (!playerRef.value) {
      console.error('播放器未初始化');
      return null;
    }

    // 获取视频元素
    const videoElement = playerRef.value?.$el?.querySelector('video');

    if (!videoElement) {
      console.error('未找到视频元素或Canvas元素');
      return null;
    }

    // 创建临时canvas用于合成
    const tempCanvas = document.createElement('canvas');
    const ctx = tempCanvas.getContext('2d');

    if (!ctx) {
      console.error('无法获取Canvas上下文');
      return null;
    }

    // 设置canvas尺寸
    const videoWidth = (videoElement as HTMLVideoElement).videoWidth || 1920;
    const videoHeight = (videoElement as HTMLVideoElement).videoHeight || 1080;

    tempCanvas.width = videoWidth;
    tempCanvas.height = videoHeight;

    // 绘制视频帧到canvas
    ctx.drawImage(
      videoElement as HTMLVideoElement,
      0,
      0,
      videoWidth,
      videoHeight,
    );

    // 获取Fabric.js canvas的内容并叠加
    if (playerRef.value.canvas) {
      const fabricCanvas = playerRef.value.canvas;
      const fabricDataURL = fabricCanvas.toDataURL({
        format: 'png',
        quality: 1,
        multiplier: 1,
      });

      // 创建图片对象加载Fabric canvas内容
      const fabricImage = new Image();

      return new Promise<Blob | null>((resolve) => {
        fabricImage.onload = () => {
          try {
            // 将Fabric canvas内容绘制到临时canvas上
            ctx.drawImage(fabricImage, 0, 0, videoWidth, videoHeight);

            // 转换为blob
            tempCanvas.toBlob(
              (blob) => {
                resolve(blob);
              },
              'image/jpeg',
              0.9,
            );
          } catch (error) {
            console.error('合成截屏失败:', error);
            resolve(null);
          }
        };

        fabricImage.onerror = () => {
          console.error('加载Fabric canvas图像失败');
          // 如果画线加载失败，至少返回视频截图
          tempCanvas.toBlob(
            (blob) => {
              resolve(blob);
            },
            'image/jpeg',
            0.9,
          );
        };

        fabricImage.src = fabricDataURL;
      });
    } else {
      // 如果没有画线数据，直接返回视频截图
      return new Promise<Blob | null>((resolve) => {
        tempCanvas.toBlob(
          (blob) => {
            resolve(blob);
          },
          'image/jpeg',
          0.9,
        );
      });
    }
  } catch (error) {
    console.error('抓拍失败:', error);
    return null;
  }
};

// 截取.video-box的图片
const captureVideoBox = async (): Promise<Blob | null> => {
  try {
    const videoBox = document.querySelector('.video-box');
    if (!videoBox) {
      console.error('未找到.video-box元素');
      return null;
    }

    // 尝试使用html2canvas
    try {
      const canvas = await html2canvas(videoBox as HTMLElement, {
        backgroundColor: null,
        scale: 1,
        logging: false,
        useCORS: true,
        allowTaint: true,
        foreignObjectRendering: false,
        removeContainer: true,
        imageTimeout: 15000,
        width: (videoBox as HTMLElement).offsetWidth,
        height: (videoBox as HTMLElement).offsetHeight,
      });

      return new Promise<Blob | null>((resolve) => {
        canvas.toBlob(
          (blob: Blob | null) => {
            if (blob) {
              console.log('html2canvas截图成功，大小:', blob.size);
              resolve(blob);
            } else {
              console.error('html2canvas生成blob失败');
              resolve(null);
            }
          },
          'image/jpeg',
          0.9,
        );
      });
    } catch (html2canvasError) {
      console.warn('html2canvas失败', html2canvasError);
      return null;
    }
  } catch (error) {
    console.error('截取video-box失败:', error);
    return null;
  }
};

// 打开弹窗
const openModal = async (task: any, modalMode: 'inspection' | 'view') => {
  open.value = true;
  mode.value = modalMode;
  taskInfo.value = task;

  // 重置表单
  formData.inspectionResult = undefined;
  formData.remarks = '';
  await getCameraList(task.id);
};

// 关闭弹窗
const closeModal = () => {
  open.value = false;
  taskInfo.value = {};
  cameras.value = [];
  filteredCameras.value = [];
  searchKeyword.value = '';
  selectedCamera.value = null;
  selectedCameraIndex.value = 0;
  videoUrl.value = '';
  formData.inspectionResult = undefined;
  formData.remarks = '';
  inspectionRecord.value = null;
  recordLoading.value = false;
};

// 处理图片加载错误
const handleImageError = (event: Event) => {
  console.error('巡检图片加载失败:', event);
  const target = event.target as HTMLImageElement;
  target.style.display = 'none';
  // 可以在这里添加默认图片或错误提示
};

// 测试截图功能
const testCapture = async () => {
  try {
    console.log('开始测试截图功能...');
    const blob = await captureVideoBox();
    if (blob) {
      console.log('截图测试成功，blob大小:', blob.size);
      return true;
    } else {
      console.error('截图测试失败：无法生成blob');
      return false;
    }
  } catch (error) {
    console.error('截图测试失败:', error);
    return false;
  }
};

// 暴露方法
defineExpose({
  openModal,
  testCapture,
});
</script>

<template>
  <a-modal
    v-model:open="open"
    :title="mode === 'inspection' ? '去巡检' : '查看记录'"
    width="1000px"
    :mask-closable="false"
    @cancel="closeModal"
  >
    <a-spin :spinning="loading">
      <div class="inspection-content">
        <div class="left-panel">
          <!-- 搜索框 -->
          <div class="search-box">
            <a-input
              v-model:value="searchKeyword"
              placeholder="搜索设备名称"
              allow-clear
              @input="searchCameras"
            >
              <template #prefix>
                <IconifyIcon icon="lucide:search" />
              </template>
            </a-input>
          </div>

          <div class="camera-list">
            <div
              v-for="(camera, index) in filteredCameras"
              :key="camera.id"
              class="camera-item"
              :class="{ active: selectedCamera?.id === camera.id }"
              @click="selectCamera(camera, index)"
            >
              <div
                class="status-indicator"
                :style="{ backgroundColor: getCameraStatusColor(camera) }"
              ></div>
              <div class="camera-info">
                <div class="camera-name">{{ camera.cameraName }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="right-panel">
          <!-- 摄像头切换控制 -->
          <div class="camera-controls">
            <a-button
              type="text"
              :disabled="filteredCameras.length <= 1"
              @click="prevCamera"
            >
              <IconifyIcon icon="lucide:chevron-left" />
            </a-button>
            <span class="camera-name-display">
              {{ selectedCamera?.cameraName || '暂无设备' }}
            </span>
            <a-button
              type="text"
              :disabled="filteredCameras.length <= 1"
              @click="nextCamera"
            >
              <IconifyIcon icon="lucide:chevron-right" />
            </a-button>
          </div>

          <!-- 巡检表单 - 只在去巡检模式且摄像头状态为PENDING时显示 -->
          <div
            v-if="mode === 'inspection' && selectedCamera?.status === 'PENDING'"
            class="inspection-form"
          >
            <a-form
              ref="formRef"
              :model="formData"
              :rules="rules"
              layout="inline"
            >
              <a-form-item label="巡检结果" name="inspectionResult">
                <a-radio-group v-model:value="formData.inspectionResult">
                  <a-radio :value="true">正常</a-radio>
                  <a-radio :value="false">异常</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="巡检备注">
                <a-input
                  v-model:value="formData.remarks"
                  placeholder="请输入巡检备注"
                  style="width: 200px"
                />
              </a-form-item>
              <a-form-item>
                <a-button
                  type="primary"
                  :loading="loading"
                  @click="captureAndSubmit"
                >
                  抓拍并提交
                </a-button>
              </a-form-item>
            </a-form>
          </div>

          <!-- 巡检记录信息 - 在查看模式或去巡检模式且摄像头状态为COMPLETED时显示 -->
          <div
            v-if="
              mode === 'view' ||
              (mode === 'inspection' && selectedCamera?.status === 'COMPLETED')
            "
            class="record-info"
          >
            <a-spin :spinning="recordLoading">
              <a-descriptions v-if="inspectionRecord" :column="2" size="small">
                <a-descriptions-item label="巡检结果">
                  <a-tag :color="inspectionRecord.isNormal ? 'green' : 'red'">
                    {{ inspectionRecord.isNormal ? '正常' : '异常' }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="巡检备注">
                  {{ inspectionRecord.remarks || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="巡检时间">
                  {{ inspectionRecord.inspectionTime || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="巡检人员">
                  {{ inspectionRecord.inspectorName || '-' }}
                </a-descriptions-item>
              </a-descriptions>
              <a-empty v-else description="暂无巡检记录" />
            </a-spin>
          </div>

          <!-- 视频播放器或巡检图片 -->
          <div class="video-box">
            <!-- 如果是已巡检状态且有巡检图片，显示图片 -->
            <div
              v-if="
                (mode === 'view' ||
                  (mode === 'inspection' &&
                    selectedCamera?.status === 'COMPLETED')) &&
                inspectionRecord &&
                inspectionRecord?.inspectionImage
              "
              class="inspection-image-container"
            >
              <img
                :src="imageUrl + inspectionRecord.inspectionImage"
                alt="巡检图片"
                class="inspection-image"
                @error="handleImageError"
              />
            </div>
            <!-- 否则显示视频播放器 -->
            <template v-else>
              <EasyPlayer
                v-if="videoUrl"
                id="video-inspection"
                ref="playerRef"
                :is-live="true"
                :video-url="videoUrl"
                :points="cameraRegions"
                :showLocation="false"
              />
              <a-spin v-else :spinning="videoLoading">
                <a-empty
                  class="no-video"
                  description="摄像头已掉线或者网络连接失败"
                />
              </a-spin>
            </template>
          </div>
        </div>
      </div>
    </a-spin>

    <template #footer>
      <a-button @click="closeModal">关闭</a-button>
    </template>
  </a-modal>
</template>

<style lang="scss" scoped>
.inspection-content {
  display: flex;
  height: 600px;
  gap: 16px;

  .left-panel {
    width: 200px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;

    .panel-title {
      padding: 12px 16px;
      background: #fafafa;
      border-bottom: 1px solid #d9d9d9;
      font-weight: 500;
    }

    .search-box {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
    }

    .camera-list {
      height: calc(100% - 45px - 60px); // 减去标题和搜索框的高度
      overflow-y: auto;

      .camera-item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background-color: #f5f5f5;
        }

        &.active {
          background-color: #e6f7ff;
          border-color: #1890ff;
        }

        .camera-info {
          // flex: 1;
          margin-left: 8px;

          .camera-name {
            font-size: 14px;
            font-weight: 500;
          }

          .camera-status {
            font-size: 12px;
          }

          .inspection-status {
            margin-top: 4px;
          }
        }

        .status-indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;
        }
      }
    }
  }

  .right-panel {
    flex: 1;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    display: flex;
    flex-direction: column;

    .panel-title {
      padding: 12px 16px;
      background: #fafafa;
      border-bottom: 1px solid #d9d9d9;
      font-weight: 500;
      flex-shrink: 0;
    }

    .camera-controls {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      flex-shrink: 0;
      gap: 12px;

      .camera-name-display {
        font-weight: 500;
        color: #1890ff;
        min-width: 120px;
        text-align: center;
      }
    }

    .inspection-form {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      flex-shrink: 0;
    }

    .record-info {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      flex-shrink: 0;
    }

    .video-box {
      padding: 16px;
      flex: 1;

      .no-video {
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fafafa;
        border-radius: 6px;
      }
    }
  }
}
</style>
