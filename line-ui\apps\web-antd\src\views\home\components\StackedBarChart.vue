<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { useDictStore } from '#/store/dict';

const dictStore = useDictStore();

interface Props {
  data: Array<{
    month: number;
    typeCounts: Array<{
      type: string;
      count: number;
    }>;
  }>;
}

const props = defineProps<Props>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

const renderChart = () => {
  if (!props.data || props.data.length === 0) return;

  // 获取所有类型
  const allTypes = new Set<string>();
  props.data.forEach((item) => {
    item.typeCounts.forEach((typeCount) => {
      allTypes.add(typeCount.type);
    });
  });

  const types = Array.from(allTypes);

  const legendTypes = types.map((item) =>
    dictStore.getDictDescription('BizAlertTypeEnum', item),
  );

  const months = props.data.map((item) => `${item.month}月`);

  // 构建系列数据
  const series = types.map((type, index) => {
    const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'];
    return {
      name: dictStore.getDictDescription('BizAlertTypeEnum', type),
      type: 'bar',
      stack: 'total',
      data: props.data.map((item) => {
        const typeData = item.typeCounts.find((tc) => tc.type === type);
        return typeData ? typeData.count : 0;
      }),
      itemStyle: {
        color: colors[index % colors.length],
      },
    };
  });

  renderEcharts({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: legendTypes,
      bottom: '0%',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '20%',
      top: '5%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: months,
    },
    yAxis: {
      type: 'value',
    },
    series,
  });
};

watch(() => props.data, renderChart, { deep: true });

onMounted(() => {
  renderChart();
});
</script>

<template>
  <EchartsUI style="height: 100%" ref="chartRef" />
</template>
