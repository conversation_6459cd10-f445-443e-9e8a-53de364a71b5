package com.bdyl.line.web.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.bdyl.boot.exception.BizException;
import com.bdyl.line.common.constant.error.LineError;
import com.bdyl.line.web.entity.CameraEntity;
import com.bdyl.line.web.entity.CameraModelConfigEntity;
import com.bdyl.line.web.mapper.CameraModelConfigMapper;
import com.bdyl.line.web.model.request.CameraModelConfigRequest;
import com.bdyl.line.web.model.response.camera.CameraModelConfigResponse;
import com.bdyl.line.web.service.CameraModelConfigService;

/**
 * 摄像头模型配置服务实现
 */
@Service
@RequiredArgsConstructor
public class CameraModelConfigServiceImpl implements CameraModelConfigService {

    /**
     * 摄像头模型配置mapper
     */
    private final CameraModelConfigMapper cameraModelConfigMapper;

    @Override
    @Transactional
    public void createBatch(CameraEntity cameraEntity) {
        if (CollectionUtils.isEmpty(cameraEntity.getModelCodes())) {
            return;
        }
        for (String modelCode : cameraEntity.getModelCodes()) {
            CameraModelConfigEntity entity = new CameraModelConfigEntity();
            entity.setOrganId(cameraEntity.getOrganId());
            entity.setCameraId(cameraEntity.getId());
            entity.setModelCode(modelCode);
            entity.setConfidence(BigDecimal.valueOf(0.45));
            entity.setRegionPoints(null);
            cameraModelConfigMapper.insert(entity);
        }
    }

    @Override
    @Transactional
    public void syncModelConfig(CameraEntity cameraEntity) {
        Long cameraId = cameraEntity.getId();
        List<String> newModelCodes = cameraEntity.getModelCodes();

        List<String> oldModelCodes = cameraModelConfigMapper.selectModelCodesByCameraId(cameraId);
        Set<String> oldSet = new HashSet<>(Optional.ofNullable(oldModelCodes).orElse(Collections.emptyList()));
        Set<String> newSet = new HashSet<>(Optional.ofNullable(newModelCodes).orElse(Collections.emptyList()));

        // 新增
        for (String modelCode : newSet) {
            if (!oldSet.contains(modelCode)) {
                CameraModelConfigEntity entity = new CameraModelConfigEntity();
                entity.setOrganId(cameraEntity.getOrganId());
                entity.setCameraId(cameraId);
                entity.setModelCode(modelCode);
                entity.setConfidence(BigDecimal.valueOf(0.45));
                entity.setRegionPoints(null);
                entity.setCreateTime(LocalDateTime.now());
                cameraModelConfigMapper.insert(entity);
            }
        }
        // 删除
        for (String modelCode : oldSet) {
            if (!newSet.contains(modelCode)) {
                cameraModelConfigMapper.deleteByCameraIdAndModelCode(cameraId, modelCode);
            }
        }
    }

    @Override
    @Transactional
    public void draw(CameraModelConfigRequest request) {
        LambdaQueryWrapper<CameraModelConfigEntity> wrapper = buildCondition(request);

        CameraModelConfigEntity entity = cameraModelConfigMapper.selectOne(wrapper);
        if (entity == null) {
            throw new BizException(LineError.CAMERA_MODEL_CONFIG_NOT_EXIST);
        }
        entity.setRegionPoints(request.getRegionPoints());
        cameraModelConfigMapper.updateById(entity);
    }

    @Override
    public List<CameraModelConfigResponse> listByCameraId(Long cameraId) {
        List<CameraModelConfigEntity> list = cameraModelConfigMapper.selectList(
            new LambdaQueryWrapper<CameraModelConfigEntity>().eq(CameraModelConfigEntity::getCameraId, cameraId));
        return list.stream().map(entity -> {
            CameraModelConfigResponse resp = new CameraModelConfigResponse();
            BeanUtils.copyProperties(entity, resp);
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public void updateConfidence(CameraModelConfigRequest request) {
        if (request.getConfidence() == null) {
            request.setConfidence(BigDecimal.valueOf(0.45));
        }

        LambdaQueryWrapper<CameraModelConfigEntity> wrapper = buildCondition(request);

        CameraModelConfigEntity cameraModelConfigEntity = cameraModelConfigMapper.selectOne(wrapper);
        if (cameraModelConfigEntity == null) {
            throw new BizException(LineError.CAMERA_MODEL_CONFIG_NOT_EXIST);
        }
        cameraModelConfigEntity.setConfidence(request.getConfidence());
        cameraModelConfigMapper.updateById(cameraModelConfigEntity);

    }

    @Override
    public CameraModelConfigResponse getDetail(Long cameraId, String modelCode) {

        CameraModelConfigRequest request = new CameraModelConfigRequest();
        request.setCameraId(cameraId);
        request.setModelCode(modelCode);
        LambdaQueryWrapper<CameraModelConfigEntity> wrapper = buildCondition(request);
        CameraModelConfigEntity entity = cameraModelConfigMapper.selectOne(wrapper);
        if (entity == null) {
            return null;
        }
        CameraModelConfigResponse resp = new CameraModelConfigResponse();
        BeanUtils.copyProperties(entity, resp);
        return resp;
    }

    private LambdaQueryWrapper<CameraModelConfigEntity> buildCondition(CameraModelConfigRequest request) {

        LambdaQueryWrapper<CameraModelConfigEntity> wrapper = new LambdaQueryWrapper<>();

        if (request.getCameraId() != null) {
            wrapper.eq(CameraModelConfigEntity::getCameraId, request.getCameraId());

        }
        if (StringUtils.isNotBlank(request.getModelCode())) {
            wrapper.eq(CameraModelConfigEntity::getModelCode, request.getModelCode());

        }
        return wrapper;

    }

}
