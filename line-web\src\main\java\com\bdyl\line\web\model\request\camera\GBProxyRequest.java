package com.bdyl.line.web.model.request.camera;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import lombok.Data;

/**
 * 拉流代理请求DTO
 */
@Data
public class GBProxyRequest {

    /**
     * 设备ID
     */
    @NotNull(message = "设备ID不能为空")
    private Long deviceId;

    /**
     * 设备code
     */
    private String deviceCode;

    /**
     * 流应用名
     */
    @NotBlank(message = "流应用名不能为空")
    private String app;

    /**
     * 流ID
     */
    @NotBlank(message = "流ID不能为空")
    private String stream;

    /**
     * rtsp流地址
     */
    @NotBlank(message = "rtsp流地址不能为空")
    private String url;

    /**
     * 拉流方式 0：tcp，1：udp，2：组播
     */
    private String rtpType = "0";

    /**
     * 是否开启MP4录制
     */
    @NotNull(message = "是否开启MP4录制不能为空")
    private Boolean enableMp4;

    /**
     * 是否开启RTSP
     */
    private Boolean enableRtsp;

    /**
     * 是否开启RTMP
     */
    private Boolean enableRtmp;

    /**
     * 是否开启HLS
     */
    @NotNull(message = "是否开启HLS不能为空")
    private Boolean enableHls;

    /**
     * 拉流超时时间（秒）
     */
    private Integer timeoutSec;
}
