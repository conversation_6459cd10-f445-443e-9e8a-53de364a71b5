package com.bdyl.line.web.entity.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import com.bdyl.line.web.model.dto.PointDTO;

/**
 * Mybatis TypeHandler: List<PointDTO> <-> JSON字符串 用于regionPoints字段自动映射
 *
 * <AUTHOR>
 */
public class PointListTypeHandler extends BaseTypeHandler<List<PointDTO>> {
    /**
     * 序列化器
     */
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<PointDTO> parameter, JdbcType jdbcType)
        throws SQLException {
        try {
            ps.setString(i, objectMapper.writeValueAsString(parameter));
        } catch (Exception e) {
            throw new RuntimeException("序列化List<PointDTO>失败", e);
        }
    }

    @Override
    public List<PointDTO> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        if (StringUtils.isBlank(json)) {
            return Collections.emptyList();
        }
        try {
            return objectMapper.readValue(json, new TypeReference<List<PointDTO>>() {});
        } catch (Exception e) {
            throw new RuntimeException("反序列化List<PointDTO>失败", e);
        }
    }

    @Override
    public List<PointDTO> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        if (StringUtils.isBlank(json)) {
            return Collections.emptyList();
        }
        try {
            return objectMapper.readValue(json, new TypeReference<List<PointDTO>>() {});
        } catch (Exception e) {
            throw new RuntimeException("反序列化List<PointDTO>失败", e);
        }
    }

    @Override
    public List<PointDTO> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        if (StringUtils.isBlank(json)) {
            return Collections.emptyList();
        }
        try {
            return objectMapper.readValue(json, new TypeReference<List<PointDTO>>() {});
        } catch (Exception e) {
            throw new RuntimeException("反序列化List<PointDTO>失败", e);
        }
    }
}
