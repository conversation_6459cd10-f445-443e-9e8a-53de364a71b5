<template>
  <!-- 设备在线率 -->
  <div class="homeChild_cont">
    <div class="tippv_cont">
      <div class="title_spel">PV组件</div>
      <a-row class="cont_row">
        <a-col :span="12"
          ><span>{{ sourcesData?.pvVoltage || 0 }}</span
          ><br />电压(V)</a-col
        >
        <a-col :span="12"
          ><span>{{ sourcesData?.pvCurrent || 0 }}</span
          ><br />电流(A)</a-col
        >
        <a-col :span="12"
          ><span>{{ sourcesData?.pvPower || 0 }}</span
          ><br />发电功率(W)</a-col
        >
      </a-row>
    </div>
    <div class="tipzl_cont">
      <div class="title_spel">直流负载</div>
      <a-row class="cont_row">
        <a-col
          :span="11"
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
          "
        >
          <img
            src="../../../../assets/images/biaop.png"
            alt=""
            style="width: 100px"
          />
          <div>
            <span>{{ sourcesData?.dcLoadEnergyToday || 0 }}</span
            ><br />当日用电量(KWH)
          </div>
        </a-col>
        <a-col :span="13">
          <a-row>
            <a-col :span="12">
              <span>{{ sourcesData?.dcLoadVoltage || 0 }}</span>
              <br />直流负载电压(V)
            </a-col>
            <a-col :span="12"
              ><span>{{ sourcesData?.dcLoadCurrent || 0 }}</span
              ><br />直流负载电流(A)</a-col
            >
            <a-col :span="12"
              ><span>{{ sourcesData?.dcLoadPower || 0 }}</span
              ><br />直流负载功率(W)</a-col
            >
          </a-row>
        </a-col>
      </a-row>
    </div>
    <div class="tipxdc_cont">
      <div class="title_spel">蓄电池</div>
      <a-row class="cont_row">
        <a-col :span="12"
          ><span>{{ sourcesData?.batteryTemperature || 0 }}</span
          ><br />温度(℃)</a-col
        >
        <a-col :span="12"
          ><span>{{ sourcesData?.batteryLevel || 0 }}</span
          ><br />剩余电量(%)</a-col
        >
        <a-col :span="12"
          ><span>{{ sourcesData?.batteryVoltage || 0 }}</span
          ><br />电压(V)</a-col
        >
        <a-col :span="12"
          ><span>{{ sourcesData?.batteryCurrent || 0 }}</span
          ><br />电流(A)</a-col
        >
      </a-row>
    </div>
    <img class="img_cont" src="../../../../assets/images/kongBG.png" alt="" />
    <div class="devname">{{ terminalName }}</div>
    <div class="btncur_div">
      <span>远程断电：</span>
      <a-switch v-model:checked="terminalIsOnline" @change="handleControl" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
defineProps({
  sourcesData: {
    type: Object,
    default: () => {},
  },
});
const terminalName = ref();
const terminalIsOnline = ref<boolean>(true);

const handleControl = (val: any) => {
  console.log(val);
};
onMounted(() => {
  const pre = JSON.parse(localStorage.getItem('terminalTemp') || '{}');
  terminalName.value = pre.terminalName || '-';

  terminalIsOnline.value = pre.terminalIsOnline || true;
});
</script>
<style lang="scss" scoped>
.homeChild_cont {
  height: 100%;
  width: 100%;
}
.tippv_cont {
  position: absolute;
  top: 0%;
  left: 5%;
  width: 320px;
  border: 2px solid #006be6;
  padding: 6px;
}
.tipzl_cont {
  position: absolute;
  top: 0%;
  right: 5%;
  width: 350px;
  border: 2px solid #006be6;
  padding: 6px;
}
.tipxdc_cont {
  position: absolute;
  bottom: 10%;
  right: 2%;
  width: 320px;
  border: 2px solid #006be6;
  padding: 6px;
}
.img_cont {
  width: 43%;
  position: absolute;
  left: 15%;
  top: 32%;
  margin: auto;
  min-width: 456px;
  min-height: 400px;
}
.title_spel {
  padding: 4px 10px;
  border-bottom: 1px solid #006be6;
  font-weight: bold;
}
.cont_row {
  text-align: center;
  padding-top: 6px;
}
.ant-col-12 {
  text-align: center;
  margin-bottom: 2%;
}
span {
  font-size: 16px;
}
.btncur_div {
  position: absolute;
  right: 12%;
  top: 35%;
  span {
    font-size: 14px;
  }
  span:hover {
    background: #ffffff;
  }
}
.devname {
  position: absolute;
  top: 35%;
  left: 10%;
  font-weight: bold;
  writing-mode: vertical-rl;
  color: #006be6;
}
</style>
