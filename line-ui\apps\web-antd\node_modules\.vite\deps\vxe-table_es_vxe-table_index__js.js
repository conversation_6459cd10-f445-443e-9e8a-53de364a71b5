import {
  table_default
} from "./chunk-7ETRVTHS.js";
import {
  addClass,
  getAbsolutePos,
  getCellValue,
  getDomNode,
  getEventTargetNode,
  getRefElem,
  getRowid,
  handleFieldOrColumn,
  hasClass,
  hasControlKey,
  isColumnInfo,
  removeClass,
  scrollToView,
  setCellValue,
  toCssUnit,
  toFilters,
  triggerEvent
} from "./chunk-R73ZCAIJ.js";
import {
  errLog,
  getChangeEvent,
  getModelEvent,
  getOnName,
  warnLog
} from "./chunk-GW2WQRXE.js";
import {
  eqEmptyValue,
  formatText,
  getFuncText,
  hasChildrenList,
  isEmptyValue,
  isEnableConf,
  parseFile
} from "./chunk-RFZDZ2Z5.js";
import {
  VxeUI,
  require_xe_utils
} from "./chunk-TV7URO3H.js";
import "./chunk-JUL7CFXM.js";
import {
  computed,
  h,
  inject,
  nextTick,
  reactive,
  resolveComponent
} from "./chunk-ZLVVKZUX.js";
import "./chunk-CMAVT37G.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/src/use/cell-view.js
var import_xe_utils = __toESM(require_xe_utils());
function useCellView(props) {
  const currColumn = computed(() => {
    const { renderParams } = props;
    return renderParams.column;
  });
  const currRow = computed(() => {
    const { renderParams } = props;
    return renderParams.row;
  });
  const cellOptions = computed(() => {
    const { renderOpts } = props;
    return renderOpts.props || {};
  });
  const cellModel = computed({
    get() {
      const { renderParams } = props;
      const { row, column } = renderParams;
      return import_xe_utils.default.get(row, column.field);
    },
    set(value) {
      const { renderParams } = props;
      const { row, column } = renderParams;
      return import_xe_utils.default.set(row, column.field, value);
    }
  });
  return {
    currColumn,
    currRow,
    cellModel,
    cellOptions
  };
}

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/module/filter/hook.js
var import_xe_utils2 = __toESM(require_xe_utils());
var { renderer, hooks } = VxeUI;
var tableFilterMethodKeys = ["openFilter", "setFilter", "clearFilter", "saveFilterPanel", "resetFilterPanel", "getCheckedFilters", "updateFilterOptionStatus"];
hooks.add("tableFilterModule", {
  setupTable($xeTable) {
    const { props, reactData, internalData } = $xeTable;
    const { refElem, refTableFilter } = $xeTable.getRefMaps();
    const { computeFilterOpts, computeMouseOpts } = $xeTable.getComputeMaps();
    const handleFilterConfirmFilter = (evnt) => {
      const { filterStore } = reactData;
      filterStore.options.forEach((option) => {
        option.checked = option._checked;
      });
      $xeTable.confirmFilterEvent(evnt);
    };
    const changeRadioOption = (evnt, checked, item) => {
      const { filterStore } = reactData;
      filterStore.options.forEach((option) => {
        option._checked = false;
      });
      item._checked = checked;
      $xeTable.checkFilterOptions();
      handleFilterConfirmFilter(evnt);
    };
    const changeMultipleOption = (evnt, checked, item) => {
      item._checked = checked;
      $xeTable.checkFilterOptions();
    };
    const handleFilterResetFilter = (evnt) => {
      const { filterStore } = reactData;
      $xeTable.handleClearFilter(filterStore.column);
      $xeTable.confirmFilterEvent(evnt);
    };
    const filterPrivateMethods = {
      checkFilterOptions() {
        const { filterStore } = reactData;
        filterStore.isAllSelected = filterStore.options.every((item) => item._checked);
        filterStore.isIndeterminate = !filterStore.isAllSelected && filterStore.options.some((item) => item._checked);
      },
      /**
       * 点击筛选事件
       * 当筛选图标被点击时触发
       * 更新选项是否全部状态
       * 打开筛选面板
       * @param {Event} evnt 事件
       * @param {ColumnInfo} column 列配置
       * @param {Object} params 参数
       */
      triggerFilterEvent(evnt, column, params) {
        const { initStore, filterStore } = reactData;
        const { elemStore } = internalData;
        if (filterStore.column === column && filterStore.visible) {
          filterStore.visible = false;
        } else {
          const el = refElem.value;
          const { scrollTop, scrollLeft, visibleHeight, visibleWidth } = getDomNode();
          const filterOpts = computeFilterOpts.value;
          const { transfer } = filterOpts;
          const tableRect = el.getBoundingClientRect();
          const btnElem = evnt.currentTarget;
          const { filters, filterMultiple, filterRender } = column;
          const compConf = isEnableConf(filterRender) ? renderer.get(filterRender.name) : null;
          const frMethod = column.filterRecoverMethod || (compConf ? compConf.tableFilterRecoverMethod || compConf.filterRecoverMethod : null);
          internalData._currFilterParams = params;
          Object.assign(filterStore, {
            multiple: filterMultiple,
            options: filters,
            column,
            style: null
          });
          filterStore.options.forEach((option) => {
            const { _checked, checked } = option;
            option._checked = checked;
            if (!checked && _checked !== checked) {
              if (frMethod) {
                frMethod({ option, column, $table: $xeTable });
              }
            }
          });
          this.checkFilterOptions();
          filterStore.visible = true;
          initStore.filter = true;
          nextTick(() => {
            const headerScrollElem = getRefElem(elemStore["main-header-scroll"]);
            if (!headerScrollElem) {
              return;
            }
            const tableFilter = refTableFilter.value;
            const filterWrapperElem = tableFilter ? tableFilter.getRefMaps().refElem.value : null;
            if (!filterWrapperElem) {
              return;
            }
            const btnRect = btnElem.getBoundingClientRect();
            const filterHeadElem = filterWrapperElem.querySelector(".vxe-table--filter-header");
            const filterFootElem = filterWrapperElem.querySelector(".vxe-table--filter-footer");
            const filterWidth = filterWrapperElem.offsetWidth;
            const centerWidth = filterWidth / 2;
            let left = 0;
            let top = 0;
            let maxHeight = 0;
            if (transfer) {
              left = btnRect.left - centerWidth + scrollLeft;
              top = btnRect.top + btnElem.clientHeight + scrollTop;
              maxHeight = Math.min(Math.max(tableRect.height, Math.floor(visibleHeight / 2)), Math.max(80, visibleHeight - top - (filterHeadElem ? filterHeadElem.clientHeight : 0) - (filterFootElem ? filterFootElem.clientHeight : 0) - 28));
              if (left < 16) {
                left = 16;
              } else if (left > visibleWidth - filterWidth - 16) {
                left = visibleWidth - filterWidth - 16;
              }
            } else {
              left = btnRect.left - tableRect.left - centerWidth;
              top = btnRect.top - tableRect.top + btnElem.clientHeight;
              maxHeight = Math.max(40, el.clientHeight - top - (filterHeadElem ? filterHeadElem.clientHeight : 0) - (filterFootElem ? filterFootElem.clientHeight : 0) - 14);
              if (left < 1) {
                left = 1;
              } else if (left > el.clientWidth - filterWidth - 1) {
                left = el.clientWidth - filterWidth - 1;
              }
            }
            filterStore.style = {
              top: toCssUnit(top),
              left: toCssUnit(left)
            };
            filterStore.maxHeight = maxHeight;
          });
        }
        $xeTable.dispatchEvent("filter-visible", { column, field: column.field, property: column.field, filterList: $xeTable.getCheckedFilters(), visible: filterStore.visible }, evnt);
      },
      handleClearFilter(column) {
        if (column) {
          const { filters, filterRender } = column;
          if (filters) {
            const compConf = isEnableConf(filterRender) ? renderer.get(filterRender.name) : null;
            const frMethod = column.filterResetMethod || (compConf ? compConf.tableFilterResetMethod || compConf.filterResetMethod : null);
            filters.forEach((item) => {
              item._checked = false;
              item.checked = false;
              if (!frMethod) {
                item.data = import_xe_utils2.default.clone(item.resetValue, true);
              }
            });
            if (frMethod) {
              frMethod({ options: filters, column, $table: $xeTable });
            }
          }
        }
      },
      handleColumnConfirmFilter(column, evnt) {
        const { mouseConfig } = props;
        const { scrollXLoad: oldScrollXLoad, scrollYLoad: oldScrollYLoad } = reactData;
        const filterOpts = computeFilterOpts.value;
        const mouseOpts = computeMouseOpts.value;
        const { field } = column;
        const values = [];
        const datas = [];
        column.filters.forEach((item) => {
          if (item.checked) {
            values.push(item.value);
            datas.push(item.data);
          }
        });
        const filterList = $xeTable.getCheckedFilters();
        const params = { $table: $xeTable, $event: evnt, column, field, property: field, values, datas, filters: filterList, filterList };
        if (!filterOpts.remote) {
          $xeTable.handleTableData(true);
          $xeTable.checkSelectionStatus();
        }
        if (mouseConfig && mouseOpts.area && $xeTable.handleFilterEvent) {
          $xeTable.handleFilterEvent(evnt, params);
        }
        if (evnt) {
          $xeTable.dispatchEvent("filter-change", params, evnt);
        }
        $xeTable.closeFilter();
        return $xeTable.updateFooter().then(() => {
          const { scrollXLoad, scrollYLoad } = reactData;
          if (oldScrollXLoad || scrollXLoad || (oldScrollYLoad || scrollYLoad)) {
            if (oldScrollXLoad || scrollXLoad) {
              $xeTable.updateScrollXSpace();
            }
            if (oldScrollYLoad || scrollYLoad) {
              $xeTable.updateScrollYSpace();
            }
            return $xeTable.refreshScroll();
          }
        }).then(() => {
          $xeTable.updateCellAreas();
          return $xeTable.recalculate(true);
        }).then(() => {
          setTimeout(() => $xeTable.recalculate(), 50);
        });
      },
      /**
       * 确认筛选
       * 当筛选面板中的确定按钮被按下时触发
       * @param {Event} evnt 事件
       */
      confirmFilterEvent(evnt) {
        const { filterStore } = reactData;
        const { column } = filterStore;
        $xeTable.handleColumnConfirmFilter(column, evnt);
      },
      handleFilterChangeRadioOption: changeRadioOption,
      handleFilterChangeMultipleOption: changeMultipleOption,
      // 筛选发生改变
      handleFilterChangeOption(evnt, checked, item) {
        const { filterStore } = reactData;
        if (filterStore.multiple) {
          changeMultipleOption(evnt, checked, item);
        } else {
          changeRadioOption(evnt, checked, item);
        }
      },
      handleFilterConfirmFilter,
      handleFilterResetFilter
    };
    const filterMethods = {
      /**
       * 手动弹出筛选面板
       * @param column
       */
      openFilter(fieldOrColumn) {
        const column = handleFieldOrColumn($xeTable, fieldOrColumn);
        if (column && column.filters) {
          const { elemStore } = internalData;
          const { fixed } = column;
          return $xeTable.scrollToColumn(column).then(() => {
            const headerWrapperElem = getRefElem(elemStore[`${fixed || "main"}-header-wrapper`] || elemStore["main-header-wrapper"]);
            if (headerWrapperElem) {
              const filterBtnElem = headerWrapperElem.querySelector(`.vxe-header--column.${column.id} .vxe-cell--filter`);
              triggerEvent(filterBtnElem, "click");
            }
          });
        }
        return nextTick();
      },
      /**
       * 修改筛选条件列表
       * @param {ColumnInfo} fieldOrColumn 列或字段名
       * @param {Array} options 选项
       */
      setFilter(fieldOrColumn, options, isUpdate) {
        const column = handleFieldOrColumn($xeTable, fieldOrColumn);
        if (column && column.filters) {
          column.filters = toFilters(options || []);
          if (isUpdate) {
            return $xeTable.handleColumnConfirmFilter(column, new Event("click"));
          }
        }
        return nextTick();
      },
      /**
       * 清空指定列的筛选条件
       * 如果为空则清空所有列的筛选条件
       * @param {String} fieldOrColumn 列或字段名
       */
      clearFilter(fieldOrColumn) {
        const { filterStore } = reactData;
        const { tableFullColumn } = internalData;
        const filterOpts = computeFilterOpts.value;
        let column;
        if (fieldOrColumn) {
          column = handleFieldOrColumn($xeTable, fieldOrColumn);
          if (column) {
            filterPrivateMethods.handleClearFilter(column);
          }
        } else {
          tableFullColumn.forEach(filterPrivateMethods.handleClearFilter);
        }
        if (!fieldOrColumn || column !== filterStore.column) {
          Object.assign(filterStore, {
            isAllSelected: false,
            isIndeterminate: false,
            style: null,
            options: [],
            column: null,
            multiple: false,
            visible: false
          });
        }
        if (!filterOpts.remote) {
          return $xeTable.updateData();
        }
        return nextTick();
      },
      saveFilterPanel() {
        handleFilterConfirmFilter(null);
        return nextTick();
      },
      resetFilterPanel() {
        handleFilterResetFilter(null);
        return nextTick();
      },
      getCheckedFilters() {
        const { tableFullColumn } = internalData;
        const filterList = [];
        tableFullColumn.forEach((column) => {
          const { field, filters } = column;
          const valueList = [];
          const dataList = [];
          if (filters && filters.length) {
            filters.forEach((item) => {
              if (item.checked) {
                valueList.push(item.value);
                dataList.push(item.data);
              }
            });
            if (valueList.length) {
              filterList.push({ column, field, property: field, values: valueList, datas: dataList });
            }
          }
        });
        return filterList;
      },
      updateFilterOptionStatus(item, checked) {
        item._checked = checked;
        item.checked = checked;
        return nextTick();
      }
    };
    return Object.assign(Object.assign({}, filterMethods), filterPrivateMethods);
  },
  setupGrid($xeGrid) {
    return $xeGrid.extendTableMethods(tableFilterMethodKeys);
  }
});

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/module/menu/hook.js
var import_xe_utils3 = __toESM(require_xe_utils());
var { menus, hooks: hooks2, globalEvents, GLOBAL_EVENT_KEYS } = VxeUI;
var tableMenuMethodKeys = ["closeMenu"];
hooks2.add("tableMenuModule", {
  setupTable($xeTable) {
    const { xID, props, reactData, internalData } = $xeTable;
    const { refElem, refTableFilter, refTableMenu } = $xeTable.getRefMaps();
    const { computeMouseOpts, computeIsMenu, computeMenuOpts } = $xeTable.getComputeMaps();
    let menuMethods = {};
    let menuPrivateMethods = {};
    const handleOpenMenuEvent = (evnt, type, params) => {
      const { ctxMenuStore } = reactData;
      const isMenu = computeIsMenu.value;
      const menuOpts = computeMenuOpts.value;
      const config = menuOpts[type];
      const visibleMethod = menuOpts.visibleMethod;
      if (config) {
        const { options, disabled } = config;
        if (disabled) {
          evnt.preventDefault();
        } else if (isMenu && options && options.length) {
          params.options = options;
          $xeTable.preventEvent(evnt, "event.showMenu", params, () => {
            if (!visibleMethod || visibleMethod(params)) {
              evnt.preventDefault();
              $xeTable.updateZindex();
              const { scrollTop, scrollLeft, visibleHeight, visibleWidth } = getDomNode();
              let top = evnt.clientY + scrollTop;
              let left = evnt.clientX + scrollLeft;
              const handleVisible = () => {
                internalData._currMenuParams = params;
                Object.assign(ctxMenuStore, {
                  visible: true,
                  list: options,
                  selected: null,
                  selectChild: null,
                  showChild: false,
                  style: {
                    zIndex: internalData.tZindex,
                    top: `${top}px`,
                    left: `${left}px`
                  }
                });
                nextTick(() => {
                  const tableMenu = refTableMenu.value;
                  const ctxElem = tableMenu.getRefMaps().refElem.value;
                  const clientHeight = ctxElem.clientHeight;
                  const clientWidth = ctxElem.clientWidth;
                  const { boundingTop, boundingLeft } = getAbsolutePos(ctxElem);
                  const offsetTop = boundingTop + clientHeight - visibleHeight;
                  const offsetLeft = boundingLeft + clientWidth - visibleWidth;
                  if (offsetTop > -10) {
                    ctxMenuStore.style.top = `${Math.max(scrollTop + 2, top - clientHeight - 2)}px`;
                  }
                  if (offsetLeft > -10) {
                    ctxMenuStore.style.left = `${Math.max(scrollLeft + 2, left - clientWidth - 2)}px`;
                  }
                });
              };
              const { keyboard, row, column } = params;
              if (keyboard && row && column) {
                $xeTable.scrollToRow(row, column).then(() => {
                  const cell = $xeTable.getCellElement(row, column);
                  if (cell) {
                    const { boundingTop, boundingLeft } = getAbsolutePos(cell);
                    top = boundingTop + scrollTop + Math.floor(cell.offsetHeight / 2);
                    left = boundingLeft + scrollLeft + Math.floor(cell.offsetWidth / 2);
                  }
                  handleVisible();
                });
              } else {
                handleVisible();
              }
            } else {
              menuMethods.closeMenu();
            }
          });
        }
      }
      $xeTable.closeFilter();
    };
    menuMethods = {
      /**
       * 关闭快捷菜单
       */
      closeMenu() {
        Object.assign(reactData.ctxMenuStore, {
          visible: false,
          selected: null,
          selectChild: null,
          showChild: false
        });
        return nextTick();
      }
    };
    menuPrivateMethods = {
      /**
       * 处理菜单的移动
       */
      moveCtxMenu(evnt, ctxMenuStore, property, hasOper, operRest, menuList) {
        let selectItem;
        const selectIndex = import_xe_utils3.default.findIndexOf(menuList, (item) => ctxMenuStore[property] === item);
        if (hasOper) {
          if (operRest && hasChildrenList(ctxMenuStore.selected)) {
            ctxMenuStore.showChild = true;
          } else {
            ctxMenuStore.showChild = false;
            ctxMenuStore.selectChild = null;
          }
        } else if (globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.ARROW_UP)) {
          for (let len = selectIndex - 1; len >= 0; len--) {
            if (menuList[len].visible !== false) {
              selectItem = menuList[len];
              break;
            }
          }
          ctxMenuStore[property] = selectItem || menuList[menuList.length - 1];
        } else if (globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.ARROW_DOWN)) {
          for (let index = selectIndex + 1; index < menuList.length; index++) {
            if (menuList[index].visible !== false) {
              selectItem = menuList[index];
              break;
            }
          }
          ctxMenuStore[property] = selectItem || menuList[0];
        } else if (ctxMenuStore[property] && (globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.ENTER) || globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.SPACEBAR))) {
          $xeTable.ctxMenuLinkEvent(evnt, ctxMenuStore[property]);
        }
      },
      handleOpenMenuEvent,
      /**
       * 快捷菜单事件处理
       */
      handleGlobalContextmenuEvent(evnt) {
        const { mouseConfig, menuConfig } = props;
        const { editStore, ctxMenuStore } = reactData;
        const { visibleColumn } = internalData;
        const tableFilter = refTableFilter.value;
        const tableMenu = refTableMenu.value;
        const mouseOpts = computeMouseOpts.value;
        const menuOpts = computeMenuOpts.value;
        const el = refElem.value;
        const { selected } = editStore;
        const layoutList = ["header", "body", "footer"];
        if (isEnableConf(menuConfig)) {
          if (ctxMenuStore.visible && tableMenu && getEventTargetNode(evnt, tableMenu.getRefMaps().refElem.value).flag) {
            evnt.preventDefault();
            return;
          }
          if (internalData._keyCtx) {
            const type = "body";
            const params = { type, $table: $xeTable, keyboard: true, columns: visibleColumn.slice(0), $event: evnt };
            if (mouseConfig && mouseOpts.area) {
              const activeArea = $xeTable.getActiveCellArea();
              if (activeArea && activeArea.row && activeArea.column) {
                params.row = activeArea.row;
                params.column = activeArea.column;
                handleOpenMenuEvent(evnt, type, params);
                return;
              }
            } else if (mouseConfig && mouseOpts.selected) {
              if (selected.row && selected.column) {
                params.row = selected.row;
                params.column = selected.column;
                handleOpenMenuEvent(evnt, type, params);
                return;
              }
            }
          }
          for (let index = 0; index < layoutList.length; index++) {
            const layout = layoutList[index];
            const columnTargetNode = getEventTargetNode(evnt, el, `vxe-${layout}--column`, (target) => {
              return target.parentNode.parentNode.parentNode.getAttribute("xid") === xID;
            });
            const params = { type: layout, $table: $xeTable, columns: visibleColumn.slice(0), $event: evnt };
            if (columnTargetNode.flag) {
              const cell = columnTargetNode.targetElem;
              const columnNodeRest = $xeTable.getColumnNode(cell);
              const column = columnNodeRest ? columnNodeRest.item : null;
              let typePrefix = `${layout}-`;
              if (column) {
                Object.assign(params, { column, columnIndex: $xeTable.getColumnIndex(column), cell });
              }
              if (layout === "body") {
                const rowNodeRest = $xeTable.getRowNode(cell.parentNode);
                const row = rowNodeRest ? rowNodeRest.item : null;
                typePrefix = "";
                if (row) {
                  params.row = row;
                  params.rowIndex = $xeTable.getRowIndex(row);
                }
              }
              const eventType = `${typePrefix}cell-menu`;
              handleOpenMenuEvent(evnt, layout, params);
              $xeTable.dispatchEvent(eventType, params, evnt);
              return;
            } else if (getEventTargetNode(evnt, el, `vxe-table--${layout}-wrapper`, (target) => target.getAttribute("xid") === xID).flag) {
              if (menuOpts.trigger === "cell") {
                evnt.preventDefault();
              } else {
                handleOpenMenuEvent(evnt, layout, params);
              }
              return;
            }
          }
        }
        if (tableFilter && !getEventTargetNode(evnt, tableFilter.getRefMaps().refElem.value).flag) {
          $xeTable.closeFilter();
        }
        menuMethods.closeMenu();
      },
      ctxMenuMouseoverEvent(evnt, item, child) {
        const menuElem = evnt.currentTarget;
        const { ctxMenuStore } = reactData;
        evnt.preventDefault();
        evnt.stopPropagation();
        ctxMenuStore.selected = item;
        ctxMenuStore.selectChild = child;
        if (!child) {
          ctxMenuStore.showChild = hasChildrenList(item);
          if (ctxMenuStore.showChild) {
            nextTick(() => {
              const childWrapperElem = menuElem.nextElementSibling;
              if (childWrapperElem) {
                const { boundingTop, boundingLeft, visibleHeight, visibleWidth } = getAbsolutePos(menuElem);
                const posTop = boundingTop + menuElem.offsetHeight;
                const posLeft = boundingLeft + menuElem.offsetWidth;
                let left = "";
                let right = "";
                if (posLeft + childWrapperElem.offsetWidth > visibleWidth - 10) {
                  left = "auto";
                  right = `${menuElem.offsetWidth}px`;
                }
                let top = "";
                let bottom = "";
                if (posTop + childWrapperElem.offsetHeight > visibleHeight - 10) {
                  top = "auto";
                  bottom = "0";
                }
                childWrapperElem.style.left = left;
                childWrapperElem.style.right = right;
                childWrapperElem.style.top = top;
                childWrapperElem.style.bottom = bottom;
              }
            });
          }
        }
      },
      ctxMenuMouseoutEvent(evnt, item) {
        const { ctxMenuStore } = reactData;
        if (!item.children) {
          ctxMenuStore.selected = null;
        }
        ctxMenuStore.selectChild = null;
      },
      /**
       * 快捷菜单点击事件
       */
      ctxMenuLinkEvent(evnt, menu) {
        const $xeGrid = $xeTable.xeGrid;
        if (!menu.disabled && (menu.code || !menu.children || !menu.children.length)) {
          const gMenuOpts = menus.get(menu.code);
          const params = Object.assign({}, internalData._currMenuParams, { menu, $table: $xeTable, $grid: $xeGrid, $event: evnt });
          const tmMethod = gMenuOpts ? gMenuOpts.tableMenuMethod || gMenuOpts.menuMethod : null;
          if (tmMethod) {
            tmMethod(params, evnt);
          }
          $xeTable.dispatchEvent("menu-click", params, evnt);
          menuMethods.closeMenu();
        }
      }
    };
    return Object.assign(Object.assign({}, menuMethods), menuPrivateMethods);
  },
  setupGrid($xeGrid) {
    return $xeGrid.extendTableMethods(tableMenuMethodKeys);
  }
});

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/module/edit/hook.js
var import_xe_utils4 = __toESM(require_xe_utils());
var { getConfig, renderer: renderer2, hooks: hooks3, getI18n } = VxeUI;
var tableEditMethodKeys = ["insert", "insertAt", "insertNextAt", "insertChild", "insertChildAt", "insertChildNextAt", "remove", "removeCheckboxRow", "removeRadioRow", "removeCurrentRow", "getRecordset", "getInsertRecords", "getRemoveRecords", "getUpdateRecords", "getEditRecord", "getActiveRecord", "getSelectedCell", "clearEdit", "clearActived", "clearSelected", "isEditByRow", "isActiveByRow", "setEditRow", "setActiveRow", "setEditCell", "setActiveCell", "setSelectCell"];
hooks3.add("tableEditModule", {
  setupTable($xeTable) {
    const { props, reactData, internalData } = $xeTable;
    const { refElem } = $xeTable.getRefMaps();
    const { computeMouseOpts, computeEditOpts, computeCheckboxOpts, computeTreeOpts, computeValidOpts } = $xeTable.getComputeMaps();
    const browseObj2 = import_xe_utils4.default.browse();
    let editMethods = {};
    let editPrivateMethods = {};
    const getEditColumnModel = (row, column) => {
      const { model, editRender } = column;
      if (editRender) {
        model.value = getCellValue(row, column);
        model.update = false;
      }
    };
    const setEditColumnModel = (row, column) => {
      const { model, editRender } = column;
      if (editRender && model.update) {
        setCellValue(row, column, model.value);
        model.update = false;
        model.value = null;
      }
    };
    const removeCellSelectedClass = () => {
      const el = refElem.value;
      if (el) {
        const cell = el.querySelector(".col--selected");
        if (cell) {
          removeClass(cell, "col--selected");
        }
      }
    };
    const syncActivedCell = () => {
      const { editStore, tableColumn } = reactData;
      const editOpts = computeEditOpts.value;
      const { actived } = editStore;
      const { row, column } = actived;
      if (row || column) {
        if (editOpts.mode === "row") {
          tableColumn.forEach((column2) => setEditColumnModel(row, column2));
        } else {
          setEditColumnModel(row, column);
        }
      }
    };
    const insertTreeRow = (newRecords, isAppend) => {
      const { tableFullTreeData, afterFullData, fullDataRowIdData, fullAllDataRowIdData } = internalData;
      const treeOpts = computeTreeOpts.value;
      const { rowField, parentField, mapChildrenField } = treeOpts;
      const childrenField = treeOpts.children || treeOpts.childrenField;
      const funcName = isAppend ? "push" : "unshift";
      newRecords.forEach((item) => {
        const parentRowId = item[parentField];
        const rowid = getRowid($xeTable, item);
        const matchObj = parentRowId ? import_xe_utils4.default.findTree(tableFullTreeData, (item2) => parentRowId === item2[rowField], { children: mapChildrenField }) : null;
        if (matchObj) {
          const { item: parentRow } = matchObj;
          const parentRest = fullAllDataRowIdData[getRowid($xeTable, parentRow)];
          const parentLevel = parentRest ? parentRest.level : 0;
          let parentChilds = parentRow[childrenField];
          let mapChilds = parentRow[mapChildrenField];
          if (!import_xe_utils4.default.isArray(parentChilds)) {
            parentChilds = parentRow[childrenField] = [];
          }
          if (!import_xe_utils4.default.isArray(mapChilds)) {
            mapChilds = parentRow[childrenField] = [];
          }
          parentChilds[funcName](item);
          mapChilds[funcName](item);
          const rest = { row: item, rowid, seq: -1, index: -1, _index: -1, $index: -1, treeIndex: -1, items: parentChilds, parent: parentRow, level: parentLevel + 1, height: 0, resizeHeight: 0, oTop: 0, expandHeight: 0 };
          fullDataRowIdData[rowid] = rest;
          fullAllDataRowIdData[rowid] = rest;
        } else {
          if (true) {
            if (parentRowId) {
              warnLog("vxe.error.unableInsert");
            }
          }
          afterFullData[funcName](item);
          tableFullTreeData[funcName](item);
          const rest = { row: item, rowid, seq: -1, index: -1, _index: -1, $index: -1, treeIndex: -1, items: tableFullTreeData, parent: null, level: 0, height: 0, resizeHeight: 0, oTop: 0, expandHeight: 0 };
          fullDataRowIdData[rowid] = rest;
          fullAllDataRowIdData[rowid] = rest;
        }
      });
    };
    const handleInsertRowAt = (records, targetRow, isInsertNextRow) => {
      const { treeConfig } = props;
      const { tableFullTreeData, afterFullData, mergeBodyList, tableFullData, fullDataRowIdData, fullAllDataRowIdData, insertRowMaps } = internalData;
      const treeOpts = computeTreeOpts.value;
      const { transform, rowField, mapChildrenField } = treeOpts;
      const childrenField = treeOpts.children || treeOpts.childrenField;
      if (!import_xe_utils4.default.isArray(records)) {
        records = [records];
      }
      const newRecords = reactive($xeTable.defineField(records.map((record) => Object.assign(treeConfig && transform ? { [mapChildrenField]: [], [childrenField]: [] } : {}, record))));
      if (import_xe_utils4.default.eqNull(targetRow)) {
        if (treeConfig && transform) {
          insertTreeRow(newRecords, false);
        } else {
          newRecords.forEach((item) => {
            const rowid = getRowid($xeTable, item);
            const rest = { row: item, rowid, seq: -1, index: -1, _index: -1, $index: -1, treeIndex: -1, items: afterFullData, parent: null, level: 0, height: 0, resizeHeight: 0, oTop: 0, expandHeight: 0 };
            fullDataRowIdData[rowid] = rest;
            fullAllDataRowIdData[rowid] = rest;
            afterFullData.unshift(item);
            tableFullData.unshift(item);
          });
          mergeBodyList.forEach((mergeItem) => {
            const { row: mergeRowIndex } = mergeItem;
            if (mergeRowIndex > 0) {
              mergeItem.row = mergeRowIndex + newRecords.length;
            }
          });
        }
      } else {
        if (targetRow === -1) {
          if (treeConfig && transform) {
            insertTreeRow(newRecords, true);
          } else {
            newRecords.forEach((item) => {
              const rowid = getRowid($xeTable, item);
              const rest = { row: item, rowid, seq: -1, index: -1, _index: -1, treeIndex: -1, $index: -1, items: afterFullData, parent: null, level: 0, height: 0, resizeHeight: 0, oTop: 0, expandHeight: 0 };
              fullDataRowIdData[rowid] = rest;
              fullAllDataRowIdData[rowid] = rest;
              afterFullData.push(item);
              tableFullData.push(item);
            });
            mergeBodyList.forEach((mergeItem) => {
              const { row: mergeRowIndex, rowspan: mergeRowspan } = mergeItem;
              if (mergeRowIndex + mergeRowspan > afterFullData.length) {
                mergeItem.rowspan = mergeRowspan + newRecords.length;
              }
            });
          }
        } else {
          if (treeConfig && transform) {
            const matchMapObj = import_xe_utils4.default.findTree(tableFullTreeData, (item) => targetRow[rowField] === item[rowField], { children: mapChildrenField });
            if (matchMapObj) {
              const { parent: parentRow } = matchMapObj;
              const parentMapChilds = parentRow ? parentRow[mapChildrenField] : tableFullTreeData;
              const parentRest = fullAllDataRowIdData[getRowid($xeTable, parentRow)];
              const parentLevel = parentRest ? parentRest.level : 0;
              newRecords.forEach((item, i) => {
                const rowid = getRowid($xeTable, item);
                if (true) {
                  if (item[treeOpts.parentField]) {
                    if (parentRow && item[treeOpts.parentField] !== parentRow[rowField]) {
                      errLog("vxe.error.errProp", [`${treeOpts.parentField}=${item[treeOpts.parentField]}`, `${treeOpts.parentField}=${parentRow[rowField]}`]);
                    }
                  }
                }
                if (parentRow) {
                  item[treeOpts.parentField] = parentRow[rowField];
                }
                let targetIndex = matchMapObj.index + i;
                if (isInsertNextRow) {
                  targetIndex = targetIndex + 1;
                }
                parentMapChilds.splice(targetIndex, 0, item);
                const rest = { row: item, rowid, seq: -1, index: -1, _index: -1, $index: -1, treeIndex: -1, items: parentMapChilds, parent: parentRow, level: parentLevel + 1, height: 0, resizeHeight: 0, oTop: 0, expandHeight: 0 };
                fullDataRowIdData[rowid] = rest;
                fullAllDataRowIdData[rowid] = rest;
              });
              if (parentRow) {
                const matchObj = import_xe_utils4.default.findTree(tableFullTreeData, (item) => targetRow[rowField] === item[rowField], { children: childrenField });
                if (matchObj) {
                  const parentChilds = matchObj.items;
                  let targetIndex = matchObj.index;
                  if (isInsertNextRow) {
                    targetIndex = targetIndex + 1;
                  }
                  parentChilds.splice(targetIndex, 0, ...newRecords);
                }
              }
            } else {
              if (true) {
                warnLog("vxe.error.unableInsert");
              }
              insertTreeRow(newRecords, true);
            }
          } else {
            if (treeConfig) {
              throw new Error(getI18n("vxe.error.noTree", ["insert"]));
            }
            let afIndex = -1;
            if (import_xe_utils4.default.isNumber(targetRow)) {
              if (targetRow < afterFullData.length) {
                afIndex = targetRow;
              }
            } else {
              afIndex = $xeTable.findRowIndexOf(afterFullData, targetRow);
            }
            if (isInsertNextRow) {
              afIndex = Math.min(afterFullData.length, afIndex + 1);
            }
            if (afIndex === -1) {
              throw new Error(getI18n("vxe.error.unableInsert"));
            }
            afterFullData.splice(afIndex, 0, ...newRecords);
            const tfIndex = $xeTable.findRowIndexOf(tableFullData, targetRow);
            if (tfIndex > -1) {
              tableFullData.splice(tfIndex + (isInsertNextRow ? 1 : 0), 0, ...newRecords);
            } else {
              tableFullData.push(...newRecords);
            }
            mergeBodyList.forEach((mergeItem) => {
              const { row: mergeRowIndex, rowspan: mergeRowspan } = mergeItem;
              if (mergeRowIndex > afIndex) {
                mergeItem.row = mergeRowIndex + newRecords.length;
              } else if (mergeRowIndex + mergeRowspan > afIndex) {
                mergeItem.rowspan = mergeRowspan + newRecords.length;
              }
            });
          }
        }
      }
      newRecords.forEach((newRow) => {
        const rowid = getRowid($xeTable, newRow);
        insertRowMaps[rowid] = newRow;
      });
      reactData.insertRowFlag++;
      $xeTable.cacheRowMap(false);
      $xeTable.updateScrollYStatus();
      $xeTable.handleTableData(treeConfig && transform);
      if (!(treeConfig && transform)) {
        $xeTable.updateAfterDataIndex();
      }
      $xeTable.updateFooter();
      $xeTable.handleUpdateBodyMerge();
      $xeTable.checkSelectionStatus();
      if (reactData.scrollYLoad) {
        $xeTable.updateScrollYSpace();
      }
      return nextTick().then(() => {
        $xeTable.updateCellAreas();
        return $xeTable.recalculate();
      }).then(() => {
        return {
          row: newRecords.length ? newRecords[newRecords.length - 1] : null,
          rows: newRecords
        };
      });
    };
    const handleInsertChildRowAt = (records, parentRow, targetRow, isInsertNextRow) => {
      const { treeConfig } = props;
      const treeOpts = computeTreeOpts.value;
      const { transform, rowField, parentField } = treeOpts;
      if (treeConfig && transform) {
        if (!import_xe_utils4.default.isArray(records)) {
          records = [records];
        }
        return handleInsertRowAt(records.map((item) => Object.assign({}, item, { [parentField]: parentRow[rowField] })), targetRow, isInsertNextRow);
      } else {
        errLog("vxe.error.errProp", ["tree-config.transform=false", "tree-config.transform=true"]);
      }
      return Promise.resolve({ row: null, rows: [] });
    };
    const handleClearEdit = (evnt, targetRow) => {
      const { editStore } = reactData;
      const { actived, focused } = editStore;
      const { row, column } = actived;
      const validOpts = computeValidOpts.value;
      if (row || column) {
        if (targetRow && getRowid($xeTable, targetRow) !== getRowid($xeTable, row)) {
          return nextTick();
        }
        syncActivedCell();
        actived.args = null;
        actived.row = null;
        actived.column = null;
        $xeTable.updateFooter();
        $xeTable.dispatchEvent("edit-closed", {
          row,
          rowIndex: $xeTable.getRowIndex(row),
          $rowIndex: $xeTable.getVMRowIndex(row),
          column,
          columnIndex: $xeTable.getColumnIndex(column),
          $columnIndex: $xeTable.getVMColumnIndex(column)
        }, evnt || null);
      }
      focused.row = null;
      focused.column = null;
      if (validOpts.autoClear) {
        if (validOpts.msgMode !== "full" || getConfig().cellVaildMode === "obsolete") {
          if ($xeTable.clearValidate) {
            return $xeTable.clearValidate();
          }
        }
      }
      return nextTick().then(() => $xeTable.updateCellAreas());
    };
    const handleEditActive = (params, evnt, isFocus, isPos) => {
      const $xeGrid = $xeTable.xeGrid;
      const { editConfig, mouseConfig } = props;
      const { editStore, tableColumn } = reactData;
      const editOpts = computeEditOpts.value;
      const { mode } = editOpts;
      const { actived, focused } = editStore;
      const { row, column } = params;
      const { editRender } = column;
      const cell = params.cell || $xeTable.getCellElement(row, column);
      const beforeEditMethod = editOpts.beforeEditMethod || editOpts.activeMethod;
      params.cell = cell;
      if (cell && isEnableConf(editConfig) && isEnableConf(editRender)) {
        if (!$xeTable.isPendingByRow(row)) {
          if (actived.row !== row || (mode === "cell" ? actived.column !== column : false)) {
            let type = "edit-disabled";
            if (!beforeEditMethod || beforeEditMethod(Object.assign(Object.assign({}, params), { $table: $xeTable, $grid: $xeGrid }))) {
              if (mouseConfig) {
                $xeTable.clearSelected();
                if ($xeTable.clearCellAreas) {
                  $xeTable.clearCellAreas();
                  $xeTable.clearCopyCellArea();
                }
              }
              $xeTable.closeTooltip();
              if (actived.column) {
                handleClearEdit(evnt);
              }
              type = "edit-activated";
              column.renderHeight = cell.offsetHeight;
              actived.args = params;
              actived.row = row;
              actived.column = column;
              if (mode === "row") {
                tableColumn.forEach((column2) => getEditColumnModel(row, column2));
              } else {
                getEditColumnModel(row, column);
              }
              const afterEditMethod = editOpts.afterEditMethod;
              nextTick(() => {
                if (isFocus) {
                  $xeTable.handleFocus(params, evnt);
                }
                if (afterEditMethod) {
                  afterEditMethod(Object.assign(Object.assign({}, params), { $table: $xeTable, $grid: $xeGrid }));
                }
              });
            }
            $xeTable.dispatchEvent(type, {
              row,
              rowIndex: $xeTable.getRowIndex(row),
              $rowIndex: $xeTable.getVMRowIndex(row),
              column,
              columnIndex: $xeTable.getColumnIndex(column),
              $columnIndex: $xeTable.getVMColumnIndex(column)
            }, evnt);
            if (type === "edit-activated") {
              $xeTable.dispatchEvent("edit-actived", {
                row,
                rowIndex: $xeTable.getRowIndex(row),
                $rowIndex: $xeTable.getVMRowIndex(row),
                column,
                columnIndex: $xeTable.getColumnIndex(column),
                $columnIndex: $xeTable.getVMColumnIndex(column)
              }, evnt);
            }
          } else {
            const { column: oldColumn } = actived;
            if (mouseConfig) {
              $xeTable.clearSelected();
              if ($xeTable.clearCellAreas) {
                $xeTable.clearCellAreas();
                $xeTable.clearCopyCellArea();
              }
            }
            if (oldColumn !== column) {
              const { model: oldModel } = oldColumn;
              if (oldModel.update) {
                setCellValue(row, oldColumn, oldModel.value);
              }
              if ($xeTable.clearValidate) {
                $xeTable.clearValidate(row, column);
              }
            }
            column.renderHeight = cell.offsetHeight;
            actived.args = params;
            actived.column = column;
            if (isPos) {
              setTimeout(() => {
                $xeTable.handleFocus(params, evnt);
              });
            }
          }
          focused.column = null;
          focused.row = null;
          $xeTable.focus();
        }
      }
      return nextTick();
    };
    const handleEditCell = (row, fieldOrColumn, isPos) => {
      const { editConfig } = props;
      const column = import_xe_utils4.default.isString(fieldOrColumn) ? $xeTable.getColumnByField(fieldOrColumn) : fieldOrColumn;
      if (row && column && isEnableConf(editConfig) && isEnableConf(column.editRender)) {
        return Promise.resolve(isPos ? $xeTable.scrollToRow(row, column) : null).then(() => {
          const cell = $xeTable.getCellElement(row, column);
          if (cell) {
            handleEditActive({
              row,
              rowIndex: $xeTable.getRowIndex(row),
              column,
              columnIndex: $xeTable.getColumnIndex(column),
              cell,
              $table: $xeTable
            }, null, isPos, isPos);
            internalData._lastCallTime = Date.now();
          }
          return nextTick();
        });
      }
      return nextTick();
    };
    editMethods = {
      /**
       * 往表格中插入临时数据
       *
       * @param {*} records
       */
      insert(records) {
        return handleInsertRowAt(records, null);
      },
      /**
       * 往表格指定行中插入临时数据
       * 如果 row 为空则从插入到顶部，如果为树结构，则插入到目标节点顶部
       * 如果 row 为 -1 则从插入到底部，如果为树结构，则插入到目标节点底部
       * 如果 row 为有效行则插入到该行的位置，如果为树结构，则有插入到效的目标节点该行的位置
       * @param {Object/Array} records 新的数据
       * @param {Row} targetRow 指定行
       */
      insertAt(records, targetRow) {
        return handleInsertRowAt(records, targetRow);
      },
      insertNextAt(records, targetRow) {
        return handleInsertRowAt(records, targetRow, true);
      },
      insertChild(records, parentRow) {
        return handleInsertChildRowAt(records, parentRow, null);
      },
      insertChildAt(records, parentRow, targetRow) {
        return handleInsertChildRowAt(records, parentRow, targetRow);
      },
      insertChildNextAt(records, parentRow, targetRow) {
        return handleInsertChildRowAt(records, parentRow, targetRow, true);
      },
      /**
       * 删除指定行数据
       * 如果传 row 则删除一行
       * 如果传 rows 则删除多行
       * 如果为空则删除所有
       */
      remove(rows) {
        const { treeConfig } = props;
        const { editStore } = reactData;
        const { tableFullTreeData, selectCheckboxMaps, afterFullData, mergeBodyList, tableFullData, pendingRowMaps, insertRowMaps, removeRowMaps } = internalData;
        const checkboxOpts = computeCheckboxOpts.value;
        const treeOpts = computeTreeOpts.value;
        const { transform, mapChildrenField } = treeOpts;
        const childrenField = treeOpts.children || treeOpts.childrenField;
        const { actived } = editStore;
        const { checkField } = checkboxOpts;
        let delList = [];
        if (!rows) {
          rows = tableFullData;
        } else if (!import_xe_utils4.default.isArray(rows)) {
          rows = [rows];
        }
        rows.forEach((row) => {
          if (!$xeTable.isInsertByRow(row)) {
            const rowid = getRowid($xeTable, row);
            removeRowMaps[rowid] = row;
          }
        });
        if (!checkField) {
          rows.forEach((row) => {
            const rowid = getRowid($xeTable, row);
            if (selectCheckboxMaps[rowid]) {
              delete selectCheckboxMaps[rowid];
            }
          });
          reactData.updateCheckboxFlag++;
        }
        if (tableFullData === rows) {
          rows = delList = tableFullData.slice(0);
          internalData.tableFullData = [];
          internalData.afterFullData = [];
          $xeTable.clearMergeCells();
        } else {
          if (treeConfig && transform) {
            rows.forEach((row) => {
              const rowid = getRowid($xeTable, row);
              const matchMapObj = import_xe_utils4.default.findTree(tableFullTreeData, (item) => rowid === getRowid($xeTable, item), { children: mapChildrenField });
              if (matchMapObj) {
                const rItems = matchMapObj.items.splice(matchMapObj.index, 1);
                delList.push(rItems[0]);
              }
              const matchObj = import_xe_utils4.default.findTree(tableFullTreeData, (item) => rowid === getRowid($xeTable, item), { children: childrenField });
              if (matchObj) {
                matchObj.items.splice(matchObj.index, 1);
              }
              const afIndex = $xeTable.findRowIndexOf(afterFullData, row);
              if (afIndex > -1) {
                afterFullData.splice(afIndex, 1);
              }
            });
          } else {
            rows.forEach((row) => {
              const tfIndex = $xeTable.findRowIndexOf(tableFullData, row);
              if (tfIndex > -1) {
                const rItems = tableFullData.splice(tfIndex, 1);
                delList.push(rItems[0]);
              }
              const afIndex = $xeTable.findRowIndexOf(afterFullData, row);
              if (afIndex > -1) {
                mergeBodyList.forEach((mergeItem) => {
                  const { row: mergeRowIndex, rowspan: mergeRowspan } = mergeItem;
                  if (mergeRowIndex > afIndex) {
                    mergeItem.row = mergeRowIndex - 1;
                  } else if (mergeRowIndex + mergeRowspan > afIndex) {
                    mergeItem.rowspan = mergeRowspan - 1;
                  }
                });
                afterFullData.splice(afIndex, 1);
              }
            });
          }
        }
        if (actived.row && $xeTable.findRowIndexOf(rows, actived.row) > -1) {
          editMethods.clearEdit();
        }
        rows.forEach((row) => {
          const rowid = getRowid($xeTable, row);
          if (insertRowMaps[rowid]) {
            delete insertRowMaps[rowid];
          }
          if (pendingRowMaps[rowid]) {
            delete pendingRowMaps[rowid];
          }
        });
        reactData.removeRowFlag++;
        reactData.insertRowFlag++;
        reactData.pendingRowFlag++;
        $xeTable.cacheRowMap(false);
        $xeTable.handleTableData(treeConfig && transform);
        $xeTable.updateFooter();
        $xeTable.handleUpdateBodyMerge();
        if (!(treeConfig && transform)) {
          $xeTable.updateAfterDataIndex();
        }
        $xeTable.checkSelectionStatus();
        if (reactData.scrollYLoad) {
          $xeTable.updateScrollYSpace();
        }
        return nextTick().then(() => {
          $xeTable.updateCellAreas();
          return $xeTable.recalculate();
        }).then(() => {
          return { row: delList.length ? delList[delList.length - 1] : null, rows: delList };
        });
      },
      /**
       * 删除复选框选中的数据
       */
      removeCheckboxRow() {
        return editMethods.remove($xeTable.getCheckboxRecords()).then((params) => {
          $xeTable.clearCheckboxRow();
          return params;
        });
      },
      /**
       * 删除单选框选中的数据
       */
      removeRadioRow() {
        const radioRecord = $xeTable.getRadioRecord();
        return editMethods.remove(radioRecord || []).then((params) => {
          $xeTable.clearRadioRow();
          return params;
        });
      },
      /**
       * 删除当前行选中的数据
       */
      removeCurrentRow() {
        const currentRecord = $xeTable.getCurrentRecord();
        return editMethods.remove(currentRecord || []).then((params) => {
          $xeTable.clearCurrentRow();
          return params;
        });
      },
      /**
       * 获取表格数据集，包含新增、删除、修改、标记
       */
      getRecordset() {
        const removeRecords = editMethods.getRemoveRecords();
        const pendingRecords = $xeTable.getPendingRecords();
        const delRecords = removeRecords.concat(pendingRecords);
        const updateRecords = editMethods.getUpdateRecords().filter((row) => {
          return !delRecords.some((item) => $xeTable.eqRow(item, row));
        });
        return {
          insertRecords: editMethods.getInsertRecords(),
          removeRecords,
          updateRecords,
          pendingRecords
        };
      },
      /**
       * 获取新增的临时数据
       */
      getInsertRecords() {
        const { fullAllDataRowIdData, insertRowMaps } = internalData;
        const insertRecords = [];
        import_xe_utils4.default.each(insertRowMaps, (row, rowid) => {
          if (fullAllDataRowIdData[rowid]) {
            insertRecords.push(row);
          }
        });
        return insertRecords;
      },
      /**
       * 获取已删除的数据
       */
      getRemoveRecords() {
        const { removeRowMaps } = internalData;
        const removeRecords = [];
        import_xe_utils4.default.each(removeRowMaps, (row) => {
          removeRecords.push(row);
        });
        return removeRecords;
      },
      /**
       * 获取更新数据
       * 只精准匹配 row 的更改
       * 如果是树表格，子节点更改状态不会影响父节点的更新状态
       */
      getUpdateRecords() {
        const { keepSource, treeConfig } = props;
        const { tableFullData } = internalData;
        const treeOpts = computeTreeOpts.value;
        if (keepSource) {
          syncActivedCell();
          if (treeConfig) {
            return import_xe_utils4.default.filterTree(tableFullData, (row) => $xeTable.isUpdateByRow(row), treeOpts);
          }
          return tableFullData.filter((row) => $xeTable.isUpdateByRow(row));
        }
        return [];
      },
      getActiveRecord() {
        if (true) {
          warnLog("vxe.error.delFunc", ["getActiveRecord", "getEditRecord"]);
        }
        return $xeTable.getEditRecord();
      },
      getEditRecord() {
        const { editStore } = reactData;
        const { afterFullData } = internalData;
        const el = refElem.value;
        const { args, row } = editStore.actived;
        if (args && $xeTable.findRowIndexOf(afterFullData, row) > -1 && el.querySelectorAll(".vxe-body--column.col--active").length) {
          return Object.assign({}, args);
        }
        return null;
      },
      /**
       * 获取选中的单元格
       */
      getSelectedCell() {
        const { editStore } = reactData;
        const { args, column } = editStore.selected;
        if (args && column) {
          return Object.assign({}, args);
        }
        return null;
      },
      clearActived(row) {
        if (true) {
          warnLog("vxe.error.delFunc", ["clearActived", "clearEdit"]);
        }
        return $xeTable.clearEdit(row);
      },
      /**
       * 清除激活的编辑
       */
      clearEdit(row) {
        return handleClearEdit(null, row);
      },
      /**
       * 清除所选中源状态
       */
      clearSelected() {
        const { editStore } = reactData;
        const { selected } = editStore;
        selected.row = null;
        selected.column = null;
        removeCellSelectedClass();
        return nextTick();
      },
      isActiveByRow(row) {
        if (true) {
          warnLog("vxe.error.delFunc", ["isActiveByRow", "isEditByRow"]);
        }
        return $xeTable.isEditByRow(row);
      },
      /**
       * 判断行是否为激活编辑状态
       * @param {Row} row 行对象
       */
      isEditByRow(row) {
        const { editStore } = reactData;
        return editStore.actived.row === row;
      },
      setActiveRow(row) {
        if (true) {
          warnLog("vxe.error.delFunc", ["setActiveRow", "setEditRow"]);
        }
        return editMethods.setEditRow(row);
      },
      /**
       * 激活行编辑
       */
      setEditRow(row, fieldOrColumn) {
        const { visibleColumn } = internalData;
        let column = import_xe_utils4.default.find(visibleColumn, (column2) => isEnableConf(column2.editRender));
        let isPos = false;
        if (fieldOrColumn) {
          isPos = true;
          if (fieldOrColumn !== true) {
            column = import_xe_utils4.default.isString(fieldOrColumn) ? $xeTable.getColumnByField(fieldOrColumn) : fieldOrColumn;
          }
        }
        return handleEditCell(row, column, isPos);
      },
      setActiveCell(row, fieldOrColumn) {
        if (true) {
          warnLog("vxe.error.delFunc", ["setActiveCell", "setEditCell"]);
        }
        return editMethods.setEditCell(row, fieldOrColumn);
      },
      /**
       * 激活单元格编辑
       */
      setEditCell(row, fieldOrColumn) {
        return handleEditCell(row, fieldOrColumn, true);
      },
      /**
       * 只对 trigger=dblclick 有效，选中单元格
       */
      setSelectCell(row, fieldOrColumn) {
        const { tableData } = reactData;
        const editOpts = computeEditOpts.value;
        const column = import_xe_utils4.default.isString(fieldOrColumn) ? $xeTable.getColumnByField(fieldOrColumn) : fieldOrColumn;
        if (row && column && editOpts.trigger !== "manual") {
          const rowIndex = $xeTable.findRowIndexOf(tableData, row);
          if (rowIndex > -1 && column) {
            const cell = $xeTable.getCellElement(row, column);
            const params = {
              row,
              rowIndex,
              column,
              columnIndex: $xeTable.getColumnIndex(column),
              cell
            };
            $xeTable.handleSelected(params, {});
          }
        }
        return nextTick();
      }
    };
    editPrivateMethods = {
      /**
       * 处理激活编辑
       */
      handleEdit(params, evnt) {
        return handleEditActive(params, evnt, true, true);
      },
      /**
       * @deprecated
       */
      handleActived(params, evnt) {
        return editPrivateMethods.handleEdit(params, evnt);
      },
      /**
       * 处理取消编辑
       * @param evnt
       * @returns
       */
      handleClearEdit,
      /**
       * 处理聚焦
       */
      handleFocus(params) {
        const { row, column, cell } = params;
        const { editRender } = column;
        const editOpts = computeEditOpts.value;
        if (isEnableConf(editRender)) {
          const compRender = renderer2.get(editRender.name);
          let autoFocus = editRender.autofocus || editRender.autoFocus;
          let autoSelect = editRender.autoSelect || editRender.autoselect;
          let inputElem;
          if (editOpts.autoFocus) {
            if (!autoFocus && compRender) {
              autoFocus = compRender.tableAutoFocus || compRender.tableAutofocus || compRender.autofocus;
            }
            if (!autoSelect && compRender) {
              autoSelect = compRender.tableAutoSelect || compRender.autoselect;
            }
            if (import_xe_utils4.default.isFunction(autoFocus)) {
              inputElem = autoFocus(params);
            } else if (autoFocus) {
              if (autoFocus === true) {
                inputElem = cell.querySelector("input,textarea");
              } else {
                inputElem = cell.querySelector(autoFocus);
              }
              if (inputElem) {
                inputElem.focus();
              }
            }
          }
          if (inputElem) {
            if (autoSelect) {
              inputElem.select();
            } else {
              if (browseObj2.msie) {
                const textRange = inputElem.createTextRange();
                textRange.collapse(false);
                textRange.select();
              }
            }
          } else {
            if (editOpts.autoPos) {
              if (!column.fixed) {
                $xeTable.scrollToRow(row, column);
              }
            }
          }
        }
      },
      /**
       * 处理选中源
       */
      handleSelected(params, evnt) {
        const { mouseConfig } = props;
        const { editStore } = reactData;
        const mouseOpts = computeMouseOpts.value;
        const editOpts = computeEditOpts.value;
        const { actived, selected } = editStore;
        const { row, column } = params;
        const isMouseSelected = mouseConfig && mouseOpts.selected;
        const selectMethod = () => {
          if (isMouseSelected && (selected.row !== row || selected.column !== column)) {
            if (actived.row !== row || (editOpts.mode === "cell" ? actived.column !== column : false)) {
              handleClearEdit(evnt);
              $xeTable.clearSelected();
              if ($xeTable.clearCellAreas) {
                $xeTable.clearCellAreas();
                $xeTable.clearCopyCellArea();
              }
              selected.args = params;
              selected.row = row;
              selected.column = column;
              if (isMouseSelected) {
                editPrivateMethods.addCellSelectedClass();
              }
              $xeTable.focus();
              if (evnt) {
                $xeTable.dispatchEvent("cell-selected", params, evnt);
              }
            }
          }
          return nextTick();
        };
        return selectMethod();
      },
      addCellSelectedClass() {
        const { editStore } = reactData;
        const { selected } = editStore;
        const { row, column } = selected;
        removeCellSelectedClass();
        if (row && column) {
          const cell = $xeTable.getCellElement(row, column);
          if (cell) {
            addClass(cell, "col--selected");
          }
        }
      }
    };
    return Object.assign(Object.assign({}, editMethods), editPrivateMethods);
  },
  setupGrid($xeGrid) {
    return $xeGrid.extendTableMethods(tableEditMethodKeys);
  }
});

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/module/export/hook.js
var import_xe_utils5 = __toESM(require_xe_utils());

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/module/export/util.js
var defaultHtmlStyle = 'body{margin:0;padding: 0 1px;color:#333333;font-size:14px;font-family:"Microsoft YaHei",微软雅黑,"MicrosoftJhengHei",华文细黑,STHeiti,MingLiu}body *{-webkit-box-sizing:border-box;box-sizing:border-box}.vxe-table{border-collapse:collapse;text-align:left;border-spacing:0}.vxe-table:not(.is--print){table-layout:fixed}.vxe-table,.vxe-table th,.vxe-table td,.vxe-table td{border-color:#D0D0D0;border-style:solid;border-width:0}.vxe-table.is--print{width:100%}.border--default,.border--full,.border--outer{border-top-width:1px}.border--default,.border--full,.border--outer{border-left-width:1px}.border--outer,.border--default th,.border--default td,.border--full th,.border--full td,.border--outer th,.border--inner th,.border--inner td{border-bottom-width:1px}.border--default,.border--outer,.border--full th,.border--full td{border-right-width:1px}.border--default th,.border--full th,.border--outer th{background-color:#f8f8f9}.vxe-table td>div,.vxe-table th>div{padding:.5em .4em}.col--center{text-align:center}.col--right{text-align:right}.vxe-table:not(.is--print) .col--ellipsis>div{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-break:break-all}.vxe-table--tree-node{text-align:left}.vxe-table--tree-node-wrapper{position:relative}.vxe-table--tree-icon-wrapper{position:absolute;top:50%;width:1em;height:1em;text-align:center;-webkit-transform:translateY(-50%);transform:translateY(-50%);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer}.vxe-table--tree-unfold-icon,.vxe-table--tree-fold-icon{position:absolute;width:0;height:0;border-style:solid;border-width:.5em;border-right-color:transparent;border-bottom-color:transparent}.vxe-table--tree-unfold-icon{left:.3em;top:0;border-left-color:#939599;border-top-color:transparent}.vxe-table--tree-fold-icon{left:0;top:.3em;border-left-color:transparent;border-top-color:#939599}.vxe-table--tree-cell{display:block;padding-left:1.5em}.vxe-table input[type="checkbox"]{margin:0}.vxe-table input[type="checkbox"],.vxe-table input[type="radio"],.vxe-table input[type="checkbox"]+span,.vxe-table input[type="radio"]+span{vertical-align:middle;padding-left:0.4em}';
function getExportBlobByContent(content, options) {
  return new Blob([content], { type: `text/${options.type};charset=utf-8;` });
}
function createHtmlPage(opts, content) {
  const { style } = opts;
  return [
    "<!DOCTYPE html><html>",
    "<head>",
    '<meta charset="utf-8"><meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,minimal-ui">',
    `<title>${opts.sheetName}</title>`,
    '<style media="print">.vxe-page-break-before{page-break-before:always;}.vxe-page-break-after{page-break-after:always;}</style>',
    `<style>${defaultHtmlStyle}</style>`,
    style ? `<style>${style}</style>` : "",
    "</head>",
    `<body>${content}</body>`,
    "</html>"
  ].join("");
}

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/module/export/hook.js
var { getI18n: getI18n2, hooks: hooks4, renderer: renderer3 } = VxeUI;
var htmlCellElem;
var csvBOM = "\uFEFF";
var enterSymbol = "\r\n";
function defaultFilterExportColumn(column) {
  return column.field || ["seq", "checkbox", "radio"].indexOf(column.type) > -1;
}
var getConvertColumns = (columns) => {
  const result = [];
  columns.forEach((column) => {
    if (column.childNodes && column.childNodes.length) {
      result.push(column);
      result.push(...getConvertColumns(column.childNodes));
    } else {
      result.push(column);
    }
  });
  return result;
};
var convertToRows = (originColumns) => {
  let maxLevel = 1;
  const traverse = (column, parent) => {
    if (parent) {
      column._level = parent._level + 1;
      if (maxLevel < column._level) {
        maxLevel = column._level;
      }
    }
    if (column.childNodes && column.childNodes.length) {
      let colSpan = 0;
      column.childNodes.forEach((subColumn) => {
        traverse(subColumn, column);
        colSpan += subColumn._colSpan;
      });
      column._colSpan = colSpan;
    } else {
      column._colSpan = 1;
    }
  };
  originColumns.forEach((column) => {
    column._level = 1;
    traverse(column);
  });
  const rows = [];
  for (let i = 0; i < maxLevel; i++) {
    rows.push([]);
  }
  const allColumns = getConvertColumns(originColumns);
  allColumns.forEach((column) => {
    if (column.childNodes && column.childNodes.length) {
      column._rowSpan = 1;
    } else {
      column._rowSpan = maxLevel - column._level + 1;
    }
    rows[column._level - 1].push(column);
  });
  return rows;
};
function toTableBorder(border) {
  if (border === true) {
    return "full";
  }
  if (border) {
    return border;
  }
  return "default";
}
function getBooleanValue(cellValue) {
  return cellValue === "TRUE" || cellValue === "true" || cellValue === true;
}
function getFooterData($xeTable, opts, footerTableData) {
  const { footerFilterMethod } = opts;
  return footerFilterMethod ? footerTableData.filter((items, index) => footerFilterMethod({ $table: $xeTable, items, $rowIndex: index })) : footerTableData;
}
function getCsvCellTypeLabel(column, cellValue) {
  if (cellValue) {
    if (column.type === "seq") {
      return `	${cellValue}`;
    }
    switch (column.cellType) {
      case "string":
        if (!isNaN(cellValue)) {
          return `	${cellValue}`;
        }
        break;
      case "number":
        break;
      default:
        if (cellValue.length >= 12 && !isNaN(cellValue)) {
          return `	${cellValue}`;
        }
        break;
    }
  }
  return cellValue;
}
function toTxtCellLabel(val) {
  if (/[",\s\n]/.test(val)) {
    return `"${val.replace(/"/g, '""')}"`;
  }
  return val;
}
function getElementsByTagName(elem, qualifiedName) {
  return elem.getElementsByTagName(qualifiedName);
}
function getTxtCellKey(now) {
  return `#${now}@${import_xe_utils5.default.uniqueId()}`;
}
function replaceTxtCell(cell, vMaps) {
  return cell.replace(/#\d+@\d+/g, (key) => import_xe_utils5.default.hasOwnProp(vMaps, key) ? vMaps[key] : key);
}
function getTxtCellValue(val, vMaps) {
  const rest = replaceTxtCell(val, vMaps);
  return rest.replace(/^"+$/g, (qVal) => '"'.repeat(Math.ceil(qVal.length / 2)));
}
function toExportField(tableConf, field) {
  const { fieldMaps, titleMaps } = tableConf;
  if (!fieldMaps[field]) {
    const teCol = titleMaps[field];
    if (teCol && teCol.field) {
      field = teCol.field;
    }
  }
  return field;
}
function parseCsvAndTxt(tableConf, content, cellSeparator) {
  const list = content.split(enterSymbol);
  const rows = [];
  let fields = [];
  if (list.length) {
    const vMaps = {};
    const now = Date.now();
    list.forEach((rVal) => {
      if (rVal) {
        const item = {};
        rVal = rVal.replace(/("")|(\n)/g, (text, dVal) => {
          const key = getTxtCellKey(now);
          vMaps[key] = dVal ? '"' : "\n";
          return key;
        }).replace(/"(.*?)"/g, (text, cVal) => {
          const key = getTxtCellKey(now);
          vMaps[key] = replaceTxtCell(cVal, vMaps);
          return key;
        });
        const cells = rVal.split(cellSeparator);
        if (!fields.length) {
          fields = cells.map((val) => toExportField(tableConf, getTxtCellValue(val.trim(), vMaps)));
        } else {
          cells.forEach((val, colIndex) => {
            if (colIndex < fields.length) {
              item[fields[colIndex]] = getTxtCellValue(val.trim(), vMaps);
            }
          });
          rows.push(item);
        }
      }
    });
  }
  return { fields, rows };
}
function parseCsv(tableConf, content) {
  return parseCsvAndTxt(tableConf, content, ",");
}
function parseTxt(tableConf, content) {
  return parseCsvAndTxt(tableConf, content, "	");
}
function parseHTML(tableConf, content) {
  const domParser = new DOMParser();
  const xmlDoc = domParser.parseFromString(content, "text/html");
  const bodyNodes = getElementsByTagName(xmlDoc, "body");
  const rows = [];
  const fields = [];
  if (bodyNodes.length) {
    const tableNodes = getElementsByTagName(bodyNodes[0], "table");
    if (tableNodes.length) {
      const theadNodes = getElementsByTagName(tableNodes[0], "thead");
      if (theadNodes.length) {
        import_xe_utils5.default.arrayEach(getElementsByTagName(theadNodes[0], "tr"), (rowNode) => {
          import_xe_utils5.default.arrayEach(getElementsByTagName(rowNode, "th"), (cellNode) => {
            fields.push(toExportField(tableConf, cellNode.textContent || ""));
          });
        });
        const tbodyNodes = getElementsByTagName(tableNodes[0], "tbody");
        if (tbodyNodes.length) {
          import_xe_utils5.default.arrayEach(getElementsByTagName(tbodyNodes[0], "tr"), (rowNode) => {
            const item = {};
            import_xe_utils5.default.arrayEach(getElementsByTagName(rowNode, "td"), (cellNode, colIndex) => {
              if (fields[colIndex]) {
                item[fields[colIndex]] = cellNode.textContent || "";
              }
            });
            rows.push(item);
          });
        }
      }
    }
  }
  return { fields, rows };
}
function parseXML(tableConf, content) {
  const domParser = new DOMParser();
  const xmlDoc = domParser.parseFromString(content, "application/xml");
  const sheetNodes = getElementsByTagName(xmlDoc, "Worksheet");
  const rows = [];
  const fields = [];
  if (sheetNodes.length) {
    const tableNodes = getElementsByTagName(sheetNodes[0], "Table");
    if (tableNodes.length) {
      const rowNodes = getElementsByTagName(tableNodes[0], "Row");
      if (rowNodes.length) {
        import_xe_utils5.default.arrayEach(getElementsByTagName(rowNodes[0], "Cell"), (cellNode) => {
          fields.push(toExportField(tableConf, cellNode.textContent || ""));
        });
        import_xe_utils5.default.arrayEach(rowNodes, (rowNode, index) => {
          if (index) {
            const item = {};
            const cellNodes = getElementsByTagName(rowNode, "Cell");
            import_xe_utils5.default.arrayEach(cellNodes, (cellNode, colIndex) => {
              if (fields[colIndex]) {
                item[fields[colIndex]] = cellNode.textContent;
              }
            });
            rows.push(item);
          }
        });
      }
    }
  }
  return { fields, rows };
}
function clearColumnConvert(columns) {
  import_xe_utils5.default.eachTree(columns, (column) => {
    delete column._level;
    delete column._colSpan;
    delete column._rowSpan;
    delete column._children;
    delete column.childNodes;
  }, { children: "children" });
}
var tableExportMethodKeys = ["exportData", "importByFile", "importData", "saveFile", "readFile", "print", "getPrintHtml", "openImport", "closeImport", "openExport", "closeExport", "openPrint", "closePrint"];
hooks4.add("tableExportModule", {
  setupTable($xeTable) {
    const { props, reactData, internalData } = $xeTable;
    const { computeTreeOpts, computePrintOpts, computeExportOpts, computeImportOpts, computeCustomOpts, computeSeqOpts, computeRadioOpts, computeCheckboxOpts, computeColumnOpts } = $xeTable.getComputeMaps();
    const $xeGrid = inject("$xeGrid", null);
    const hasTreeChildren = (row) => {
      const treeOpts = computeTreeOpts.value;
      const childrenField = treeOpts.children || treeOpts.childrenField;
      return row[childrenField] && row[childrenField].length;
    };
    const getSeq = (cellValue, row, $rowIndex, column, $columnIndex) => {
      const seqOpts = computeSeqOpts.value;
      const seqMethod = seqOpts.seqMethod || column.seqMethod;
      if (seqMethod) {
        return seqMethod({
          $table: $xeTable,
          row,
          rowIndex: $xeTable.getRowIndex(row),
          $rowIndex,
          column,
          columnIndex: $xeTable.getColumnIndex(column),
          $columnIndex
        });
      }
      return cellValue;
    };
    function getHeaderTitle(opts, column) {
      const columnOpts = computeColumnOpts.value;
      const headExportMethod = column.headerExportMethod || columnOpts.headerExportMethod;
      return headExportMethod ? headExportMethod({ column, options: opts, $table: $xeTable }) : (opts.isTitle ? column.getTitle() : column.field) || "";
    }
    const toBooleanValue = (cellValue) => {
      return import_xe_utils5.default.isBoolean(cellValue) ? cellValue ? "TRUE" : "FALSE" : cellValue;
    };
    const toStringValue = (cellValue) => {
      return eqEmptyValue(cellValue) ? "" : `${cellValue}`;
    };
    const getBodyLabelData = (opts, columns, datas) => {
      const { isAllExpand, mode } = opts;
      const { treeConfig } = props;
      const radioOpts = computeRadioOpts.value;
      const checkboxOpts = computeCheckboxOpts.value;
      const treeOpts = computeTreeOpts.value;
      const columnOpts = computeColumnOpts.value;
      if (!htmlCellElem) {
        htmlCellElem = document.createElement("div");
      }
      if (treeConfig) {
        const childrenField = treeOpts.children || treeOpts.childrenField;
        const rest = [];
        const expandMaps = /* @__PURE__ */ new Map();
        import_xe_utils5.default.eachTree(datas, (item, $rowIndex, items, path, parent, nodes) => {
          const row = item._row || item;
          const parentRow = parent && parent._row ? parent._row : parent;
          if (isAllExpand || !parentRow || expandMaps.has(parentRow) && $xeTable.isTreeExpandByRow(parentRow)) {
            const hasRowChild = hasTreeChildren(row);
            const item2 = {
              _row: row,
              _level: nodes.length - 1,
              _hasChild: hasRowChild,
              _expand: hasRowChild && $xeTable.isTreeExpandByRow(row)
            };
            columns.forEach((column, $columnIndex) => {
              let cellValue = "";
              const renderOpts = column.editRender || column.cellRender;
              let bodyExportMethod = column.exportMethod || columnOpts.exportMethod;
              if (!bodyExportMethod && renderOpts && renderOpts.name) {
                const compConf = renderer3.get(renderOpts.name);
                if (compConf) {
                  bodyExportMethod = compConf.tableExportMethod || compConf.exportMethod;
                }
              }
              if (!bodyExportMethod) {
                bodyExportMethod = columnOpts.exportMethod;
              }
              if (bodyExportMethod) {
                cellValue = bodyExportMethod({ $table: $xeTable, row, column, options: opts });
              } else {
                switch (column.type) {
                  case "seq": {
                    const seqVal = path.map((num, i) => i % 2 === 0 ? Number(num) + 1 : ".").join("");
                    cellValue = mode === "all" ? seqVal : getSeq(seqVal, row, $rowIndex, column, $columnIndex);
                    break;
                  }
                  case "checkbox":
                    cellValue = toBooleanValue($xeTable.isCheckedByCheckboxRow(row));
                    item2._checkboxLabel = checkboxOpts.labelField ? import_xe_utils5.default.get(row, checkboxOpts.labelField) : "";
                    item2._checkboxDisabled = checkboxOpts.checkMethod && !checkboxOpts.checkMethod({ $table: $xeTable, row });
                    break;
                  case "radio":
                    cellValue = toBooleanValue($xeTable.isCheckedByRadioRow(row));
                    item2._radioLabel = radioOpts.labelField ? import_xe_utils5.default.get(row, radioOpts.labelField) : "";
                    item2._radioDisabled = radioOpts.checkMethod && !radioOpts.checkMethod({ $table: $xeTable, row });
                    break;
                  default:
                    if (opts.original) {
                      cellValue = getCellValue(row, column);
                    } else {
                      cellValue = $xeTable.getCellLabel(row, column);
                      if (column.type === "html") {
                        htmlCellElem.innerHTML = cellValue;
                        cellValue = htmlCellElem.innerText.trim();
                      } else {
                        const cell = $xeTable.getCellElement(row, column);
                        if (cell && !hasClass(cell, "is--progress")) {
                          cellValue = cell.innerText.trim();
                        }
                      }
                    }
                }
              }
              item2[column.id] = toStringValue(cellValue);
            });
            expandMaps.set(row, 1);
            rest.push(Object.assign(item2, row));
          }
        }, { children: childrenField });
        return rest;
      }
      return datas.map((row, $rowIndex) => {
        const item = {
          _row: row
        };
        columns.forEach((column, $columnIndex) => {
          let cellValue = "";
          const renderOpts = column.editRender || column.cellRender;
          let bodyExportMethod = column.exportMethod || columnOpts.exportMethod;
          if (!bodyExportMethod && renderOpts && renderOpts.name) {
            const compConf = renderer3.get(renderOpts.name);
            if (compConf) {
              bodyExportMethod = compConf.tableExportMethod || compConf.exportMethod;
            }
          }
          if (bodyExportMethod) {
            cellValue = bodyExportMethod({ $table: $xeTable, row, column, options: opts });
          } else {
            switch (column.type) {
              case "seq": {
                const seqValue = $rowIndex + 1;
                cellValue = mode === "all" ? seqValue : getSeq(seqValue, row, $rowIndex, column, $columnIndex);
                break;
              }
              case "checkbox":
                cellValue = toBooleanValue($xeTable.isCheckedByCheckboxRow(row));
                item._checkboxLabel = checkboxOpts.labelField ? import_xe_utils5.default.get(row, checkboxOpts.labelField) : "";
                item._checkboxDisabled = checkboxOpts.checkMethod && !checkboxOpts.checkMethod({ $table: $xeTable, row });
                break;
              case "radio":
                cellValue = toBooleanValue($xeTable.isCheckedByRadioRow(row));
                item._radioLabel = radioOpts.labelField ? import_xe_utils5.default.get(row, radioOpts.labelField) : "";
                item._radioDisabled = radioOpts.checkMethod && !radioOpts.checkMethod({ $table: $xeTable, row });
                break;
              default:
                if (opts.original) {
                  cellValue = getCellValue(row, column);
                } else {
                  cellValue = $xeTable.getCellLabel(row, column);
                  if (column.type === "html") {
                    htmlCellElem.innerHTML = cellValue;
                    cellValue = htmlCellElem.innerText.trim();
                  } else {
                    const cell = $xeTable.getCellElement(row, column);
                    if (cell && !hasClass(cell, "is--progress")) {
                      cellValue = cell.innerText.trim();
                    }
                  }
                }
            }
          }
          item[column.id] = toStringValue(cellValue);
        });
        return item;
      });
    };
    const getExportData = (opts) => {
      const { columns, dataFilterMethod } = opts;
      let datas = opts.data;
      if (dataFilterMethod) {
        datas = datas.filter((row, index) => dataFilterMethod({ $table: $xeTable, row, $rowIndex: index }));
      }
      return getBodyLabelData(opts, columns, datas);
    };
    const getFooterCellValue = (opts, row, column) => {
      const columnOpts = computeColumnOpts.value;
      const renderOpts = column.editRender || column.cellRender;
      let footLabelMethod = column.footerExportMethod;
      if (!footLabelMethod && renderOpts && renderOpts.name) {
        const compConf = renderer3.get(renderOpts.name);
        if (compConf) {
          footLabelMethod = compConf.tableFooterExportMethod || compConf.footerExportMethod;
        }
      }
      if (!footLabelMethod) {
        footLabelMethod = columnOpts.footerExportMethod;
      }
      const _columnIndex = $xeTable.getVTColumnIndex(column);
      if (footLabelMethod) {
        return footLabelMethod({ $table: $xeTable, items: row, itemIndex: _columnIndex, row, _columnIndex, column, options: opts });
      }
      if (import_xe_utils5.default.isArray(row)) {
        return import_xe_utils5.default.toValueString(row[_columnIndex]);
      }
      return import_xe_utils5.default.get(row, column.field);
    };
    const toCsv = ($xeTable2, opts, columns, datas) => {
      let content = csvBOM;
      if (opts.isHeader) {
        content += columns.map((column) => toTxtCellLabel(getHeaderTitle(opts, column))).join(",") + enterSymbol;
      }
      datas.forEach((row) => {
        content += columns.map((column) => toTxtCellLabel(getCsvCellTypeLabel(column, row[column.id]))).join(",") + enterSymbol;
      });
      if (opts.isFooter) {
        const { footerTableData } = reactData;
        const footers = getFooterData($xeTable2, opts, footerTableData);
        footers.forEach((row) => {
          content += columns.map((column) => toTxtCellLabel(getFooterCellValue(opts, row, column))).join(",") + enterSymbol;
        });
      }
      return content;
    };
    const toTxt = ($xeTable2, opts, columns, datas) => {
      let content = "";
      if (opts.isHeader) {
        content += columns.map((column) => toTxtCellLabel(getHeaderTitle(opts, column))).join("	") + enterSymbol;
      }
      datas.forEach((row) => {
        content += columns.map((column) => toTxtCellLabel(row[column.id])).join("	") + enterSymbol;
      });
      if (opts.isFooter) {
        const { footerTableData } = reactData;
        const footers = getFooterData($xeTable2, opts, footerTableData);
        footers.forEach((row) => {
          content += columns.map((column) => toTxtCellLabel(getFooterCellValue(opts, row, column))).join("	") + enterSymbol;
        });
      }
      return content;
    };
    const hasEllipsis = (column, property, allColumnOverflow) => {
      const columnOverflow = column[property];
      const headOverflow = import_xe_utils5.default.isUndefined(columnOverflow) || import_xe_utils5.default.isNull(columnOverflow) ? allColumnOverflow : columnOverflow;
      const showEllipsis = headOverflow === "ellipsis";
      const showTitle = headOverflow === "title";
      const showTooltip = headOverflow === true || headOverflow === "tooltip";
      let isEllipsis = showTitle || showTooltip || showEllipsis;
      const { scrollXLoad, scrollYLoad } = reactData;
      if ((scrollXLoad || scrollYLoad) && !isEllipsis) {
        isEllipsis = true;
      }
      return isEllipsis;
    };
    const toHtml = (opts, columns, datas) => {
      const { id, border, treeConfig, headerAlign: allHeaderAlign, align: allAlign, footerAlign: allFooterAlign, showOverflow: allColumnOverflow, showHeaderOverflow: allColumnHeaderOverflow } = props;
      const { isAllSelected, isIndeterminate } = reactData;
      const { mergeBodyCellMaps } = internalData;
      const treeOpts = computeTreeOpts.value;
      const { print: isPrint, isHeader, isFooter, isColgroup, isMerge, colgroups, original } = opts;
      const allCls = "check-all";
      const clss = [
        "vxe-table",
        `border--${toTableBorder(border)}`,
        isPrint ? "is--print" : "",
        isHeader ? "is--header" : ""
      ].filter((cls) => cls);
      const tables = [
        `<table class="${clss.join(" ")}" border="0" cellspacing="0" cellpadding="0">`,
        `<colgroup>${columns.map((column) => `<col style="width:${column.renderWidth}px">`).join("")}</colgroup>`
      ];
      if (isHeader) {
        tables.push("<thead>");
        if (isColgroup && !original) {
          colgroups.forEach((cols) => {
            tables.push(`<tr>${cols.map((column) => {
              const headAlign = column.headerAlign || column.align || allHeaderAlign || allAlign;
              const classNames = hasEllipsis(column, "showHeaderOverflow", allColumnHeaderOverflow) ? ["col--ellipsis"] : [];
              const cellTitle = getHeaderTitle(opts, column);
              let childWidth = 0;
              let countChild = 0;
              import_xe_utils5.default.eachTree([column], (item) => {
                if (!item.childNodes || !column.childNodes.length) {
                  countChild++;
                }
                childWidth += item.renderWidth;
              }, { children: "childNodes" });
              const cellWidth = childWidth - countChild;
              if (headAlign) {
                classNames.push(`col--${headAlign}`);
              }
              if (column.type === "checkbox") {
                return `<th class="${classNames.join(" ")}" colspan="${column._colSpan}" rowspan="${column._rowSpan}"><div ${isPrint ? "" : `style="width: ${cellWidth}px"`}><input type="checkbox" class="${allCls}" ${isAllSelected ? "checked" : ""}><span>${cellTitle}</span></div></th>`;
              }
              return `<th class="${classNames.join(" ")}" colspan="${column._colSpan}" rowspan="${column._rowSpan}" title="${cellTitle}"><div ${isPrint ? "" : `style="width: ${cellWidth}px"`}><span>${formatText(cellTitle, true)}</span></div></th>`;
            }).join("")}</tr>`);
          });
        } else {
          tables.push(`<tr>${columns.map((column) => {
            const headAlign = column.headerAlign || column.align || allHeaderAlign || allAlign;
            const classNames = hasEllipsis(column, "showHeaderOverflow", allColumnHeaderOverflow) ? ["col--ellipsis"] : [];
            const cellTitle = getHeaderTitle(opts, column);
            if (headAlign) {
              classNames.push(`col--${headAlign}`);
            }
            if (column.type === "checkbox") {
              return `<th class="${classNames.join(" ")}"><div ${isPrint ? "" : `style="width: ${column.renderWidth}px"`}><input type="checkbox" class="${allCls}" ${isAllSelected ? "checked" : ""}><span>${cellTitle}</span></div></th>`;
            }
            return `<th class="${classNames.join(" ")}" title="${cellTitle}"><div ${isPrint ? "" : `style="width: ${column.renderWidth}px"`}><span>${formatText(cellTitle, true)}</span></div></th>`;
          }).join("")}</tr>`);
        }
        tables.push("</thead>");
      }
      if (datas.length) {
        tables.push("<tbody>");
        if (treeConfig) {
          datas.forEach((item) => {
            tables.push("<tr>" + columns.map((column) => {
              const colid = column.id;
              const cellAlign = column.align || allAlign;
              const classNames = hasEllipsis(column, "showOverflow", allColumnOverflow) ? ["col--ellipsis"] : [];
              const cellValue = item[colid];
              if (cellAlign) {
                classNames.push(`col--${cellAlign}`);
              }
              if (column.treeNode) {
                let treeIcon = "";
                if (item._hasChild) {
                  treeIcon = `<i class="${item._expand ? "vxe-table--tree-fold-icon" : "vxe-table--tree-unfold-icon"}"></i>`;
                }
                classNames.push("vxe-table--tree-node");
                if (column.type === "radio") {
                  return `<td class="${classNames.join(" ")}" title="${cellValue}"><div ${isPrint ? "" : `style="width: ${column.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${item._level * treeOpts.indent}px"><div class="vxe-table--tree-icon-wrapper">${treeIcon}</div><div class="vxe-table--tree-cell"><input type="radio" name="radio_${id}" ${item._radioDisabled ? "disabled " : ""}${getBooleanValue(cellValue) ? "checked" : ""}><span>${item._radioLabel}</span></div></div></div></td>`;
                } else if (column.type === "checkbox") {
                  return `<td class="${classNames.join(" ")}" title="${cellValue}"><div ${isPrint ? "" : `style="width: ${column.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${item._level * treeOpts.indent}px"><div class="vxe-table--tree-icon-wrapper">${treeIcon}</div><div class="vxe-table--tree-cell"><input type="checkbox" ${item._checkboxDisabled ? "disabled " : ""}${getBooleanValue(cellValue) ? "checked" : ""}><span>${item._checkboxLabel}</span></div></div></div></td>`;
                }
                return `<td class="${classNames.join(" ")}" title="${cellValue}"><div ${isPrint ? "" : `style="width: ${column.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${item._level * treeOpts.indent}px"><div class="vxe-table--tree-icon-wrapper">${treeIcon}</div><div class="vxe-table--tree-cell">${cellValue}</div></div></div></td>`;
              }
              if (column.type === "radio") {
                return `<td class="${classNames.join(" ")}"><div ${isPrint ? "" : `style="width: ${column.renderWidth}px"`}><input type="radio" name="radio_${id}" ${item._radioDisabled ? "disabled " : ""}${getBooleanValue(cellValue) ? "checked" : ""}><span>${item._radioLabel}</span></div></td>`;
              } else if (column.type === "checkbox") {
                return `<td class="${classNames.join(" ")}"><div ${isPrint ? "" : `style="width: ${column.renderWidth}px"`}><input type="checkbox" ${item._checkboxDisabled ? "disabled " : ""}${getBooleanValue(cellValue) ? "checked" : ""}><span>${item._checkboxLabel}</span></div></td>`;
              }
              return `<td class="${classNames.join(" ")}" title="${cellValue}"><div ${isPrint ? "" : `style="width: ${column.renderWidth}px"`}>${formatText(cellValue, true)}</div></td>`;
            }).join("") + "</tr>");
          });
        } else {
          datas.forEach((item) => {
            tables.push("<tr>" + columns.map((column) => {
              const cellAlign = column.align || allAlign;
              const classNames = hasEllipsis(column, "showOverflow", allColumnOverflow) ? ["col--ellipsis"] : [];
              const cellValue = item[column.id];
              let rowSpan = 1;
              let colSpan = 1;
              if (isMerge) {
                const _rowIndex = $xeTable.getVTRowIndex(item._row);
                const _columnIndex = $xeTable.getVTColumnIndex(column);
                const spanRest = mergeBodyCellMaps[`${_rowIndex}:${_columnIndex}`];
                if (spanRest) {
                  const { rowspan, colspan } = spanRest;
                  if (!rowspan || !colspan) {
                    return "";
                  }
                  if (rowspan > 1) {
                    rowSpan = rowspan;
                  }
                  if (colspan > 1) {
                    colSpan = colspan;
                  }
                }
              }
              if (cellAlign) {
                classNames.push(`col--${cellAlign}`);
              }
              if (column.type === "radio") {
                return `<td class="${classNames.join(" ")}" rowspan="${rowSpan}" colspan="${colSpan}"><div ${isPrint ? "" : `style="width: ${column.renderWidth}px"`}><input type="radio" name="radio_${id}" ${item._radioDisabled ? "disabled " : ""}${getBooleanValue(cellValue) ? "checked" : ""}><span>${item._radioLabel}</span></div></td>`;
              } else if (column.type === "checkbox") {
                return `<td class="${classNames.join(" ")}" rowspan="${rowSpan}" colspan="${colSpan}"><div ${isPrint ? "" : `style="width: ${column.renderWidth}px"`}><input type="checkbox" ${item._checkboxDisabled ? "disabled " : ""}${getBooleanValue(cellValue) ? "checked" : ""}><span>${item._checkboxLabel}</span></div></td>`;
              }
              return `<td class="${classNames.join(" ")}" rowspan="${rowSpan}" colspan="${colSpan}" title="${cellValue}"><div ${isPrint ? "" : `style="width: ${column.renderWidth}px"`}>${formatText(cellValue, true)}</div></td>`;
            }).join("") + "</tr>");
          });
        }
        tables.push("</tbody>");
      }
      if (isFooter) {
        const { footerTableData } = reactData;
        const footers = getFooterData($xeTable, opts, footerTableData);
        if (footers.length) {
          tables.push("<tfoot>");
          footers.forEach((row) => {
            tables.push(`<tr>${columns.map((column) => {
              const footAlign = column.footerAlign || column.align || allFooterAlign || allAlign;
              const classNames = hasEllipsis(column, "showOverflow", allColumnOverflow) ? ["col--ellipsis"] : [];
              const cellValue = getFooterCellValue(opts, row, column);
              if (footAlign) {
                classNames.push(`col--${footAlign}`);
              }
              return `<td class="${classNames.join(" ")}" title="${cellValue}"><div ${isPrint ? "" : `style="width: ${column.renderWidth}px"`}>${formatText(cellValue, true)}</div></td>`;
            }).join("")}</tr>`);
          });
          tables.push("</tfoot>");
        }
      }
      const script = !isAllSelected && isIndeterminate ? `<script>(function(){var a=document.querySelector(".${allCls}");if(a){a.indeterminate=true}})()<\/script>` : "";
      tables.push("</table>", script);
      return isPrint ? tables.join("") : createHtmlPage(opts, tables.join(""));
    };
    const toXML = (opts, columns, datas) => {
      let xml = [
        '<?xml version="1.0"?>',
        '<?mso-application progid="Excel.Sheet"?>',
        '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">',
        '<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">',
        "<Version>16.00</Version>",
        "</DocumentProperties>",
        '<ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">',
        "<WindowHeight>7920</WindowHeight>",
        "<WindowWidth>21570</WindowWidth>",
        "<WindowTopX>32767</WindowTopX>",
        "<WindowTopY>32767</WindowTopY>",
        "<ProtectStructure>False</ProtectStructure>",
        "<ProtectWindows>False</ProtectWindows>",
        "</ExcelWorkbook>",
        `<Worksheet ss:Name="${opts.sheetName}">`,
        "<Table>",
        columns.map((column) => `<Column ss:Width="${column.renderWidth}"/>`).join("")
      ].join("");
      if (opts.isHeader) {
        xml += `<Row>${columns.map((column) => `<Cell><Data ss:Type="String">${getHeaderTitle(opts, column)}</Data></Cell>`).join("")}</Row>`;
      }
      datas.forEach((row) => {
        xml += "<Row>" + columns.map((column) => `<Cell><Data ss:Type="String">${row[column.id]}</Data></Cell>`).join("") + "</Row>";
      });
      if (opts.isFooter) {
        const { footerTableData } = reactData;
        const footers = getFooterData($xeTable, opts, footerTableData);
        footers.forEach((row) => {
          xml += `<Row>${columns.map((column) => `<Cell><Data ss:Type="String">${getFooterCellValue(opts, row, column)}</Data></Cell>`).join("")}</Row>`;
        });
      }
      return `${xml}</Table></Worksheet></Workbook>`;
    };
    const getContent = ($xeTable2, opts, columns, datas) => {
      if (columns.length) {
        switch (opts.type) {
          case "csv":
            return toCsv($xeTable2, opts, columns, datas);
          case "txt":
            return toTxt($xeTable2, opts, columns, datas);
          case "html":
            return toHtml(opts, columns, datas);
          case "xml":
            return toXML(opts, columns, datas);
        }
      }
      return "";
    };
    const downloadFile = (opts, content) => {
      const { filename, type, download } = opts;
      if (!download) {
        const blob = getExportBlobByContent(content, opts);
        return Promise.resolve({ type, content, blob });
      }
      if (VxeUI.saveFile) {
        VxeUI.saveFile({ filename, type, content }).then(() => {
          if (opts.message !== false) {
            if (VxeUI.modal) {
              VxeUI.modal.message({ content: getI18n2("vxe.table.expSuccess"), status: "success" });
            }
          }
        });
      }
    };
    const handleExport = (opts) => {
      const { remote, columns, colgroups, exportMethod, afterExportMethod } = opts;
      return new Promise((resolve) => {
        if (remote) {
          const params = { options: opts, $table: $xeTable, $grid: $xeGrid };
          resolve(exportMethod ? exportMethod(params) : params);
        } else {
          const datas = getExportData(opts);
          resolve($xeTable.preventEvent(null, "event.export", { options: opts, columns, colgroups, datas }, () => {
            return downloadFile(opts, getContent($xeTable, opts, columns, datas));
          }));
        }
      }).then((params) => {
        clearColumnConvert(columns);
        if (!opts.print) {
          if (afterExportMethod) {
            afterExportMethod({ status: true, options: opts, $table: $xeTable, $grid: $xeGrid });
          }
        }
        return Object.assign({ status: true }, params);
      }).catch(() => {
        clearColumnConvert(columns);
        if (!opts.print) {
          if (afterExportMethod) {
            afterExportMethod({ status: false, options: opts, $table: $xeTable, $grid: $xeGrid });
          }
        }
        const params = { status: false };
        return Promise.reject(params);
      });
    };
    const handleImport = (content, opts) => {
      const { tableFullColumn, _importResolve, _importReject } = internalData;
      let rest = { fields: [], rows: [] };
      const tableFieldMaps = {};
      const tableTitleMaps = {};
      tableFullColumn.forEach((column) => {
        const field = column.field;
        const title = column.getTitle();
        if (field) {
          tableFieldMaps[field] = column;
        }
        if (title) {
          tableTitleMaps[column.getTitle()] = column;
        }
      });
      const tableConf = {
        fieldMaps: tableFieldMaps,
        titleMaps: tableTitleMaps
      };
      switch (opts.type) {
        case "csv":
          rest = parseCsv(tableConf, content);
          break;
        case "txt":
          rest = parseTxt(tableConf, content);
          break;
        case "html":
          rest = parseHTML(tableConf, content);
          break;
        case "xml":
          rest = parseXML(tableConf, content);
          break;
      }
      const { fields, rows } = rest;
      const status = fields.some((field) => tableFieldMaps[field] || tableTitleMaps[field]);
      if (status) {
        $xeTable.createData(rows).then((data) => {
          let loadRest;
          if (opts.mode === "insert" || opts.mode === "insertBottom") {
            loadRest = $xeTable.insertAt(data, -1);
          }
          if (opts.mode === "insertTop") {
            loadRest = $xeTable.insert(data);
          } else {
            loadRest = $xeTable.reloadData(data);
          }
          if (opts.message !== false) {
            if (VxeUI.modal) {
              VxeUI.modal.message({ content: getI18n2("vxe.table.impSuccess", [rows.length]), status: "success" });
            }
          }
          return loadRest.then(() => {
            if (_importResolve) {
              _importResolve({ status: true });
            }
          });
        });
      } else if (opts.message !== false) {
        if (VxeUI.modal) {
          VxeUI.modal.message({ content: getI18n2("vxe.error.impFields"), status: "error" });
        }
        if (_importReject) {
          _importReject({ status: false });
        }
      }
    };
    const handleFileImport = (file, opts) => {
      const { importMethod, afterImportMethod } = opts;
      const { type, filename } = parseFile(file);
      const importOpts = computeImportOpts.value;
      if (!importMethod && !import_xe_utils5.default.includes(import_xe_utils5.default.keys(importOpts._typeMaps), type)) {
        if (opts.message !== false) {
          if (VxeUI.modal) {
            VxeUI.modal.message({ content: getI18n2("vxe.error.notType", [type]), status: "error" });
          }
        }
        const params = { status: false };
        return Promise.reject(params);
      }
      const rest = new Promise((resolve, reject) => {
        const _importResolve = (params) => {
          resolve(params);
          internalData._importResolve = null;
          internalData._importReject = null;
        };
        const _importReject = (params) => {
          reject(params);
          internalData._importResolve = null;
          internalData._importReject = null;
        };
        internalData._importResolve = _importResolve;
        internalData._importReject = _importReject;
        if (window.FileReader) {
          const options = Object.assign({ mode: "insertTop" }, opts, { type, filename });
          if (options.remote) {
            if (importMethod) {
              Promise.resolve(importMethod({ file, options, $table: $xeTable })).then(() => {
                _importResolve({ status: true });
              }).catch(() => {
                _importResolve({ status: true });
              });
            } else {
              _importResolve({ status: true });
            }
          } else {
            const { tableFullColumn } = internalData;
            $xeTable.preventEvent(null, "event.import", { file, options, columns: tableFullColumn }, () => {
              const reader = new FileReader();
              reader.onerror = () => {
                errLog("vxe.error.notType", [type]);
                _importReject({ status: false });
              };
              reader.onload = (e) => {
                handleImport(e.target.result, options);
              };
              reader.readAsText(file, options.encoding || "UTF-8");
            });
          }
        } else {
          errLog("vxe.error.notExp");
          _importResolve({ status: true });
        }
      });
      return rest.then(() => {
        if (afterImportMethod) {
          afterImportMethod({ status: true, options: opts, $table: $xeTable });
        }
      }).catch((e) => {
        if (afterImportMethod) {
          afterImportMethod({ status: false, options: opts, $table: $xeTable });
        }
        return Promise.reject(e);
      });
    };
    const handleFilterColumns = (exportOpts, column, columns) => {
      return columns.some((item) => {
        if (isColumnInfo(item)) {
          return column.id === item.id;
        } else if (import_xe_utils5.default.isString(item)) {
          return column.field === item;
        } else {
          const colid = item.id || item.colId;
          const type = item.type;
          const field = item.field;
          if (colid) {
            return column.id === colid;
          } else if (field && type) {
            return column.field === field && column.type === type;
          } else if (field) {
            return column.field === field;
          } else if (type) {
            return column.type === type;
          }
        }
        return false;
      });
    };
    const handleFilterFields = (exportOpts, column, includeFields, excludeFields) => {
      if (excludeFields) {
        if (import_xe_utils5.default.includes(excludeFields, column.field)) {
          return false;
        }
      }
      if (includeFields) {
        if (import_xe_utils5.default.includes(includeFields, column.field)) {
          return true;
        }
        return false;
      }
      return exportOpts.original ? column.field : defaultFilterExportColumn(column);
    };
    const handleExportAndPrint = (options, isPrint) => {
      const { treeConfig, showHeader, showFooter } = props;
      const { initStore, isGroup, footerTableData, exportStore, exportParams } = reactData;
      const { collectColumn, mergeBodyList, mergeFooterList } = internalData;
      const exportOpts = computeExportOpts.value;
      const hasTree = treeConfig;
      const customOpts = computeCustomOpts.value;
      const selectRecords = $xeTable.getCheckboxRecords();
      const proxyOpts = $xeGrid ? $xeGrid.getComputeMaps().computeProxyOpts.value : {};
      const hasFooter = !!footerTableData.length;
      const hasMerge = !!(mergeBodyList.length || mergeFooterList.length);
      const defOpts = Object.assign({
        message: true,
        isHeader: showHeader,
        isTitle: showHeader,
        isFooter: showFooter,
        isColgroup: isGroup,
        isMerge: hasMerge,
        useStyle: true,
        current: "current",
        modes: (proxyOpts.ajax && proxyOpts.ajax.queryAll ? ["all"] : []).concat(["current", "selected", "empty"])
      }, options);
      const types = defOpts.types || import_xe_utils5.default.keys(exportOpts._typeMaps);
      const modes = defOpts.modes || [];
      const checkMethod = customOpts.checkMethod;
      const exportColumns = collectColumn.slice(0);
      const { columns, excludeFields, includeFields } = defOpts;
      const typeList = types.map((value) => {
        return {
          value,
          label: getI18n2(`vxe.export.types.${value}`)
        };
      });
      const modeList = modes.map((item) => {
        if (item && item.value) {
          return {
            value: item.value,
            label: item.label || item.value
          };
        }
        return {
          value: item,
          label: getI18n2(`vxe.export.modes.${item}`)
        };
      });
      import_xe_utils5.default.eachTree(exportColumns, (column, index, items, path, parent) => {
        const isColGroup = column.children && column.children.length;
        let isChecked = false;
        if (columns && columns.length) {
          isChecked = handleFilterColumns(defOpts, column, columns);
        } else if (excludeFields || includeFields) {
          isChecked = handleFilterFields(defOpts, column, includeFields, excludeFields);
        } else {
          isChecked = column.visible && (isColGroup || defaultFilterExportColumn(column));
        }
        column.checked = isChecked;
        column.halfChecked = false;
        column.disabled = parent && parent.disabled || (checkMethod ? !checkMethod({ $table: $xeTable, column }) : false);
      });
      Object.assign(exportStore, {
        columns: exportColumns,
        typeList,
        modeList,
        hasFooter,
        hasMerge,
        hasTree,
        isPrint,
        hasColgroup: isGroup,
        visible: true
      });
      Object.assign(exportParams, {
        mode: selectRecords.length ? "selected" : "current"
      }, defOpts);
      const { filename, sheetName, mode, type } = exportParams;
      if (filename) {
        if (import_xe_utils5.default.isFunction(filename)) {
          exportParams.filename = filename({
            options: defOpts,
            $table: $xeTable,
            $grid: $xeGrid
          });
        } else {
          exportParams.filename = `${filename}`;
        }
      }
      if (sheetName) {
        if (import_xe_utils5.default.isFunction(sheetName)) {
          exportParams.sheetName = sheetName({
            options: defOpts,
            $table: $xeTable,
            $grid: $xeGrid
          });
        } else {
          exportParams.sheetName = `${sheetName}`;
        }
      }
      if (!modeList.some((item) => item.value === mode)) {
        exportParams.mode = modeList[0].value;
      }
      if (!typeList.some((item) => item.value === type)) {
        exportParams.type = typeList[0].value;
      }
      initStore.export = true;
      return nextTick();
    };
    const handleCloseExport = () => {
      if (VxeUI.modal) {
        return VxeUI.modal.close("VXE_EXPORT_MODAL");
      }
      return Promise.resolve();
    };
    const exportMethods = {
      /**
       * 导出文件，支持 csv/html/xml/txt
       * 如果是树表格，则默认是导出所有节点
       * 如果是启用了虚拟滚动，则只能导出数据源，可以配合 dataFilterMethod 函数转换数据
       * @param {Object} options 参数
       */
      exportData(options) {
        const { treeConfig, showHeader, showFooter } = props;
        const { isGroup } = reactData;
        const { tableFullColumn, afterFullData, collectColumn, mergeBodyList, mergeFooterList } = internalData;
        const exportOpts = computeExportOpts.value;
        const treeOpts = computeTreeOpts.value;
        const proxyOpts = $xeGrid ? $xeGrid.getComputeMaps().computeProxyOpts.value : {};
        const hasMerge = !!(mergeBodyList.length || mergeFooterList.length);
        const opts = Object.assign({
          message: true,
          isHeader: showHeader,
          isTitle: showHeader,
          isFooter: showFooter,
          isColgroup: isGroup,
          isMerge: hasMerge,
          useStyle: true,
          current: "current",
          modes: (proxyOpts.ajax && proxyOpts.ajax.queryAll ? ["all"] : []).concat(["current", "selected", "empty"]),
          download: true,
          type: "csv"
          // filename: '',
          // sheetName: '',
          // original: false,
          // isAllExpand: false,
          // data: null,
          // remote: false,
          // dataFilterMethod: null,
          // footerFilterMethod: null,
          // exportMethod: null,
          // columnFilterMethod: null,
          // beforeExportMethod: null,
          // afterExportMethod: null
        }, exportOpts, options);
        let { filename, sheetName, type, mode, columns, original, columnFilterMethod, beforeExportMethod, includeFields, excludeFields } = opts;
        let groups = [];
        const selectRecords = $xeTable.getCheckboxRecords();
        if (!mode) {
          mode = selectRecords.length ? "selected" : "current";
        }
        const customCols = columns && columns.length ? columns : import_xe_utils5.default.searchTree(collectColumn, (column) => {
          const isColGroup = column.children && column.children.length;
          let isChecked = false;
          if (columns && columns.length) {
            isChecked = handleFilterColumns(opts, column, columns);
          } else if (excludeFields || includeFields) {
            isChecked = handleFilterFields(opts, column, includeFields, excludeFields);
          } else {
            isChecked = column.visible && (isColGroup || defaultFilterExportColumn(column));
          }
          return isChecked;
        }, { children: "children", mapChildren: "childNodes", original: true });
        const handleOptions = Object.assign({}, opts, { filename: "", sheetName: "" });
        if (!customCols && !columnFilterMethod) {
          handleOptions.columnFilterMethod = ({ column }) => {
            if (excludeFields) {
              if (import_xe_utils5.default.includes(excludeFields, column.field)) {
                return false;
              }
            }
            if (includeFields) {
              if (import_xe_utils5.default.includes(includeFields, column.field)) {
                return true;
              }
              return false;
            }
            return original ? column.field : defaultFilterExportColumn(column);
          };
        }
        if (customCols) {
          handleOptions._isCustomColumn = true;
          groups = import_xe_utils5.default.searchTree(import_xe_utils5.default.mapTree(customCols, (item) => {
            let targetColumn;
            if (item) {
              if (isColumnInfo(item)) {
                targetColumn = item;
              } else if (import_xe_utils5.default.isString(item)) {
                targetColumn = $xeTable.getColumnByField(item);
              } else {
                const colid = item.id || item.colId;
                const type2 = item.type;
                const field = item.field;
                if (colid) {
                  targetColumn = $xeTable.getColumnById(colid);
                } else if (field && type2) {
                  targetColumn = tableFullColumn.find((column) => column.field === field && column.type === type2);
                } else if (field) {
                  targetColumn = $xeTable.getColumnByField(field);
                } else if (type2) {
                  targetColumn = tableFullColumn.find((column) => column.type === type2);
                }
              }
              return targetColumn || {};
            }
          }, {
            children: "childNodes",
            mapChildren: "_children"
          }), (column, index) => isColumnInfo(column) && (!columnFilterMethod || columnFilterMethod({ $table: $xeTable, column, $columnIndex: index })), {
            children: "_children",
            mapChildren: "childNodes",
            original: true
          });
        } else {
          groups = import_xe_utils5.default.searchTree(isGroup ? collectColumn : tableFullColumn, (column, index) => column.visible && (!columnFilterMethod || columnFilterMethod({ $table: $xeTable, column, $columnIndex: index })), { children: "children", mapChildren: "childNodes", original: true });
        }
        const cols = [];
        import_xe_utils5.default.eachTree(groups, (column) => {
          const isColGroup = column.children && column.children.length;
          if (!isColGroup) {
            cols.push(column);
          }
        }, { children: "childNodes" });
        handleOptions.columns = cols;
        handleOptions.colgroups = convertToRows(groups);
        if (filename) {
          if (import_xe_utils5.default.isFunction(filename)) {
            handleOptions.filename = filename({
              options: opts,
              $table: $xeTable,
              $grid: $xeGrid
            });
          } else {
            handleOptions.filename = `${filename}`;
          }
        }
        if (!handleOptions.filename) {
          handleOptions.filename = getI18n2(handleOptions.original ? "vxe.table.expOriginFilename" : "vxe.table.expFilename", [import_xe_utils5.default.toDateString(Date.now(), "yyyyMMddHHmmss")]);
        }
        if (sheetName) {
          if (import_xe_utils5.default.isFunction(sheetName)) {
            handleOptions.sheetName = sheetName({
              options: opts,
              $table: $xeTable,
              $grid: $xeGrid
            });
          } else {
            handleOptions.sheetName = `${sheetName}`;
          }
        }
        if (!handleOptions.sheetName) {
          handleOptions.sheetName = document.title || "";
        }
        if (!handleOptions.exportMethod && !import_xe_utils5.default.includes(import_xe_utils5.default.keys(exportOpts._typeMaps), type)) {
          errLog("vxe.error.notType", [type]);
          if (true) {
            if (["xlsx", "pdf"].includes(type)) {
              warnLog("vxe.error.reqPlugin", [4, "plugin-export-xlsx"]);
            }
          }
          const params = { status: false };
          return Promise.reject(params);
        }
        if (!handleOptions.print) {
          if (beforeExportMethod) {
            beforeExportMethod({ options: handleOptions, $table: $xeTable, $grid: $xeGrid });
          }
        }
        if (!handleOptions.data) {
          handleOptions.data = [];
          if (mode === "selected") {
            if (["html", "pdf"].indexOf(type) > -1 && treeConfig) {
              handleOptions.data = import_xe_utils5.default.searchTree($xeTable.getTableData().fullData, (item) => $xeTable.findRowIndexOf(selectRecords, item) > -1, Object.assign({}, treeOpts, { data: "_row" }));
            } else {
              handleOptions.data = selectRecords;
            }
          } else if (mode === "all") {
            if (true) {
              if (!$xeGrid) {
                warnLog("vxe.error.errProp", ["all", "mode=current,selected"]);
              }
            }
            if ($xeGrid && !handleOptions.remote) {
              const gridReactData = $xeGrid.reactData;
              const { computeProxyOpts } = $xeGrid.getComputeMaps();
              const proxyOpts2 = computeProxyOpts.value;
              const { sortData } = gridReactData;
              const { beforeQueryAll, afterQueryAll, ajax = {} } = proxyOpts2;
              const resConfigs = proxyOpts2.response || proxyOpts2.props || {};
              const ajaxMethods = ajax.queryAll;
              const queryAllSuccessMethods = ajax.queryAllSuccess;
              const queryAllErrorMethods = ajax.queryAllError;
              if (true) {
                if (!ajaxMethods) {
                  warnLog("vxe.error.notFunc", ["proxy-config.ajax.queryAll"]);
                }
              }
              if (ajaxMethods) {
                const params = {
                  $table: $xeTable,
                  $grid: $xeGrid,
                  sort: sortData.length ? sortData[0] : {},
                  sorts: sortData,
                  filters: gridReactData.filterData,
                  form: gridReactData.formData,
                  options: handleOptions
                };
                return Promise.resolve((beforeQueryAll || ajaxMethods)(params)).then((rest) => {
                  const listProp = resConfigs.list;
                  handleOptions.data = (listProp ? import_xe_utils5.default.isFunction(listProp) ? listProp({ data: rest, $grid: $xeGrid }) : import_xe_utils5.default.get(rest, listProp) : rest) || [];
                  if (afterQueryAll) {
                    afterQueryAll(params);
                  }
                  if (queryAllSuccessMethods) {
                    queryAllSuccessMethods(Object.assign(Object.assign({}, params), { response: rest }));
                  }
                  return handleExport(handleOptions);
                }).catch((rest) => {
                  if (queryAllErrorMethods) {
                    queryAllErrorMethods(Object.assign(Object.assign({}, params), { response: rest }));
                  }
                });
              }
            }
          }
          if (mode === "current") {
            handleOptions.data = afterFullData;
          }
        }
        return handleExport(handleOptions);
      },
      importByFile(file, options) {
        const opts = Object.assign({}, options);
        const { beforeImportMethod } = opts;
        if (beforeImportMethod) {
          beforeImportMethod({ options: opts, $table: $xeTable });
        }
        return handleFileImport(file, opts);
      },
      importData(options) {
        const importOpts = computeImportOpts.value;
        const opts = Object.assign({
          types: import_xe_utils5.default.keys(importOpts._typeMaps)
          // beforeImportMethod: null,
          // afterImportMethod: null
        }, importOpts, options);
        const { beforeImportMethod, afterImportMethod } = opts;
        if (beforeImportMethod) {
          beforeImportMethod({ options: opts, $table: $xeTable });
        }
        return VxeUI.readFile(opts).catch((e) => {
          if (afterImportMethod) {
            afterImportMethod({ status: false, options: opts, $table: $xeTable });
          }
          return Promise.reject(e);
        }).then((params) => {
          const { file } = params;
          return handleFileImport(file, opts);
        });
      },
      saveFile(options) {
        return VxeUI.saveFile(options);
      },
      readFile(options) {
        return VxeUI.readFile(options);
      },
      print(options) {
        const printOpts = computePrintOpts.value;
        const opts = Object.assign({
          original: false
          // beforePrintMethod
        }, printOpts, options, {
          type: "html",
          download: false,
          remote: false,
          print: true
        });
        const { sheetName } = opts;
        let printTitle = "";
        if (sheetName) {
          if (import_xe_utils5.default.isFunction(sheetName)) {
            printTitle = sheetName({
              options: opts,
              $table: $xeTable,
              $grid: $xeGrid
            });
          } else {
            printTitle = `${sheetName}`;
          }
        }
        if (!printTitle) {
          printTitle = document.title || "";
        }
        const beforePrintMethod = opts.beforePrintMethod;
        const tableHtml = opts.html || opts.content;
        return new Promise((resolve, reject) => {
          if (VxeUI.print) {
            if (tableHtml) {
              resolve(VxeUI.print({
                title: printTitle,
                html: tableHtml,
                customStyle: opts.style,
                beforeMethod: beforePrintMethod ? ({ html }) => {
                  return beforePrintMethod({
                    html,
                    content: html,
                    options: opts,
                    $table: $xeTable
                  });
                } : void 0
              }));
            } else {
              resolve(exportMethods.exportData(opts).then(({ content }) => {
                return VxeUI.print({
                  title: printTitle,
                  html: content,
                  customStyle: opts.style,
                  beforeMethod: beforePrintMethod ? ({ html }) => {
                    return beforePrintMethod({
                      html,
                      content: html,
                      options: opts,
                      $table: $xeTable
                    });
                  } : void 0
                });
              }));
            }
          } else {
            const e = { status: false };
            reject(e);
          }
        });
      },
      getPrintHtml(options) {
        const printOpts = computePrintOpts.value;
        const opts = Object.assign({
          original: false
          // beforePrintMethod
        }, printOpts, options, {
          type: "html",
          download: false,
          remote: false,
          print: true
        });
        return $xeTable.exportData(opts).then(({ content }) => {
          return {
            html: content
          };
        });
      },
      closeImport() {
        if (VxeUI.modal) {
          return VxeUI.modal.close("VXE_IMPORT_MODAL");
        }
        return Promise.resolve();
      },
      openImport(options) {
        const { treeConfig, importConfig } = props;
        const { initStore, importStore, importParams } = reactData;
        const importOpts = computeImportOpts.value;
        const defOpts = Object.assign({
          mode: "insertTop",
          message: true,
          types: import_xe_utils5.default.keys(importOpts._typeMaps),
          modes: ["insertTop", "covering"]
        }, importOpts, options);
        const types = defOpts.types || [];
        const modes = defOpts.modes || [];
        const isTree = !!treeConfig;
        if (isTree) {
          if (defOpts.message) {
            if (VxeUI.modal) {
              VxeUI.modal.message({ content: getI18n2("vxe.error.treeNotImp"), status: "error" });
            }
          }
          return;
        }
        if (!importConfig) {
          errLog("vxe.error.reqProp", ["import-config"]);
        }
        const typeList = types.map((value) => {
          return {
            value,
            label: getI18n2(`vxe.export.types.${value}`)
          };
        });
        const modeList = modes.map((item) => {
          if (item && item.value) {
            return {
              value: item.value,
              label: item.label || item.value
            };
          }
          return {
            value: item,
            label: getI18n2(`vxe.import.modes.${item}`)
          };
        });
        Object.assign(importStore, {
          file: null,
          type: "",
          filename: "",
          modeList,
          typeList,
          visible: true
        });
        Object.assign(importParams, defOpts);
        if (!modeList.some((item) => item.value === importParams.mode)) {
          importParams.mode = modeList[0].value;
        }
        initStore.import = true;
      },
      closeExport: handleCloseExport,
      openExport(options) {
        const exportOpts = computeExportOpts.value;
        const defOpts = Object.assign({
          message: true,
          types: import_xe_utils5.default.keys(exportOpts._typeMaps)
        }, exportOpts, options);
        if (!props.exportConfig) {
          errLog("vxe.error.reqProp", ["export-config"]);
        }
        handleExportAndPrint(defOpts);
      },
      closePrint: handleCloseExport,
      openPrint(options) {
        const printOpts = computePrintOpts.value;
        const defOpts = Object.assign({
          message: true
        }, printOpts, options);
        if (!props.printConfig) {
          errLog("vxe.error.reqProp", ["print-config"]);
        }
        handleExportAndPrint(defOpts, true);
      }
    };
    return exportMethods;
  },
  setupGrid($xeGrid) {
    return $xeGrid.extendTableMethods(tableExportMethodKeys);
  }
});

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/module/keyboard/hook.js
var import_xe_utils6 = __toESM(require_xe_utils());
var { hooks: hooks5 } = VxeUI;
var browseObj = import_xe_utils6.default.browse();
function getTargetOffset(target, container) {
  let offsetTop = 0;
  let offsetLeft = 0;
  const triggerCheckboxLabel = !browseObj.firefox && hasClass(target, "vxe-checkbox--label");
  if (triggerCheckboxLabel) {
    const checkboxLabelStyle = getComputedStyle(target);
    offsetTop -= import_xe_utils6.default.toNumber(checkboxLabelStyle.paddingTop);
    offsetLeft -= import_xe_utils6.default.toNumber(checkboxLabelStyle.paddingLeft);
  }
  while (target && target !== container) {
    offsetTop += target.offsetTop;
    offsetLeft += target.offsetLeft;
    target = target.offsetParent;
    if (triggerCheckboxLabel) {
      const checkboxStyle = getComputedStyle(target);
      offsetTop -= import_xe_utils6.default.toNumber(checkboxStyle.paddingTop);
      offsetLeft -= import_xe_utils6.default.toNumber(checkboxStyle.paddingLeft);
    }
  }
  return { offsetTop, offsetLeft };
}
hooks5.add("tableKeyboardModule", {
  setupTable($xeTable) {
    const { props, reactData, internalData } = $xeTable;
    const { refElem } = $xeTable.getRefMaps();
    const { computeEditOpts, computeCheckboxOpts, computeMouseOpts, computeTreeOpts, computeRowOpts, computeColumnOpts, computeCellOpts, computeDefaultRowHeight, computeCurrentRowOpts, computeCurrentColumnOpts } = $xeTable.getComputeMaps();
    function getCheckboxRangeRows(evnt, params, targetTrElem, trRect, offsetClientTop, moveRange) {
      const { showOverflow } = props;
      const { fullAllDataRowIdData, isResizeCellHeight } = internalData;
      const rowOpts = computeRowOpts.value;
      const cellOpts = computeCellOpts.value;
      const defaultRowHeight = computeDefaultRowHeight.value;
      const { row } = params;
      let countHeight = 0;
      let rangeRows = [];
      let moveSize = 0;
      const isDown = moveRange > 0;
      const { scrollYLoad } = reactData;
      const { afterFullData } = internalData;
      if (isDown) {
        moveSize = offsetClientTop + moveRange;
      } else {
        moveSize = trRect.height - offsetClientTop + Math.abs(moveRange);
      }
      if (scrollYLoad) {
        const _rowIndex = $xeTable.getVTRowIndex(row);
        const isCustomCellHeight = isResizeCellHeight || cellOpts.height || rowOpts.height;
        if (!isCustomCellHeight && showOverflow) {
          if (isDown) {
            rangeRows = afterFullData.slice(_rowIndex, _rowIndex + Math.ceil(moveSize / defaultRowHeight));
          } else {
            rangeRows = afterFullData.slice(_rowIndex - Math.floor(moveSize / defaultRowHeight), _rowIndex + 1);
          }
        } else {
          if (isDown) {
            for (let i = _rowIndex; i < afterFullData.length; i++) {
              const item = afterFullData[i];
              const rowid = $xeTable.getRowid(item);
              const rowRest = fullAllDataRowIdData[rowid] || {};
              countHeight += rowRest.resizeHeight || cellOpts.height || rowOpts.height || rowRest.height || defaultRowHeight;
              rangeRows.push(item);
              if (countHeight > moveSize) {
                return rangeRows;
              }
            }
          } else {
            for (let len = _rowIndex; len >= 0; len--) {
              const item = afterFullData[len];
              const rowid = $xeTable.getRowid(item);
              const rowRest = fullAllDataRowIdData[rowid] || {};
              countHeight += rowRest.resizeHeight || cellOpts.height || rowOpts.height || rowRest.height || defaultRowHeight;
              rangeRows.push(item);
              if (countHeight > moveSize) {
                return rangeRows;
              }
            }
          }
        }
      } else {
        const siblingProp = isDown ? "next" : "previous";
        while (targetTrElem && countHeight < moveSize) {
          const rowNodeRest = $xeTable.getRowNode(targetTrElem);
          if (rowNodeRest) {
            rangeRows.push(rowNodeRest.item);
            countHeight += targetTrElem.offsetHeight;
            targetTrElem = targetTrElem[`${siblingProp}ElementSibling`];
          }
        }
      }
      return rangeRows;
    }
    const handleCheckboxRangeEvent = (evnt, params) => {
      const { elemStore } = internalData;
      const bodyScrollElem = getRefElem(elemStore["main-body-scroll"]);
      const leftScrollElem = getRefElem(elemStore["left-body-scroll"]);
      const rightScrollElem = getRefElem(elemStore["right-body-scroll"]);
      const { column, cell } = params;
      if (column.type === "checkbox") {
        let bodyWrapperElem = bodyScrollElem;
        if (leftScrollElem && column.fixed === "left") {
          bodyWrapperElem = leftScrollElem;
        } else if (rightScrollElem && column.fixed === "right") {
          bodyWrapperElem = rightScrollElem;
        }
        if (!bodyWrapperElem) {
          return;
        }
        const el = refElem.value;
        const disX = evnt.clientX;
        const disY = evnt.clientY;
        const checkboxRangeElem = bodyWrapperElem.querySelector(".vxe-table--checkbox-range");
        const trElem = cell.parentElement;
        const selectRecords = $xeTable.getCheckboxRecords();
        let lastRangeRows = [];
        const marginSize = 1;
        const offsetRest = getTargetOffset(evnt.target, bodyWrapperElem);
        const startTop = offsetRest.offsetTop + evnt.offsetY;
        const startLeft = offsetRest.offsetLeft + evnt.offsetX;
        const startScrollTop = bodyWrapperElem.scrollTop;
        const rowHeight = trElem.offsetHeight;
        const trRect = trElem.getBoundingClientRect();
        const offsetClientTop = disY - trRect.y;
        let mouseScrollTimeout = null;
        let isMouseScrollDown = false;
        let mouseScrollSpaceSize = 1;
        const triggerEvent2 = (type, evnt2) => {
          $xeTable.dispatchEvent(`checkbox-range-${type}`, { records: $xeTable.getCheckboxRecords(), reserves: $xeTable.getCheckboxReserveRecords() }, evnt2);
        };
        const handleChecked = (evnt2) => {
          const { clientX, clientY } = evnt2;
          const offsetLeft = clientX - disX;
          const offsetTop = clientY - disY + (bodyWrapperElem.scrollTop - startScrollTop);
          let rangeHeight = Math.abs(offsetTop);
          let rangeWidth = Math.abs(offsetLeft);
          let rangeTop = startTop;
          let rangeLeft = startLeft;
          if (offsetTop < marginSize) {
            rangeTop += offsetTop;
            if (rangeTop < marginSize) {
              rangeTop = marginSize;
              rangeHeight = startTop;
            }
          } else {
            rangeHeight = Math.min(rangeHeight, bodyWrapperElem.scrollHeight - startTop - marginSize);
          }
          if (offsetLeft < marginSize) {
            rangeLeft += offsetLeft;
            if (rangeWidth > startLeft) {
              rangeLeft = marginSize;
              rangeWidth = startLeft;
            }
          } else {
            rangeWidth = Math.min(rangeWidth, bodyWrapperElem.clientWidth - startLeft - marginSize);
          }
          checkboxRangeElem.style.height = `${rangeHeight}px`;
          checkboxRangeElem.style.width = `${rangeWidth}px`;
          checkboxRangeElem.style.left = `${rangeLeft}px`;
          checkboxRangeElem.style.top = `${rangeTop}px`;
          checkboxRangeElem.style.display = "block";
          const rangeRows = getCheckboxRangeRows(evnt2, params, trElem, trRect, offsetClientTop, offsetTop < marginSize ? -rangeHeight : rangeHeight);
          if (rangeHeight > 10 && rangeRows.length !== lastRangeRows.length) {
            const isControlKey = hasControlKey(evnt2);
            lastRangeRows = rangeRows;
            if (isControlKey) {
              rangeRows.forEach((row) => {
                $xeTable.handleBatchSelectRows([row], selectRecords.indexOf(row) === -1);
              });
            } else {
              $xeTable.setAllCheckboxRow(false);
              $xeTable.handleCheckedCheckboxRow(rangeRows, true, false);
            }
            triggerEvent2("change", evnt2);
          }
        };
        const stopMouseScroll = () => {
          clearTimeout(mouseScrollTimeout);
          mouseScrollTimeout = null;
        };
        const startMouseScroll = (evnt2) => {
          stopMouseScroll();
          mouseScrollTimeout = setTimeout(() => {
            if (mouseScrollTimeout) {
              const { scrollLeft, scrollTop, clientHeight, scrollHeight } = bodyWrapperElem;
              const topSize = Math.ceil(mouseScrollSpaceSize * 50 / rowHeight);
              if (isMouseScrollDown) {
                if (scrollTop + clientHeight < scrollHeight) {
                  $xeTable.scrollTo(scrollLeft, scrollTop + topSize);
                  startMouseScroll(evnt2);
                  handleChecked(evnt2);
                } else {
                  stopMouseScroll();
                }
              } else {
                if (scrollTop) {
                  $xeTable.scrollTo(scrollLeft, scrollTop - topSize);
                  startMouseScroll(evnt2);
                  handleChecked(evnt2);
                } else {
                  stopMouseScroll();
                }
              }
            }
          }, 50);
        };
        addClass(el, "drag--range");
        document.onmousemove = (evnt2) => {
          evnt2.preventDefault();
          evnt2.stopPropagation();
          const { clientY } = evnt2;
          const { boundingTop } = getAbsolutePos(bodyWrapperElem);
          if (clientY < boundingTop) {
            isMouseScrollDown = false;
            mouseScrollSpaceSize = boundingTop - clientY;
            if (!mouseScrollTimeout) {
              startMouseScroll(evnt2);
            }
          } else if (clientY > boundingTop + bodyWrapperElem.clientHeight) {
            isMouseScrollDown = true;
            mouseScrollSpaceSize = clientY - boundingTop - bodyWrapperElem.clientHeight;
            if (!mouseScrollTimeout) {
              startMouseScroll(evnt2);
            }
          } else if (mouseScrollTimeout) {
            stopMouseScroll();
          }
          handleChecked(evnt2);
        };
        document.onmouseup = (evnt2) => {
          stopMouseScroll();
          removeClass(el, "drag--range");
          checkboxRangeElem.removeAttribute("style");
          document.onmousemove = null;
          document.onmouseup = null;
          triggerEvent2("end", evnt2);
        };
        triggerEvent2("start", evnt);
      }
    };
    const handleCellMousedownEvent = (evnt, params) => {
      const { editConfig, checkboxConfig, mouseConfig } = props;
      const checkboxOpts = computeCheckboxOpts.value;
      const mouseOpts = computeMouseOpts.value;
      const editOpts = computeEditOpts.value;
      if (mouseConfig && mouseOpts.area && $xeTable.triggerCellAreaMousedownEvent) {
        return $xeTable.triggerCellAreaMousedownEvent(evnt, params);
      } else {
        if (checkboxConfig && checkboxOpts.range) {
          handleCheckboxRangeEvent(evnt, params);
        }
        if (mouseConfig && mouseOpts.selected) {
          if (!editConfig || editOpts.mode === "cell") {
            $xeTable.handleSelected(params, evnt);
          }
        }
      }
    };
    const handleMoveSelected = (evnt, args, isLeftArrow, isUpArrow, isRightArrow, isDwArrow) => {
      const { afterFullData, visibleColumn } = internalData;
      const params = Object.assign({}, args);
      const _rowIndex = $xeTable.getVTRowIndex(params.row);
      const _columnIndex = $xeTable.getVTColumnIndex(params.column);
      evnt.preventDefault();
      if (isUpArrow && _rowIndex > 0) {
        params.rowIndex = _rowIndex - 1;
        params.row = afterFullData[params.rowIndex];
      } else if (isDwArrow && _rowIndex < afterFullData.length - 1) {
        params.rowIndex = _rowIndex + 1;
        params.row = afterFullData[params.rowIndex];
      } else if (isLeftArrow && _columnIndex) {
        params.columnIndex = _columnIndex - 1;
        params.column = visibleColumn[params.columnIndex];
      } else if (isRightArrow && _columnIndex < visibleColumn.length - 1) {
        params.columnIndex = _columnIndex + 1;
        params.column = visibleColumn[params.columnIndex];
      }
      $xeTable.scrollToRow(params.row, params.column).then(() => {
        params.cell = $xeTable.getCellElement(params.row, params.column);
        $xeTable.handleSelected(params, evnt);
      });
      return params;
    };
    const keyboardMethods = {
      // 处理 Tab 键移动
      moveTabSelected(args, isLeft, evnt) {
        const { editConfig } = props;
        const { afterFullData, visibleColumn } = internalData;
        const editOpts = computeEditOpts.value;
        const rowOpts = computeRowOpts.value;
        const currentRowOpts = computeCurrentRowOpts.value;
        const columnOpts = computeColumnOpts.value;
        const currentColumnOpts = computeCurrentColumnOpts.value;
        let targetRow;
        let targetRowIndex;
        let targetColumnIndex;
        const params = Object.assign({}, args);
        const _rowIndex = $xeTable.getVTRowIndex(params.row);
        const _columnIndex = $xeTable.getVTColumnIndex(params.column);
        evnt.preventDefault();
        if (isLeft) {
          if (_columnIndex <= 0) {
            if (_rowIndex > 0) {
              targetRowIndex = _rowIndex - 1;
              targetRow = afterFullData[targetRowIndex];
              targetColumnIndex = visibleColumn.length - 1;
            }
          } else {
            targetColumnIndex = _columnIndex - 1;
          }
        } else {
          if (_columnIndex >= visibleColumn.length - 1) {
            if (_rowIndex < afterFullData.length - 1) {
              targetRowIndex = _rowIndex + 1;
              targetRow = afterFullData[targetRowIndex];
              targetColumnIndex = 0;
            }
          } else {
            targetColumnIndex = _columnIndex + 1;
          }
        }
        const targetColumn = visibleColumn[targetColumnIndex];
        if (targetColumn) {
          if (targetRow) {
            params.rowIndex = targetRowIndex;
            params.row = targetRow;
          } else {
            params.rowIndex = _rowIndex;
          }
          params.columnIndex = targetColumnIndex;
          params.column = targetColumn;
          params.cell = $xeTable.getCellElement(params.row, params.column);
          if (rowOpts.isCurrent && currentRowOpts.isFollowSelected) {
            $xeTable.triggerCurrentRowEvent(evnt, params);
          }
          if (columnOpts.isCurrent && currentColumnOpts.isFollowSelected) {
            $xeTable.triggerCurrentColumnEvent(evnt, params);
          }
          if (editConfig) {
            if (editOpts.trigger === "click" || editOpts.trigger === "dblclick") {
              if (editOpts.mode === "row") {
                $xeTable.handleEdit(params, evnt);
              } else {
                $xeTable.scrollToRow(params.row, params.column).then(() => {
                  $xeTable.handleSelected(params, evnt);
                });
              }
            }
          } else {
            $xeTable.scrollToRow(params.row, params.column).then(() => {
              $xeTable.handleSelected(params, evnt);
            });
          }
        }
      },
      // 处理当前行方向键移动
      moveCurrentRow(isUpArrow, isDwArrow, evnt) {
        const { treeConfig } = props;
        const { currentRow } = reactData;
        const { afterFullData } = internalData;
        const treeOpts = computeTreeOpts.value;
        const childrenField = treeOpts.children || treeOpts.childrenField;
        let targetRow;
        if (currentRow) {
          if (treeConfig) {
            const { index, items } = import_xe_utils6.default.findTree(afterFullData, (item) => item === currentRow, { children: childrenField });
            if (isUpArrow && index > 0) {
              targetRow = items[index - 1];
            } else if (isDwArrow && index < items.length - 1) {
              targetRow = items[index + 1];
            }
          } else {
            const _rowIndex = $xeTable.getVTRowIndex(currentRow);
            if (isUpArrow && _rowIndex > 0) {
              targetRow = afterFullData[_rowIndex - 1];
            } else if (isDwArrow && _rowIndex < afterFullData.length - 1) {
              targetRow = afterFullData[_rowIndex + 1];
            }
          }
        } else {
          targetRow = afterFullData[0];
        }
        if (targetRow) {
          evnt.preventDefault();
          const params = {
            $table: $xeTable,
            row: targetRow,
            rowIndex: $xeTable.getRowIndex(targetRow),
            $rowIndex: $xeTable.getVMRowIndex(targetRow)
          };
          $xeTable.scrollToRow(targetRow).then(() => $xeTable.triggerCurrentRowEvent(evnt, params));
        }
      },
      // 处理当前列方向键移动
      moveCurrentColumn(isLeftArrow, isRightArrow, evnt) {
        const { currentColumn } = reactData;
        const { visibleColumn } = internalData;
        let targetCol = null;
        if (currentColumn) {
          const _columnIndex = $xeTable.getVTColumnIndex(currentColumn);
          if (isLeftArrow && _columnIndex > 0) {
            targetCol = visibleColumn[_columnIndex - 1];
          } else if (isRightArrow && _columnIndex < visibleColumn.length - 1) {
            targetCol = visibleColumn[_columnIndex + 1];
          }
        } else {
          targetCol = visibleColumn[0];
        }
        if (targetCol) {
          evnt.preventDefault();
          const params = {
            $table: $xeTable,
            column: targetCol,
            columnIndex: $xeTable.getColumnIndex(targetCol),
            $columnIndex: $xeTable.getVMColumnIndex(targetCol)
          };
          $xeTable.scrollToColumn(targetCol).then(() => $xeTable.triggerCurrentColumnEvent(evnt, params));
        }
      },
      // 处理可编辑方向键移动
      moveArrowSelected(args, isLeftArrow, isUpArrow, isRightArrow, isDwArrow, evnt) {
        const { highlightCurrentRow, highlightCurrentColumn } = props;
        const rowOpts = computeRowOpts.value;
        const currentRowOpts = computeCurrentRowOpts.value;
        const columnOpts = computeColumnOpts.value;
        const currentColumnOpts = computeCurrentColumnOpts.value;
        const params = handleMoveSelected(evnt, args, isLeftArrow, isUpArrow, isRightArrow, isDwArrow);
        if (rowOpts.isCurrent || highlightCurrentRow) {
          if (currentRowOpts.isFollowSelected) {
            $xeTable.triggerCurrentRowEvent(evnt, params);
          } else {
            if ((isUpArrow || isDwArrow) && (rowOpts.isCurrent || highlightCurrentRow)) {
              $xeTable.moveCurrentRow(isUpArrow, isDwArrow, evnt);
            }
          }
        }
        if (columnOpts.isCurrent || highlightCurrentColumn) {
          if (currentColumnOpts.isFollowSelected) {
            $xeTable.triggerCurrentColumnEvent(evnt, params);
          } else {
            if ((isLeftArrow || isRightArrow) && (columnOpts.isCurrent || highlightCurrentColumn)) {
              $xeTable.moveCurrentColumn(isLeftArrow, isRightArrow, evnt);
            }
          }
        }
      },
      moveEnterSelected(args, isLeftArrow, isUpArrow, isRightArrow, isDwArrow, evnt) {
        const { highlightCurrentRow, highlightCurrentColumn } = props;
        const rowOpts = computeRowOpts.value;
        const currentRowOpts = computeCurrentRowOpts.value;
        const columnOpts = computeColumnOpts.value;
        const currentColumnOpts = computeCurrentColumnOpts.value;
        const params = handleMoveSelected(evnt, args, isLeftArrow, isUpArrow, isRightArrow, isDwArrow);
        if ((rowOpts.isCurrent || highlightCurrentRow) && currentRowOpts.isFollowSelected) {
          $xeTable.triggerCurrentRowEvent(evnt, params);
        }
        if ((columnOpts.isCurrent || highlightCurrentColumn) && currentColumnOpts.isFollowSelected) {
          $xeTable.triggerCurrentColumnEvent(evnt, params);
        }
      },
      // 已废弃，待删除
      moveSelected(args, isLeftArrow, isUpArrow, isRightArrow, isDwArrow, evnt) {
        handleMoveSelected(evnt, args, isLeftArrow, isUpArrow, isRightArrow, isDwArrow);
      },
      handleCellMousedownEvent
    };
    return keyboardMethods;
  }
});

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/module/validator/hook.js
var import_xe_utils7 = __toESM(require_xe_utils());
var { getConfig: getConfig2, validators, hooks: hooks6 } = VxeUI;
var Rule = class {
  constructor(rule) {
    Object.assign(this, {
      $options: rule,
      required: rule.required,
      min: rule.min,
      max: rule.max,
      type: rule.type,
      pattern: rule.pattern,
      validator: rule.validator,
      trigger: rule.trigger,
      maxWidth: rule.maxWidth
    });
  }
  /**
   * 获取校验不通过的消息
   * 支持国际化翻译
   */
  get content() {
    return getFuncText(this.$options.content || this.$options.message);
  }
  get message() {
    return this.content;
  }
};
var tableValidatorMethodKeys = ["fullValidate", "validate", "fullValidateField", "validateField", "clearValidate"];
hooks6.add("tableValidatorModule", {
  setupTable($xeTable) {
    const { props, reactData, internalData } = $xeTable;
    const { refValidTooltip } = $xeTable.getRefMaps();
    const { computeValidOpts, computeTreeOpts, computeEditOpts } = $xeTable.getComputeMaps();
    let validatorMethods = {};
    let validatorPrivateMethods = {};
    let validRuleErr;
    const handleValidError = (params) => {
      return new Promise((resolve) => {
        const validOpts = computeValidOpts.value;
        if (validOpts.autoPos === false) {
          $xeTable.dispatchEvent("valid-error", params, null);
          resolve();
        } else {
          $xeTable.handleEdit(params, { type: "valid-error", trigger: "call" }).then(() => {
            resolve(validatorPrivateMethods.showValidTooltip(params));
          });
        }
      });
    };
    const handleErrMsgMode = (validErrMaps) => {
      const validOpts = computeValidOpts.value;
      if (validOpts.msgMode === "single") {
        const keys = Object.keys(validErrMaps);
        const resMaps = {};
        if (keys.length) {
          const firstKey = keys[0];
          resMaps[firstKey] = validErrMaps[firstKey];
        }
        return resMaps;
      }
      return validErrMaps;
    };
    const beginValidate = (rows, cols, cb, isFull) => {
      const validRest = {};
      const { editRules, treeConfig } = props;
      const { afterFullData, pendingRowMaps, removeRowMaps } = internalData;
      const treeOpts = computeTreeOpts.value;
      const childrenField = treeOpts.children || treeOpts.childrenField;
      const validOpts = computeValidOpts.value;
      let validList;
      if (rows === true) {
        validList = afterFullData;
      } else if (rows) {
        if (import_xe_utils7.default.isFunction(rows)) {
          cb = rows;
        } else {
          validList = import_xe_utils7.default.isArray(rows) ? rows : [rows];
        }
      }
      if (!validList) {
        if ($xeTable.getInsertRecords) {
          validList = $xeTable.getInsertRecords().concat($xeTable.getUpdateRecords());
        } else {
          validList = [];
        }
      }
      const rowValidErrs = [];
      internalData._lastCallTime = Date.now();
      validRuleErr = false;
      validatorMethods.clearValidate();
      const validErrMaps = {};
      if (editRules) {
        const columns = cols && cols.length ? cols : $xeTable.getColumns();
        const handleVaild = (row) => {
          const rowid = getRowid($xeTable, row);
          if (removeRowMaps[rowid]) {
            return;
          }
          if (pendingRowMaps[rowid]) {
            return;
          }
          if (isFull || !validRuleErr) {
            const colVailds = [];
            columns.forEach((column) => {
              const field = import_xe_utils7.default.isString(column) ? column : column.field;
              if ((isFull || !validRuleErr) && import_xe_utils7.default.has(editRules, field)) {
                colVailds.push(validatorPrivateMethods.validCellRules("all", row, column).catch(({ rule, rules }) => {
                  const rest = {
                    rule,
                    rules,
                    rowIndex: $xeTable.getRowIndex(row),
                    row,
                    columnIndex: $xeTable.getColumnIndex(column),
                    column,
                    field,
                    $table: $xeTable
                  };
                  if (!validRest[field]) {
                    validRest[field] = [];
                  }
                  validErrMaps[`${getRowid($xeTable, row)}:${column.id}`] = {
                    column,
                    row,
                    rule,
                    content: rule.content
                  };
                  validRest[field].push(rest);
                  if (!isFull) {
                    validRuleErr = true;
                    return Promise.reject(rest);
                  }
                }));
              }
            });
            rowValidErrs.push(Promise.all(colVailds));
          }
        };
        if (treeConfig) {
          import_xe_utils7.default.eachTree(validList, handleVaild, { children: childrenField });
        } else {
          validList.forEach(handleVaild);
        }
        return Promise.all(rowValidErrs).then(() => {
          const ruleProps = Object.keys(validRest);
          reactData.validErrorMaps = handleErrMsgMode(validErrMaps);
          return nextTick().then(() => {
            if (ruleProps.length) {
              return Promise.reject(validRest[ruleProps[0]][0]);
            }
            if (cb) {
              cb();
            }
          });
        }).catch((firstErrParams) => {
          return new Promise((resolve, reject) => {
            const finish = () => {
              nextTick(() => {
                if (cb) {
                  cb(validRest);
                  resolve();
                } else {
                  if (getConfig2().validToReject === "obsolete") {
                    reject(validRest);
                  } else {
                    resolve(validRest);
                  }
                }
              });
            };
            const posAndFinish = () => {
              firstErrParams.cell = $xeTable.getCellElement(firstErrParams.row, firstErrParams.column);
              scrollToView(firstErrParams.cell);
              handleValidError(firstErrParams).then(finish);
            };
            if (validOpts.autoPos === false) {
              finish();
            } else {
              const row = firstErrParams.row;
              const column = firstErrParams.column;
              $xeTable.scrollToRow(row, column).then(posAndFinish);
            }
          });
        });
      } else {
        reactData.validErrorMaps = {};
      }
      return nextTick().then(() => {
        if (cb) {
          cb();
        }
      });
    };
    validatorMethods = {
      /**
       * 完整校验行，和 validate 的区别就是会给有效数据中的每一行进行校验
       */
      fullValidate(rows, cb) {
        if (true) {
          if (import_xe_utils7.default.isFunction(cb)) {
            warnLog("vxe.error.notValidators", ["fullValidate(rows, callback)", "fullValidate(rows)"]);
          }
        }
        return beginValidate(rows, null, cb, true);
      },
      /**
       * 快速校验行，如果存在记录不通过的记录，则返回不再继续校验（异步校验除外）
       */
      validate(rows, cb) {
        return beginValidate(rows, null, cb);
      },
      /**
       * 完整校验单元格，和 validateField 的区别就是会给有效数据中的每一行进行校验
       */
      fullValidateField(rows, fieldOrColumn) {
        const colList = (import_xe_utils7.default.isArray(fieldOrColumn) ? fieldOrColumn : fieldOrColumn ? [fieldOrColumn] : []).map((column) => handleFieldOrColumn($xeTable, column));
        if (colList.length) {
          return beginValidate(rows, colList, null, true);
        }
        return nextTick();
      },
      /**
       * 快速校验单元格，如果存在记录不通过的记录，则返回不再继续校验（异步校验除外）
       */
      validateField(rows, fieldOrColumn) {
        const colList = (import_xe_utils7.default.isArray(fieldOrColumn) ? fieldOrColumn : fieldOrColumn ? [fieldOrColumn] : []).map((column) => handleFieldOrColumn($xeTable, column));
        if (colList.length) {
          return beginValidate(rows, colList, null);
        }
        return nextTick();
      },
      clearValidate(rows, fieldOrColumn) {
        const { validErrorMaps } = reactData;
        const validTip = refValidTooltip.value;
        const validOpts = computeValidOpts.value;
        const rowList = import_xe_utils7.default.isArray(rows) ? rows : rows ? [rows] : [];
        const colList = (import_xe_utils7.default.isArray(fieldOrColumn) ? fieldOrColumn : fieldOrColumn ? [fieldOrColumn] : []).map((column) => handleFieldOrColumn($xeTable, column));
        let validErrMaps = {};
        if (validTip && validTip.reactData.visible) {
          validTip.close();
        }
        if (validOpts.msgMode === "single") {
          reactData.validErrorMaps = {};
          return nextTick();
        }
        if (rowList.length && colList.length) {
          validErrMaps = Object.assign({}, validErrorMaps);
          rowList.forEach((row) => {
            colList.forEach((column) => {
              const validKey = `${getRowid($xeTable, row)}:${column.id}`;
              if (validErrMaps[validKey]) {
                delete validErrMaps[validKey];
              }
            });
          });
        } else if (rowList.length) {
          const rowIdList = rowList.map((row) => `${getRowid($xeTable, row)}`);
          import_xe_utils7.default.each(validErrorMaps, (item, key) => {
            if (rowIdList.indexOf(key.split(":")[0]) > -1) {
              validErrMaps[key] = item;
            }
          });
        } else if (colList.length) {
          const colidList = colList.map((column) => `${column.id}`);
          import_xe_utils7.default.each(validErrorMaps, (item, key) => {
            if (colidList.indexOf(key.split(":")[1]) > -1) {
              validErrMaps[key] = item;
            }
          });
        }
        reactData.validErrorMaps = validErrMaps;
        return nextTick();
      }
    };
    const validErrorRuleValue = (rule, val) => {
      const { type, min, max, pattern } = rule;
      const isNumType = type === "number";
      const numVal = isNumType ? import_xe_utils7.default.toNumber(val) : import_xe_utils7.default.getSize(val);
      if (isNumType && isNaN(val)) {
        return true;
      }
      if (!import_xe_utils7.default.eqNull(min) && numVal < import_xe_utils7.default.toNumber(min)) {
        return true;
      }
      if (!import_xe_utils7.default.eqNull(max) && numVal > import_xe_utils7.default.toNumber(max)) {
        return true;
      }
      if (pattern && !(import_xe_utils7.default.isRegExp(pattern) ? pattern : new RegExp(pattern)).test(val)) {
        return true;
      }
      return false;
    };
    validatorPrivateMethods = {
      /**
       * 校验数据
       * 按表格行、列顺序依次校验（同步或异步）
       * 校验规则根据索引顺序依次校验，如果是异步则会等待校验完成才会继续校验下一列
       * 如果校验失败则，触发回调或者Promise<不通过列的错误消息>
       * 如果是传回调方式这返回一个校验不通过列的错误消息
       *
       * rule 配置：
       *  required=Boolean 是否必填
       *  min=Number 最小长度
       *  max=Number 最大长度
       *  validator=Function({ cellValue, rule, rules, row, column, rowIndex, columnIndex }) 自定义校验，接收一个 Promise
       *  trigger=blur|change 触发方式（除非特殊场景，否则默认为空就行）
       */
      validCellRules(validType, row, column, val) {
        const $xeGrid = $xeTable.xeGrid;
        const { editRules } = props;
        const { field } = column;
        const errorRules = [];
        const syncValidList = [];
        if (field && editRules) {
          const rules = import_xe_utils7.default.get(editRules, field);
          if (rules) {
            const cellValue = import_xe_utils7.default.isUndefined(val) ? import_xe_utils7.default.get(row, field) : val;
            rules.forEach((rule) => {
              const { type, trigger, required, validator } = rule;
              if (validType === "all" || !trigger || validType === trigger) {
                if (validator) {
                  const validParams = {
                    cellValue,
                    rule,
                    rules,
                    row,
                    rowIndex: $xeTable.getRowIndex(row),
                    column,
                    columnIndex: $xeTable.getColumnIndex(column),
                    field: column.field,
                    $table: $xeTable,
                    $grid: $xeGrid
                  };
                  let customValid;
                  if (import_xe_utils7.default.isString(validator)) {
                    const gvItem = validators.get(validator);
                    if (gvItem) {
                      const tcvMethod = gvItem.tableCellValidatorMethod || gvItem.cellValidatorMethod;
                      if (tcvMethod) {
                        customValid = tcvMethod(validParams);
                      } else {
                        if (true) {
                          warnLog("vxe.error.notValidators", [validator]);
                        }
                      }
                    } else {
                      errLog("vxe.error.notValidators", [validator]);
                    }
                  } else {
                    customValid = validator(validParams);
                  }
                  if (customValid) {
                    if (import_xe_utils7.default.isError(customValid)) {
                      validRuleErr = true;
                      errorRules.push(new Rule({ type: "custom", trigger, content: customValid.message, rule: new Rule(rule) }));
                    } else if (customValid.catch) {
                      syncValidList.push(customValid.catch((e) => {
                        validRuleErr = true;
                        errorRules.push(new Rule({ type: "custom", trigger, content: e && e.message ? e.message : rule.content || rule.message, rule: new Rule(rule) }));
                      }));
                    }
                  }
                } else {
                  const isArrType = type === "array";
                  const isArrVal = import_xe_utils7.default.isArray(cellValue);
                  let hasEmpty = true;
                  if (isArrType || isArrVal) {
                    hasEmpty = !isArrVal || !cellValue.length;
                  } else if (import_xe_utils7.default.isString(cellValue)) {
                    hasEmpty = eqEmptyValue(cellValue.trim());
                  } else {
                    hasEmpty = eqEmptyValue(cellValue);
                  }
                  if (required ? hasEmpty || validErrorRuleValue(rule, cellValue) : !hasEmpty && validErrorRuleValue(rule, cellValue)) {
                    validRuleErr = true;
                    errorRules.push(new Rule(rule));
                  }
                }
              }
            });
          }
        }
        return Promise.all(syncValidList).then(() => {
          if (errorRules.length) {
            const rest = { rules: errorRules, rule: errorRules[0] };
            return Promise.reject(rest);
          }
        });
      },
      hasCellRules(type, row, column) {
        const { editRules } = props;
        const { field } = column;
        if (field && editRules) {
          const rules = import_xe_utils7.default.get(editRules, field);
          return rules && !!import_xe_utils7.default.find(rules, (rule) => type === "all" || !rule.trigger || type === rule.trigger);
        }
        return false;
      },
      /**
       * 触发校验
       */
      triggerValidate(type) {
        const { editConfig, editRules } = props;
        const { editStore } = reactData;
        const { actived } = editStore;
        const editOpts = computeEditOpts.value;
        const validOpts = computeValidOpts.value;
        if (editRules && validOpts.msgMode === "single") {
          reactData.validErrorMaps = {};
        }
        if (editConfig && editRules && actived.row) {
          const { row, column, cell } = actived.args;
          if (validatorPrivateMethods.hasCellRules(type, row, column)) {
            return validatorPrivateMethods.validCellRules(type, row, column).then(() => {
              if (editOpts.mode === "row") {
                validatorMethods.clearValidate(row, column);
              }
            }).catch(({ rule }) => {
              if (!rule.trigger || type === rule.trigger) {
                const rest = { rule, row, column, cell };
                validatorPrivateMethods.showValidTooltip(rest);
                return Promise.reject(rest);
              }
              return Promise.resolve();
            });
          }
        }
        return Promise.resolve();
      },
      /**
       * 弹出校验错误提示
       */
      showValidTooltip(params) {
        const { height } = props;
        const { tableData, validStore, validErrorMaps } = reactData;
        const { rule, row, column, cell } = params;
        const validOpts = computeValidOpts.value;
        const validTip = refValidTooltip.value;
        const content = rule.content;
        validStore.visible = true;
        if (validOpts.msgMode === "single") {
          reactData.validErrorMaps = {
            [`${getRowid($xeTable, row)}:${column.id}`]: {
              column,
              row,
              rule,
              content
            }
          };
        } else {
          reactData.validErrorMaps = Object.assign({}, validErrorMaps, {
            [`${getRowid($xeTable, row)}:${column.id}`]: {
              column,
              row,
              rule,
              content
            }
          });
        }
        $xeTable.dispatchEvent("valid-error", params, null);
        if (validTip) {
          if (validTip && (validOpts.message === "tooltip" || validOpts.message === "default" && !height && tableData.length < 2)) {
            return validTip.open(cell, content);
          }
        }
        return nextTick();
      }
    };
    return Object.assign(Object.assign({}, validatorMethods), validatorPrivateMethods);
  },
  setupGrid($xeGrid) {
    return $xeGrid.extendTableMethods(tableValidatorMethodKeys);
  }
});

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/module/custom/hook.js
var import_xe_utils8 = __toESM(require_xe_utils());
var tableCustomMethodKeys = ["openCustom", "closeCustom", "saveCustom", "cancelCustom", "resetCustom", "toggleCustomAllCheckbox", "setCustomAllCheckbox"];
VxeUI.hooks.add("tableCustomModule", {
  setupTable($xeTable) {
    const { reactData, internalData } = $xeTable;
    const { computeCustomOpts } = $xeTable.getComputeMaps();
    const { refElem } = $xeTable.getRefMaps();
    const $xeGrid = $xeTable.xeGrid;
    const calcMaxHeight = () => {
      const { customStore } = reactData;
      const el = refElem.value;
      let tableHeight = 0;
      if (el) {
        tableHeight = el.clientHeight - 28;
      }
      customStore.maxHeight = Math.max(88, tableHeight);
    };
    const openCustom = () => {
      const { initStore, customStore } = reactData;
      customStore.visible = true;
      initStore.custom = true;
      handleUpdateCustomColumn();
      checkCustomStatus();
      calcMaxHeight();
      return nextTick().then(() => calcMaxHeight());
    };
    const handleUpdateCustomColumn = () => {
      const { customStore } = reactData;
      const { collectColumn } = internalData;
      if (customStore.visible) {
        const sortMaps = {};
        const fixedMaps = {};
        const visibleMaps = {};
        import_xe_utils8.default.eachTree(collectColumn, (column) => {
          const colid = column.getKey();
          column.renderFixed = column.fixed;
          column.renderVisible = column.visible;
          column.renderResizeWidth = column.renderWidth;
          sortMaps[colid] = column.renderSortNumber;
          fixedMaps[colid] = column.fixed;
          visibleMaps[colid] = column.visible;
        });
        customStore.oldSortMaps = sortMaps;
        customStore.oldFixedMaps = fixedMaps;
        customStore.oldVisibleMaps = visibleMaps;
        reactData.customColumnList = collectColumn.slice(0);
      }
    };
    const closeCustom = () => {
      const { customStore } = reactData;
      const customOpts = computeCustomOpts.value;
      if (customStore.visible) {
        customStore.visible = false;
        if (!customOpts.immediate) {
          $xeTable.handleCustom();
        }
      }
      return nextTick();
    };
    const saveCustom = () => {
      const { customColumnList } = reactData;
      const customOpts = computeCustomOpts.value;
      const { allowVisible, allowSort, allowFixed, allowResizable } = customOpts;
      import_xe_utils8.default.eachTree(customColumnList, (column, index, items, path, parentColumn) => {
        if (parentColumn) {
          column.fixed = parentColumn.fixed;
        } else {
          if (allowSort) {
            const sortIndex = index + 1;
            column.renderSortNumber = sortIndex;
          }
          if (allowFixed) {
            column.fixed = column.renderFixed;
          }
        }
        if (allowResizable) {
          if (column.renderVisible && (!column.children || column.children.length)) {
            if (column.renderResizeWidth !== column.renderWidth) {
              column.resizeWidth = column.renderResizeWidth;
              column.renderWidth = column.renderResizeWidth;
            }
          }
        }
        if (allowVisible) {
          column.visible = column.renderVisible;
        }
      });
      reactData.isCustomStatus = true;
      reactData.isDragColMove = true;
      setTimeout(() => {
        reactData.isDragColMove = false;
      }, 1e3);
      return $xeTable.saveCustomStore("confirm");
    };
    const cancelCustom = () => {
      const { customColumnList, customStore } = reactData;
      const { oldSortMaps, oldFixedMaps, oldVisibleMaps } = customStore;
      const customOpts = computeCustomOpts.value;
      const { allowVisible, allowSort, allowFixed, allowResizable } = customOpts;
      import_xe_utils8.default.eachTree(customColumnList, (column) => {
        const colid = column.getKey();
        const visible = !!oldVisibleMaps[colid];
        const fixed = oldFixedMaps[colid] || "";
        if (allowVisible) {
          column.renderVisible = visible;
          column.visible = visible;
        }
        if (allowFixed) {
          column.renderFixed = fixed;
          column.fixed = fixed;
        }
        if (allowSort) {
          column.renderSortNumber = oldSortMaps[colid] || 0;
        }
        if (allowResizable) {
          column.renderResizeWidth = column.renderWidth;
        }
      }, { children: "children" });
      return nextTick();
    };
    const setCustomAllCheckbox = (checked) => {
      const { customStore } = reactData;
      const { customColumnList } = reactData;
      const customOpts = computeCustomOpts.value;
      const { checkMethod, visibleMethod } = customOpts;
      const isAll = !!checked;
      if (customOpts.immediate) {
        import_xe_utils8.default.eachTree(customColumnList, (column) => {
          if (visibleMethod && !visibleMethod({ $table: $xeTable, column })) {
            return;
          }
          if (checkMethod && !checkMethod({ $table: $xeTable, column })) {
            return;
          }
          column.visible = isAll;
          column.renderVisible = isAll;
          column.halfVisible = false;
        });
        customStore.isAll = isAll;
        reactData.isCustomStatus = true;
        $xeTable.handleCustom();
        $xeTable.saveCustomStore("update:visible");
      } else {
        import_xe_utils8.default.eachTree(customColumnList, (column) => {
          if (visibleMethod && !visibleMethod({ $table: $xeTable, column })) {
            return;
          }
          if (checkMethod && !checkMethod({ $table: $xeTable, column })) {
            return;
          }
          column.renderVisible = isAll;
          column.halfVisible = false;
        });
        customStore.isAll = isAll;
      }
      $xeTable.checkCustomStatus();
      return nextTick();
    };
    const customMethods = {
      openCustom,
      closeCustom,
      saveCustom,
      cancelCustom,
      resetCustom(options) {
        const { collectColumn } = internalData;
        const customOpts = computeCustomOpts.value;
        const { checkMethod } = customOpts;
        const opts = Object.assign({
          visible: true,
          resizable: options === true,
          fixed: options === true,
          sort: options === true
        }, options);
        import_xe_utils8.default.eachTree(collectColumn, (column) => {
          if (opts.resizable) {
            column.resizeWidth = 0;
          }
          if (opts.fixed) {
            column.fixed = column.defaultFixed;
          }
          if (opts.sort) {
            column.renderSortNumber = column.sortNumber;
          }
          if (!checkMethod || checkMethod({ $table: $xeTable, column })) {
            column.visible = column.defaultVisible;
          }
          column.renderResizeWidth = column.renderWidth;
        });
        reactData.isCustomStatus = false;
        $xeTable.saveCustomStore("reset");
        return $xeTable.handleCustom();
      },
      toggleCustomAllCheckbox() {
        const { customStore } = reactData;
        const isAll = !customStore.isAll;
        return setCustomAllCheckbox(isAll);
      },
      setCustomAllCheckbox
    };
    const checkCustomStatus = () => {
      const { customStore } = reactData;
      const { collectColumn } = internalData;
      const customOpts = computeCustomOpts.value;
      const { checkMethod } = customOpts;
      customStore.isAll = collectColumn.every((column) => (checkMethod ? !checkMethod({ $table: $xeTable, column }) : false) || column.renderVisible);
      customStore.isIndeterminate = !customStore.isAll && collectColumn.some((column) => (!checkMethod || checkMethod({ $table: $xeTable, column })) && (column.renderVisible || column.halfVisible));
    };
    const emitCustomEvent = (type, evnt) => {
      const comp = $xeGrid || $xeTable;
      comp.dispatchEvent("custom", { type }, evnt);
    };
    const customPrivateMethods = {
      checkCustomStatus,
      emitCustomEvent,
      triggerCustomEvent(evnt) {
        const reactData2 = $xeTable.reactData;
        const { customStore } = reactData2;
        if (customStore.visible) {
          closeCustom();
          emitCustomEvent("close", evnt);
        } else {
          customStore.btnEl = evnt.target;
          openCustom();
          emitCustomEvent("open", evnt);
        }
      },
      customOpenEvent(evnt) {
        const reactData2 = $xeTable.reactData;
        const { customStore } = reactData2;
        if (!customStore.visible) {
          customStore.activeBtn = true;
          customStore.btnEl = evnt.target;
          $xeTable.openCustom();
          $xeTable.emitCustomEvent("open", evnt);
        }
      },
      customCloseEvent(evnt) {
        const reactData2 = $xeTable.reactData;
        const { customStore } = reactData2;
        if (customStore.visible) {
          customStore.activeBtn = false;
          $xeTable.closeCustom();
          $xeTable.emitCustomEvent("close", evnt);
        }
      },
      handleUpdateCustomColumn
    };
    return Object.assign(Object.assign({}, customMethods), customPrivateMethods);
  },
  setupGrid($xeGrid) {
    return $xeGrid.extendTableMethods(tableCustomMethodKeys);
  }
});

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/render/index.js
var import_xe_utils9 = __toESM(require_xe_utils());
var { getConfig: getConfig3, renderer: renderer4, getI18n: getI18n3, getComponent } = VxeUI;
var componentDefaultModelProp = "modelValue";
var defaultCompProps = {};
function handleDefaultValue(value, defaultVal, initVal) {
  return import_xe_utils9.default.eqNull(value) ? import_xe_utils9.default.eqNull(defaultVal) ? initVal : defaultVal : value;
}
function parseDate(value, props) {
  return value && props.valueFormat ? import_xe_utils9.default.toStringDate(value, props.valueFormat) : value;
}
function getFormatDate(value, props, defaultFormat) {
  const { dateConfig = {} } = props;
  return import_xe_utils9.default.toDateString(parseDate(value, props), dateConfig.labelFormat || defaultFormat);
}
function getLabelFormatDate(value, props) {
  return getFormatDate(value, props, getI18n3(`vxe.input.date.labelFormat.${props.type || "date"}`));
}
function getOldComponentName(name) {
  return `vxe-${name.replace("$", "")}`;
}
function getDefaultComponent({ name }) {
  return getComponent(name);
}
function getOldComponent({ name }) {
  return resolveComponent(getOldComponentName(name));
}
function handleConfirmFilter(params, checked, option) {
  const { $panel } = params;
  $panel.changeOption({}, checked, option);
}
function getNativeAttrs(renderOpts) {
  let { name, attrs } = renderOpts;
  if (name === "input") {
    attrs = Object.assign({ type: "text" }, attrs);
  }
  return attrs;
}
function getInputImmediateModel(renderOpts) {
  const { name, immediate, props } = renderOpts;
  if (!immediate) {
    if (name === "VxeInput" || name === "$input") {
      const { type } = props || {};
      return !(!type || type === "text" || type === "number" || type === "integer" || type === "float");
    }
    if (name === "input" || name === "textarea" || name === "$textarea") {
      return false;
    }
    return true;
  }
  return immediate;
}
function getCellEditProps(renderOpts, params, value, defaultProps) {
  return import_xe_utils9.default.assign({ immediate: getInputImmediateModel(renderOpts) }, defaultCompProps, defaultProps, renderOpts.props, { [componentDefaultModelProp]: value });
}
function getCellEditFilterProps(renderOpts, params, value, defaultProps) {
  return import_xe_utils9.default.assign({}, defaultCompProps, defaultProps, renderOpts.props, { [componentDefaultModelProp]: value });
}
function isImmediateCell(renderOpts, params) {
  return params.$type === "cell" || getInputImmediateModel(renderOpts);
}
function getCellLabelVNs(renderOpts, params, cellLabel, opts) {
  const { placeholder } = renderOpts;
  return [
    h("span", {
      class: ["vxe-cell--label", opts ? opts.class : ""]
    }, placeholder && isEmptyValue(cellLabel) ? [
      h("span", {
        class: "vxe-cell--placeholder"
      }, formatText(getFuncText(placeholder), 1))
    ] : formatText(cellLabel, 1))
  ];
}
function getNativeElementOns(renderOpts, params, eFns) {
  const { events } = renderOpts;
  const modelEvent = getModelEvent(renderOpts);
  const changeEvent = getChangeEvent(renderOpts);
  const { model: modelFunc, change: changeFunc, blur: blurFunc } = eFns || {};
  const isSameEvent = changeEvent === modelEvent;
  const ons = {};
  if (events) {
    import_xe_utils9.default.objectEach(events, (func, key) => {
      ons[getOnName(key)] = function(...args) {
        func(params, ...args);
      };
    });
  }
  if (modelFunc) {
    ons[getOnName(modelEvent)] = function(targetEvnt) {
      modelFunc(targetEvnt);
      if (isSameEvent && changeFunc) {
        changeFunc(targetEvnt);
      }
      if (events && events[modelEvent]) {
        events[modelEvent](params, targetEvnt);
      }
    };
  }
  if (!isSameEvent && changeFunc) {
    ons[getOnName(changeEvent)] = function(evnt) {
      changeFunc(evnt);
      if (events && events[changeEvent]) {
        events[changeEvent](params, evnt);
      }
    };
  }
  if (blurFunc) {
    ons[getOnName(blurEvent)] = function(evnt) {
      blurFunc(evnt);
      if (events && events[blurEvent]) {
        events[blurEvent](params, evnt);
      }
    };
  }
  return ons;
}
var blurEvent = "blur";
function getComponentOns(renderOpts, params, eFns) {
  const { events } = renderOpts;
  const modelEvent = getModelEvent(renderOpts);
  const changeEvent = getChangeEvent(renderOpts);
  const { model: modelFunc, change: changeFunc, blur: blurFunc } = eFns || {};
  const ons = {};
  import_xe_utils9.default.objectEach(events, (func, key) => {
    ons[getOnName(key)] = function(...args) {
      if (!import_xe_utils9.default.isFunction(func)) {
        errLog("vxe.error.errFunc", [func]);
      }
      func(params, ...args);
    };
  });
  if (modelFunc) {
    ons[getOnName(modelEvent)] = function(targetEvnt) {
      modelFunc(targetEvnt);
      if (events && events[modelEvent]) {
        events[modelEvent](params, targetEvnt);
      }
    };
  }
  if (changeFunc) {
    ons[getOnName(changeEvent)] = function(...args) {
      changeFunc(...args);
      if (events && events[changeEvent]) {
        events[changeEvent](params, ...args);
      }
    };
  }
  if (blurFunc) {
    ons[getOnName(blurEvent)] = function(...args) {
      blurFunc(...args);
      if (events && events[blurEvent]) {
        events[blurEvent](params, ...args);
      }
    };
  }
  return ons;
}
function getEditOns(renderOpts, params) {
  const { $table, row, column } = params;
  const { name } = renderOpts;
  const { model } = column;
  const isImmediate = isImmediateCell(renderOpts, params);
  return getComponentOns(renderOpts, params, {
    model(cellValue) {
      model.update = true;
      model.value = cellValue;
      if (isImmediate) {
        setCellValue(row, column, cellValue);
      }
    },
    change(eventParams) {
      if (!isImmediate && name && ["VxeInput", "VxeNumberInput", "VxeTextarea", "$input", "$textarea"].includes(name)) {
        const cellValue = eventParams.value;
        model.update = true;
        model.value = cellValue;
        $table.updateStatus(params, cellValue);
      } else {
        $table.updateStatus(params);
      }
    },
    blur() {
      if (isImmediate) {
        $table.handleCellRuleUpdateStatus("blur", params);
      } else {
        $table.handleCellRuleUpdateStatus("blur", params, model.value);
      }
    }
  });
}
function getFilterOns(renderOpts, params, option) {
  return getComponentOns(renderOpts, params, {
    model(value) {
      option.data = value;
    },
    change() {
      handleConfirmFilter(params, !import_xe_utils9.default.eqNull(option.data), option);
    },
    blur() {
      handleConfirmFilter(params, !import_xe_utils9.default.eqNull(option.data), option);
    }
  });
}
function getNativeEditOns(renderOpts, params) {
  const { $table, row, column } = params;
  const { model } = column;
  return getNativeElementOns(renderOpts, params, {
    model(evnt) {
      const targetEl = evnt.target;
      if (targetEl) {
        const cellValue = targetEl.value;
        if (isImmediateCell(renderOpts, params)) {
          setCellValue(row, column, cellValue);
        } else {
          model.update = true;
          model.value = cellValue;
        }
      }
    },
    change(evnt) {
      const targetEl = evnt.target;
      if (targetEl) {
        const cellValue = targetEl.value;
        $table.updateStatus(params, cellValue);
      }
    },
    blur(evnt) {
      const targetEl = evnt.target;
      if (targetEl) {
        const cellValue = targetEl.value;
        $table.updateStatus(params, cellValue);
      }
    }
  });
}
function getNativeFilterOns(renderOpts, params, option) {
  return getNativeElementOns(renderOpts, params, {
    model(evnt) {
      const targetEl = evnt.target;
      if (targetEl) {
        option.data = targetEl.value;
      }
    },
    change() {
      handleConfirmFilter(params, !import_xe_utils9.default.eqNull(option.data), option);
    },
    blur() {
      handleConfirmFilter(params, !import_xe_utils9.default.eqNull(option.data), option);
    }
  });
}
function nativeEditRender(renderOpts, params) {
  const { row, column } = params;
  const { name } = renderOpts;
  const cellValue = isImmediateCell(renderOpts, params) ? getCellValue(row, column) : column.model.value;
  return [
    h(name, Object.assign(Object.assign(Object.assign({ class: `vxe-default-${name}` }, getNativeAttrs(renderOpts)), { value: cellValue }), getNativeEditOns(renderOpts, params)))
  ];
}
function buttonCellRender(renderOpts, params) {
  return [
    h(getDefaultComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, null)), getComponentOns(renderOpts, params)))
  ];
}
function defaultEditRender(renderOpts, params) {
  const { row, column } = params;
  const cellValue = getCellValue(row, column);
  return [
    h(getDefaultComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue)), getEditOns(renderOpts, params)))
  ];
}
function checkboxEditRender(renderOpts, params) {
  const { row, column } = params;
  const cellValue = getCellValue(row, column);
  return [
    h(getDefaultComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue)), getEditOns(renderOpts, params)))
  ];
}
function radioAndCheckboxGroupEditRender(renderOpts, params) {
  const { options } = renderOpts;
  const { row, column } = params;
  const cellValue = getCellValue(row, column);
  return [
    h(getDefaultComponent(renderOpts), Object.assign(Object.assign({ options }, getCellEditProps(renderOpts, params, cellValue)), getEditOns(renderOpts, params)))
  ];
}
function oldEditRender(renderOpts, params) {
  const { row, column } = params;
  const cellValue = getCellValue(row, column);
  return [
    h(getOldComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue)), getEditOns(renderOpts, params)))
  ];
}
function oldButtonEditRender(renderOpts, params) {
  return [
    h(getComponent("vxe-button"), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, null)), getComponentOns(renderOpts, params)))
  ];
}
function oldButtonsEditRender(renderOpts, params) {
  return renderOpts.children.map((childRenderOpts) => oldButtonEditRender(childRenderOpts, params)[0]);
}
function renderNativeOptgroups(renderOpts, params, renderOptionsMethods) {
  const { optionGroups, optionGroupProps = {} } = renderOpts;
  const groupOptions = optionGroupProps.options || "options";
  const groupLabel = optionGroupProps.label || "label";
  return optionGroups.map((group, gIndex) => {
    return h("optgroup", {
      key: gIndex,
      label: group[groupLabel]
    }, renderOptionsMethods(group[groupOptions], renderOpts, params));
  });
}
function renderNativeOptions(options, renderOpts, params) {
  const { optionProps = {} } = renderOpts;
  const { row, column } = params;
  const labelProp = optionProps.label || "label";
  const valueProp = optionProps.value || "value";
  const disabledProp = optionProps.disabled || "disabled";
  const cellValue = isImmediateCell(renderOpts, params) ? getCellValue(row, column) : column.model.value;
  return options.map((option, oIndex) => {
    return h("option", {
      key: oIndex,
      value: option[valueProp],
      disabled: option[disabledProp],
      /* eslint-disable eqeqeq */
      selected: option[valueProp] == cellValue
    }, option[labelProp]);
  });
}
function nativeFilterRender(renderOpts, params) {
  const { column } = params;
  const { name } = renderOpts;
  const attrs = getNativeAttrs(renderOpts);
  return column.filters.map((option, oIndex) => {
    return h(name, Object.assign(Object.assign(Object.assign({ key: oIndex, class: `vxe-default-${name}` }, attrs), { value: option.data }), getNativeFilterOns(renderOpts, params, option)));
  });
}
function defaultFilterRender(renderOpts, params) {
  const { column } = params;
  return column.filters.map((option, oIndex) => {
    const optionValue = option.data;
    return h(getDefaultComponent(renderOpts), Object.assign(Object.assign({ key: oIndex }, getCellEditFilterProps(renderOpts, renderOpts, optionValue)), getFilterOns(renderOpts, params, option)));
  });
}
function oldFilterRender(renderOpts, params) {
  const { column } = params;
  return column.filters.map((option, oIndex) => {
    const optionValue = option.data;
    return h(getOldComponent(renderOpts), Object.assign(Object.assign({ key: oIndex }, getCellEditFilterProps(renderOpts, renderOpts, optionValue)), getFilterOns(renderOpts, params, option)));
  });
}
function handleFilterMethod({ option, row, column }) {
  const { data } = option;
  const cellValue = import_xe_utils9.default.get(row, column.field);
  return cellValue == data;
}
function handleInputFilterMethod({ option, row, column }) {
  const { data } = option;
  const cellValue = import_xe_utils9.default.get(row, column.field);
  return import_xe_utils9.default.toValueString(cellValue).indexOf(data) > -1;
}
function nativeSelectEditRender(renderOpts, params) {
  return [
    h("select", Object.assign(Object.assign({ class: "vxe-default-select" }, getNativeAttrs(renderOpts)), getNativeEditOns(renderOpts, params)), renderOpts.optionGroups ? renderNativeOptgroups(renderOpts, params, renderNativeOptions) : renderNativeOptions(renderOpts.options, renderOpts, params))
  ];
}
function defaultSelectEditRender(renderOpts, params) {
  const { row, column } = params;
  const { options, optionProps, optionGroups, optionGroupProps } = renderOpts;
  const cellValue = getCellValue(row, column);
  return [
    h(getDefaultComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue, { options, optionProps, optionGroups, optionGroupProps })), getEditOns(renderOpts, params)))
  ];
}
function defaultTableOrTreeSelectEditRender(renderOpts, params) {
  const { row, column } = params;
  const { options, optionProps } = renderOpts;
  const cellValue = getCellValue(row, column);
  return [
    h(getDefaultComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue, { options, optionProps })), getEditOns(renderOpts, params)))
  ];
}
function oldSelectEditRender(renderOpts, params) {
  const { row, column } = params;
  const { options, optionProps, optionGroups, optionGroupProps } = renderOpts;
  const cellValue = getCellValue(row, column);
  return [
    h(getOldComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue, { options, optionProps, optionGroups, optionGroupProps })), getEditOns(renderOpts, params)))
  ];
}
function getSelectCellValue(renderOpts, { row, column }) {
  const { options, optionGroups, optionProps = {}, optionGroupProps = {} } = renderOpts;
  const cellValue = import_xe_utils9.default.get(row, column.field);
  let selectItem;
  const labelProp = optionProps.label || "label";
  const valueProp = optionProps.value || "value";
  if (!(cellValue === null || cellValue === void 0)) {
    return import_xe_utils9.default.map(import_xe_utils9.default.isArray(cellValue) ? cellValue : [cellValue], optionGroups ? (value) => {
      const groupOptions = optionGroupProps.options || "options";
      for (let index = 0; index < optionGroups.length; index++) {
        selectItem = import_xe_utils9.default.find(optionGroups[index][groupOptions], (item) => item[valueProp] == value);
        if (selectItem) {
          break;
        }
      }
      return selectItem ? selectItem[labelProp] : value;
    } : (value) => {
      selectItem = import_xe_utils9.default.find(options, (item) => item[valueProp] == value);
      return selectItem ? selectItem[labelProp] : value;
    }).join(", ");
  }
  return "";
}
function handleExportSelectMethod(params) {
  const { row, column, options } = params;
  return options.original ? getCellValue(row, column) : getSelectCellValue(column.editRender || column.cellRender, params);
}
function getTreeSelectCellValue(renderOpts, { row, column }) {
  const { options, optionProps = {} } = renderOpts;
  const cellValue = import_xe_utils9.default.get(row, column.field);
  const labelProp = optionProps.label || "label";
  const valueProp = optionProps.value || "value";
  const childrenProp = optionProps.children || "children";
  if (!(cellValue === null || cellValue === void 0)) {
    const keyMaps = {};
    import_xe_utils9.default.eachTree(options, (item) => {
      keyMaps[import_xe_utils9.default.get(item, valueProp)] = item;
    }, { children: childrenProp });
    return import_xe_utils9.default.map(import_xe_utils9.default.isArray(cellValue) ? cellValue : [cellValue], (value) => {
      const item = keyMaps[value];
      return item ? import_xe_utils9.default.get(item, labelProp) : item;
    }).join(", ");
  }
  return "";
}
function handleExportTreeSelectMethod(params) {
  const { row, column, options } = params;
  return options.original ? getCellValue(row, column) : getTreeSelectCellValue(column.editRender || column.cellRender, params);
}
function handleNumberCell(renderOpts, params) {
  const { props = {}, showNegativeStatus } = renderOpts;
  const { row, column } = params;
  const { type } = props;
  let cellValue = import_xe_utils9.default.get(row, column.field);
  let isNegative = false;
  if (!isEmptyValue(cellValue)) {
    const numberInputConfig = getConfig3().numberInput || {};
    if (type === "float") {
      const autoFill = handleDefaultValue(props.autoFill, numberInputConfig.autoFill, true);
      const digits = handleDefaultValue(props.digits, numberInputConfig.digits, 1);
      cellValue = import_xe_utils9.default.toFixed(import_xe_utils9.default.floor(cellValue, digits), digits);
      if (!autoFill) {
        cellValue = import_xe_utils9.default.toNumber(cellValue);
      }
      if (showNegativeStatus) {
        if (cellValue < 0) {
          isNegative = true;
        }
      }
    } else if (type === "amount") {
      const autoFill = handleDefaultValue(props.autoFill, numberInputConfig.autoFill, true);
      const digits = handleDefaultValue(props.digits, numberInputConfig.digits, 2);
      const showCurrency = handleDefaultValue(props.showCurrency, numberInputConfig.showCurrency, false);
      cellValue = import_xe_utils9.default.toNumber(cellValue);
      if (showNegativeStatus) {
        if (cellValue < 0) {
          isNegative = true;
        }
      }
      cellValue = import_xe_utils9.default.commafy(cellValue, { digits });
      if (!autoFill) {
        const [iStr, dStr] = cellValue.split(".");
        if (dStr) {
          const dRest = dStr.replace(/0+$/, "");
          cellValue = dRest ? [iStr, ".", dRest].join("") : iStr;
        }
      }
      if (showCurrency) {
        cellValue = `${props.currencySymbol || numberInputConfig.currencySymbol || getI18n3("vxe.numberInput.currencySymbol") || ""}${cellValue}`;
      }
    } else {
      if (showNegativeStatus) {
        if (import_xe_utils9.default.toNumber(cellValue) < 0) {
          isNegative = true;
        }
      }
    }
  }
  return getCellLabelVNs(renderOpts, params, cellValue, isNegative ? {
    class: "is--negative"
  } : {});
}
renderer4.mixin({
  input: {
    tableAutoFocus: "input",
    renderTableEdit: nativeEditRender,
    renderTableDefault: nativeEditRender,
    renderTableFilter: nativeFilterRender,
    tableFilterDefaultMethod: handleInputFilterMethod
  },
  textarea: {
    tableAutoFocus: "textarea",
    renderTableEdit: nativeEditRender
  },
  select: {
    renderTableEdit: nativeSelectEditRender,
    renderTableDefault: nativeSelectEditRender,
    renderTableCell(renderOpts, params) {
      return getCellLabelVNs(renderOpts, params, getSelectCellValue(renderOpts, params));
    },
    renderTableFilter(renderOpts, params) {
      const { column } = params;
      return column.filters.map((option, oIndex) => {
        return h("select", Object.assign(Object.assign({ key: oIndex, class: "vxe-default-select" }, getNativeAttrs(renderOpts)), getNativeFilterOns(renderOpts, params, option)), renderOpts.optionGroups ? renderNativeOptgroups(renderOpts, params, renderNativeOptions) : renderNativeOptions(renderOpts.options, renderOpts, params));
      });
    },
    tableFilterDefaultMethod: handleFilterMethod,
    tableExportMethod: handleExportSelectMethod
  },
  VxeInput: {
    tableAutoFocus: "input",
    renderTableEdit: defaultEditRender,
    renderTableCell(renderOpts, params) {
      const { props = {} } = renderOpts;
      const { row, column } = params;
      const inputConfig = getConfig3().input || {};
      const digits = props.digits || inputConfig.digits || 2;
      let cellValue = import_xe_utils9.default.get(row, column.field);
      if (cellValue) {
        switch (props.type) {
          case "date":
          case "week":
          case "month":
          case "quarter":
          case "year":
            cellValue = getLabelFormatDate(cellValue, props);
            break;
          case "float":
            cellValue = import_xe_utils9.default.toFixed(import_xe_utils9.default.floor(cellValue, digits), digits);
            break;
        }
      }
      return getCellLabelVNs(renderOpts, params, cellValue);
    },
    renderTableDefault: defaultEditRender,
    renderTableFilter: defaultFilterRender,
    tableFilterDefaultMethod: handleInputFilterMethod
  },
  FormatNumberInput: {
    renderTableDefault: handleNumberCell,
    tableFilterDefaultMethod: handleInputFilterMethod,
    tableExportMethod(params) {
      const { row, column } = params;
      const cellValue = import_xe_utils9.default.get(row, column.field);
      return cellValue;
    }
  },
  VxeNumberInput: {
    tableAutoFocus: "input",
    renderTableEdit: defaultEditRender,
    renderTableCell: handleNumberCell,
    renderTableFooter(renderOpts, params) {
      const { props = {} } = renderOpts;
      const { row, column, _columnIndex } = params;
      const { type } = props;
      const itemValue = import_xe_utils9.default.isArray(row) ? row[_columnIndex] : import_xe_utils9.default.get(row, column.field);
      if (import_xe_utils9.default.isNumber(itemValue)) {
        const numberInputConfig = getConfig3().numberInput || {};
        if (type === "float") {
          const autoFill = handleDefaultValue(props.autoFill, numberInputConfig.autoFill, true);
          const digits = handleDefaultValue(props.digits, numberInputConfig.digits, 1);
          let amountLabel = import_xe_utils9.default.toFixed(import_xe_utils9.default.floor(itemValue, digits), digits);
          if (!autoFill) {
            amountLabel = import_xe_utils9.default.toNumber(amountLabel);
          }
          return amountLabel;
        } else if (type === "amount") {
          const autoFill = handleDefaultValue(props.autoFill, numberInputConfig.autoFill, true);
          const digits = handleDefaultValue(props.digits, numberInputConfig.digits, 2);
          const showCurrency = handleDefaultValue(props.showCurrency, numberInputConfig.showCurrency, false);
          let amountLabel = import_xe_utils9.default.commafy(import_xe_utils9.default.toNumber(itemValue), { digits });
          if (!autoFill) {
            const [iStr, dStr] = amountLabel.split(".");
            if (dStr) {
              const dRest = dStr.replace(/0+$/, "");
              amountLabel = dRest ? [iStr, ".", dRest].join("") : iStr;
            }
          }
          if (showCurrency) {
            amountLabel = `${props.currencySymbol || numberInputConfig.currencySymbol || getI18n3("vxe.numberInput.currencySymbol") || ""}${amountLabel}`;
          }
          return amountLabel;
        }
      }
      return getFuncText(itemValue, 1);
    },
    renderTableDefault: defaultEditRender,
    renderTableFilter: defaultFilterRender,
    tableFilterDefaultMethod: handleInputFilterMethod,
    tableExportMethod(params) {
      const { row, column } = params;
      const cellValue = import_xe_utils9.default.get(row, column.field);
      return cellValue;
    }
  },
  VxeDatePicker: {
    tableAutoFocus: "input",
    renderTableEdit: defaultEditRender,
    renderTableCell(renderOpts, params) {
      const { props = {} } = renderOpts;
      const { row, column } = params;
      let cellValue = import_xe_utils9.default.get(row, column.field);
      if (cellValue) {
        if (props.type !== "time") {
          cellValue = getLabelFormatDate(cellValue, props);
        }
      }
      return getCellLabelVNs(renderOpts, params, cellValue);
    },
    renderTableDefault: defaultEditRender,
    renderTableFilter: defaultFilterRender,
    tableFilterDefaultMethod: handleFilterMethod
  },
  VxeTextarea: {
    tableAutoFocus: "textarea",
    renderTableEdit: defaultEditRender,
    renderTableCell(renderOpts, params) {
      const { row, column } = params;
      const cellValue = import_xe_utils9.default.get(row, column.field);
      return getCellLabelVNs(renderOpts, params, cellValue);
    }
  },
  VxeButton: {
    renderTableDefault: buttonCellRender
  },
  VxeButtonGroup: {
    renderTableDefault(renderOpts, params) {
      const { options } = renderOpts;
      return [
        h(getDefaultComponent(renderOpts), Object.assign(Object.assign({ options }, getCellEditProps(renderOpts, params, null)), getComponentOns(renderOpts, params)))
      ];
    }
  },
  VxeSelect: {
    tableAutoFocus: "input",
    renderTableEdit: defaultSelectEditRender,
    renderTableDefault: defaultSelectEditRender,
    renderTableCell(renderOpts, params) {
      return getCellLabelVNs(renderOpts, params, getSelectCellValue(renderOpts, params));
    },
    renderTableFilter(renderOpts, params) {
      const { column } = params;
      const { options, optionProps, optionGroups, optionGroupProps } = renderOpts;
      return column.filters.map((option, oIndex) => {
        const optionValue = option.data;
        return h(getDefaultComponent(renderOpts), Object.assign(Object.assign({ key: oIndex }, getCellEditFilterProps(renderOpts, params, optionValue, { options, optionProps, optionGroups, optionGroupProps })), getFilterOns(renderOpts, params, option)));
      });
    },
    tableFilterDefaultMethod: handleFilterMethod,
    tableExportMethod: handleExportSelectMethod
  },
  /**
   * 已废弃，被 FormatSelect 替换
   * @deprecated
   */
  formatOption: {
    renderTableDefault(renderOpts, params) {
      return getCellLabelVNs(renderOpts, params, getSelectCellValue(renderOpts, params));
    }
  },
  FormatSelect: {
    renderTableDefault(renderOpts, params) {
      return getCellLabelVNs(renderOpts, params, getSelectCellValue(renderOpts, params));
    },
    tableFilterDefaultMethod: handleFilterMethod,
    tableExportMethod: handleExportSelectMethod
  },
  VxeTreeSelect: {
    tableAutoFocus: "input",
    renderTableEdit: defaultTableOrTreeSelectEditRender,
    renderTableCell(renderOpts, params) {
      return getCellLabelVNs(renderOpts, params, getTreeSelectCellValue(renderOpts, params));
    },
    tableExportMethod: handleExportTreeSelectMethod
  },
  /**
   * 已废弃，被 FormatTreeSelect 替换
   * @deprecated
   */
  formatTree: {
    renderTableDefault(renderOpts, params) {
      return getCellLabelVNs(renderOpts, params, getTreeSelectCellValue(renderOpts, params));
    }
  },
  FormatTreeSelect: {
    renderTableDefault(renderOpts, params) {
      return getCellLabelVNs(renderOpts, params, getTreeSelectCellValue(renderOpts, params));
    },
    tableExportMethod: handleExportTreeSelectMethod
  },
  VxeTableSelect: {
    tableAutoFocus: "input",
    renderTableEdit: defaultTableOrTreeSelectEditRender,
    renderTableCell(renderOpts, params) {
      return getCellLabelVNs(renderOpts, params, getTreeSelectCellValue(renderOpts, params));
    },
    tableExportMethod: handleExportTreeSelectMethod
  },
  VxeColorPicker: {
    tableAutoFocus: "input",
    renderTableEdit(renderOpts, params) {
      const { row, column } = params;
      const { options } = renderOpts;
      const cellValue = getCellValue(row, column);
      return [
        h(getDefaultComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue, { colors: options })), getEditOns(renderOpts, params)))
      ];
    },
    renderTableCell(renderOpts, params) {
      const { row, column } = params;
      const cellValue = import_xe_utils9.default.get(row, column.field);
      return h("span", {
        class: "vxe-color-picker--readonly"
      }, [
        h("div", {
          class: "vxe-color-picker--readonly-color",
          style: {
            backgroundColor: cellValue
          }
        })
      ]);
    }
  },
  VxeIconPicker: {
    tableAutoFocus: "input",
    renderTableEdit(renderOpts, params) {
      const { row, column } = params;
      const { options } = renderOpts;
      const cellValue = getCellValue(row, column);
      return [
        h(getDefaultComponent(renderOpts), Object.assign(Object.assign({}, getCellEditProps(renderOpts, params, cellValue, { icons: options })), getEditOns(renderOpts, params)))
      ];
    },
    renderTableCell(renderOpts, params) {
      const { row, column } = params;
      const cellValue = import_xe_utils9.default.get(row, column.field);
      return h("i", {
        class: cellValue
      });
    }
  },
  VxeRadioGroup: {
    renderTableDefault: radioAndCheckboxGroupEditRender
  },
  VxeCheckbox: {
    renderTableDefault: checkboxEditRender
  },
  VxeCheckboxGroup: {
    renderTableDefault: radioAndCheckboxGroupEditRender
  },
  VxeSwitch: {
    tableAutoFocus: "button",
    renderTableEdit: defaultEditRender,
    renderTableDefault: defaultEditRender
  },
  VxeUpload: {
    renderTableEdit: defaultEditRender,
    renderTableCell: defaultEditRender,
    renderTableDefault: defaultEditRender
  },
  VxeImage: {
    renderTableDefault(renderOpts, params) {
      const { row, column } = params;
      const { props } = renderOpts;
      const cellValue = getCellValue(row, column);
      return [
        h(getDefaultComponent(renderOpts), Object.assign(Object.assign(Object.assign({}, props), { src: cellValue }), getEditOns(renderOpts, params)))
      ];
    }
  },
  VxeImageGroup: {
    renderTableDefault(renderOpts, params) {
      const { row, column } = params;
      const { props } = renderOpts;
      const cellValue = getCellValue(row, column);
      return [
        h(getDefaultComponent(renderOpts), Object.assign(Object.assign(Object.assign({}, props), { urlList: cellValue }), getEditOns(renderOpts, params)))
      ];
    }
  },
  VxeTextEllipsis: {
    renderTableDefault(renderOpts, params) {
      const { row, column } = params;
      const { props } = renderOpts;
      const cellValue = getCellValue(row, column);
      return [
        h(getDefaultComponent(renderOpts), Object.assign(Object.assign(Object.assign({}, props), { content: cellValue }), getEditOns(renderOpts, params)))
      ];
    }
  },
  VxeRate: {
    renderTableDefault: defaultEditRender
  },
  VxeSlider: {
    renderTableDefault: defaultEditRender
  },
  // 以下已废弃
  $input: {
    tableAutoFocus: ".vxe-input--inner",
    renderTableEdit: oldEditRender,
    renderTableCell(renderOpts, params) {
      var _a;
      const { props = {} } = renderOpts;
      const { row, column } = params;
      const digits = props.digits || ((_a = getConfig3().input) === null || _a === void 0 ? void 0 : _a.digits) || 2;
      let cellValue = import_xe_utils9.default.get(row, column.field);
      if (cellValue) {
        switch (props.type) {
          case "date":
          case "week":
          case "month":
          case "year":
            cellValue = getLabelFormatDate(cellValue, props);
            break;
          case "float":
            cellValue = import_xe_utils9.default.toFixed(import_xe_utils9.default.floor(cellValue, digits), digits);
            break;
        }
      }
      return getCellLabelVNs(renderOpts, params, cellValue);
    },
    renderTableDefault: oldEditRender,
    renderTableFilter: oldFilterRender,
    tableFilterDefaultMethod: handleInputFilterMethod
  },
  $textarea: {
    tableAutoFocus: ".vxe-textarea--inner"
  },
  $button: {
    renderTableDefault: oldButtonEditRender
  },
  $buttons: {
    renderTableDefault: oldButtonsEditRender
  },
  $select: {
    tableAutoFocus: ".vxe-input--inner",
    renderTableEdit: oldSelectEditRender,
    renderTableDefault: oldSelectEditRender,
    renderTableCell(renderOpts, params) {
      return getCellLabelVNs(renderOpts, params, getSelectCellValue(renderOpts, params));
    },
    renderTableFilter(renderOpts, params) {
      const { column } = params;
      const { options, optionProps, optionGroups, optionGroupProps } = renderOpts;
      return column.filters.map((option, oIndex) => {
        const optionValue = option.data;
        return h(getOldComponent(renderOpts), Object.assign(Object.assign({ key: oIndex }, getCellEditFilterProps(renderOpts, params, optionValue, { options, optionProps, optionGroups, optionGroupProps })), getFilterOns(renderOpts, params, option)));
      });
    },
    tableFilterDefaultMethod: handleFilterMethod,
    tableExportMethod: handleExportSelectMethod
  },
  $radio: {
    tableAutoFocus: ".vxe-radio--input"
  },
  $checkbox: {
    tableAutoFocus: ".vxe-checkbox--input"
  },
  $switch: {
    tableAutoFocus: ".vxe-switch--button",
    renderTableEdit: oldEditRender,
    renderTableDefault: oldEditRender
  }
  // 以上已废弃
});

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/index.js
var VxeTable = Object.assign({}, table_default, {
  install(app) {
    app.component(table_default.name, table_default);
  }
});
var tableHandle = {
  useCellView
};
if (VxeUI.dynamicApp) {
  VxeUI.dynamicApp.component(table_default.name, table_default);
}
VxeUI.component(table_default);
VxeUI.tableHandle = tableHandle;
var Table = VxeTable;
var table_default2 = VxeTable;

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/vxe-table/index.js
var vxe_table_default = table_default2;
export {
  Table,
  VxeTable,
  vxe_table_default as default
};
//# sourceMappingURL=vxe-table_es_vxe-table_index__js.js.map
