<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bdyl.line.web.mapper.CameraModelConfigMapper">


    <select id="selectModelCodesByCameraId" resultType="java.lang.String" parameterType="java.lang.Long">
        select model_code
        from t_camera_model_config
        where camera_id = #{cameraId}
    </select>

    <delete id="deleteByCameraIdAndModelCode">
        delete
        from t_camera_model_config
        where camera_id = #{cameraId}
          and model_code = #{modelCode}
    </delete>
</mapper>