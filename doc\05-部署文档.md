# 线路巡检管理系统 - 部署文档

## 1. 部署环境要求

### 1.1 硬件要求

#### 1.1.1 最小配置
- **CPU**：4核心
- **内存**：8GB
- **存储**：100GB SSD
- **网络**：100Mbps

#### 1.1.2 推荐配置
- **CPU**：8核心
- **内存**：16GB
- **存储**：500GB SSD
- **网络**：1Gbps

### 1.2 软件环境

#### 1.2.1 操作系统
- **Linux**：CentOS 7+、Ubuntu 18.04+、RHEL 7+
- **Windows**：Windows Server 2016+（不推荐生产环境）

#### 1.2.2 基础软件
- **JDK**：OpenJDK 21 或 Oracle JDK 21
- **MySQL**：8.0+
- **Nginx**：1.18+
- **Redis**：6.0+（可选）

## 2. 生产环境部署

### 2.1 数据库部署

#### 2.1.1 MySQL安装配置
```bash
# CentOS/RHEL
sudo yum install mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld

# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
sudo systemctl enable mysql
```

#### 2.1.2 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE line CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'line_user'@'%' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON line.* TO 'line_user'@'%';
FLUSH PRIVILEGES;

-- 配置参数
SET GLOBAL max_connections = 1000;
SET GLOBAL innodb_buffer_pool_size = 2147483648; -- 2GB
```

#### 2.1.3 MySQL配置优化
```ini
# /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
# 基础配置
port = 3306
bind-address = 0.0.0.0
max_connections = 1000
max_connect_errors = 10000

# InnoDB配置
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 日志配置
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

### 2.2 应用服务部署

#### 2.2.1 JDK安装
```bash
# 下载并安装OpenJDK 21
wget https://download.java.net/java/GA/jdk21/fd2272bbf8e04c3dbaee13770090416c/35/GPL/openjdk-21_linux-x64_bin.tar.gz
tar -xzf openjdk-21_linux-x64_bin.tar.gz
sudo mv jdk-21 /opt/
sudo ln -s /opt/jdk-21/bin/java /usr/bin/java

# 配置环境变量
echo 'export JAVA_HOME=/opt/jdk-21' >> ~/.bashrc
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

#### 2.2.2 应用打包
```bash
# 克隆代码
git clone <repository-url>
cd line-inspection

# 后端打包
cd line-web
mvn clean package -Pprod -DskipTests

# 前端打包
cd ../line-ui
pnpm install
pnpm build:antd
```

#### 2.2.3 应用部署
```bash
# 创建应用目录
sudo mkdir -p /opt/line-inspection
sudo mkdir -p /opt/line-inspection/logs
sudo mkdir -p /opt/line-inspection/upload

# 复制应用文件
sudo cp line-web/target/line-web-*.jar /opt/line-inspection/line-web.jar
sudo cp -r line-ui/apps/web-antd/dist/* /opt/line-inspection/static/

# 创建配置文件
sudo tee /opt/line-inspection/application-prod.yml > /dev/null <<EOF
server:
  port: 8080

spring:
  datasource:
    url: *****************************************************************************************************
    username: line_user
    password: your_secure_password
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5

logging:
  level:
    root: INFO
    com.bdyl: INFO
  file:
    name: /opt/line-inspection/logs/application.log

line:
  upload-dir: /opt/line-inspection/upload
  relative-upload-dir: /upload
  uaa-url: http://your-uaa-server:8180/api/uaa
  platform-url: http://your-platform-server:8180/api/iot
  gb-url: http://your-gb-server:8280/api/gb28181/v1
EOF
```

#### 2.2.4 创建系统服务
```bash
# 创建systemd服务文件
sudo tee /etc/systemd/system/line-inspection.service > /dev/null <<EOF
[Unit]
Description=Line Inspection Management System
After=network.target mysql.service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/line-inspection
ExecStart=/usr/bin/java -jar -Xms2g -Xmx4g -Dspring.profiles.active=prod line-web.jar
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable line-inspection
sudo systemctl start line-inspection
```

### 2.3 Nginx配置

#### 2.3.1 安装Nginx
```bash
# CentOS/RHEL
sudo yum install nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Ubuntu/Debian
sudo apt install nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

#### 2.3.2 配置Nginx
```nginx
# /etc/nginx/conf.d/line-inspection.conf
upstream line_backend {
    server 127.0.0.1:8080;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 静态文件
    location / {
        root /opt/line-inspection/static;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://line_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 文件上传
    location /upload/ {
        alias /opt/line-inspection/upload/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # 日志配置
    access_log /var/log/nginx/line-inspection.access.log;
    error_log /var/log/nginx/line-inspection.error.log;
}
```

## 3. Docker部署

### 3.1 Dockerfile
```dockerfile
# 多阶段构建
FROM maven:3.9-openjdk-21 AS backend-build
WORKDIR /app
COPY pom.xml .
COPY line-common ./line-common
COPY line-web ./line-web
RUN mvn clean package -DskipTests

FROM node:18-alpine AS frontend-build
WORKDIR /app
COPY line-ui/package.json line-ui/pnpm-lock.yaml ./
RUN npm install -g pnpm && pnpm install
COPY line-ui ./
RUN pnpm build:antd

FROM openjdk:21-jre-slim
WORKDIR /app

# 安装必要的工具
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# 复制应用文件
COPY --from=backend-build /app/line-web/target/line-web-*.jar app.jar
COPY --from=frontend-build /app/apps/web-antd/dist ./static

# 创建目录
RUN mkdir -p /app/logs /app/upload

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/api/line/actuator/health || exit 1

# 启动命令
ENTRYPOINT ["java", "-jar", "-Xms1g", "-Xmx2g", "app.jar"]
```

### 3.2 Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: line-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: line
      MYSQL_USER: line_user
      MYSQL_PASSWORD: line_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    container_name: line-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  app:
    build: .
    container_name: line-app
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: *************************************************************************************************
      SPRING_DATASOURCE_USERNAME: line_user
      SPRING_DATASOURCE_PASSWORD: line_password
    volumes:
      - app_logs:/app/logs
      - app_upload:/app/upload
    depends_on:
      - mysql
      - redis
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    container_name: line-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  app_logs:
  app_upload:
```

### 3.3 Docker部署命令
```bash
# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f app

# 停止服务
docker-compose down

# 更新应用
docker-compose pull
docker-compose up -d --force-recreate app
```

## 4. 监控和维护

### 4.1 应用监控
```bash
# 查看应用状态
sudo systemctl status line-inspection

# 查看应用日志
sudo tail -f /opt/line-inspection/logs/application.log

# 查看JVM状态
jps -l
jstat -gc <pid>
```

### 4.2 数据库监控
```sql
-- 查看连接数
SHOW STATUS LIKE 'Threads_connected';

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 查看表大小
SELECT 
    table_schema,
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'line'
ORDER BY (data_length + index_length) DESC;
```

### 4.3 备份策略
```bash
# 数据库备份脚本
#!/bin/bash
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="line_backup_$DATE.sql"

mkdir -p $BACKUP_DIR
mysqldump -u line_user -p'line_password' line > $BACKUP_DIR/$BACKUP_FILE
gzip $BACKUP_DIR/$BACKUP_FILE

# 删除7天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete
```

## 5. 故障排查

### 5.1 常见问题
- **应用启动失败**：检查JDK版本、数据库连接、端口占用
- **数据库连接失败**：检查数据库服务状态、网络连接、用户权限
- **内存不足**：调整JVM参数、增加服务器内存
- **磁盘空间不足**：清理日志文件、扩展磁盘空间

### 5.2 性能优化
- **数据库优化**：添加索引、优化查询、调整配置参数
- **应用优化**：调整JVM参数、启用缓存、优化代码
- **网络优化**：使用CDN、启用Gzip压缩、优化静态资源
