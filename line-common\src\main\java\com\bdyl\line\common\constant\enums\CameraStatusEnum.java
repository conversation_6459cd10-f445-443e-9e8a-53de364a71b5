package com.bdyl.line.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 终端状态枚举
 *
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@AllArgsConstructor
public enum CameraStatusEnum {

    /**
     * 在线
     */
    ONLINE("ONLINE", "在线", "在线"),
    /**
     * 离线
     */
    OFFLINE("OFFLINE", "离线", "离线"),
    /**
     * 报警状态
     */
    ALARM("ALARM", "报警", "报警");

    /**
     * value
     */
    private final String value;
    /**
     * 名称
     */
    private final String name;
    /**
     * 描述
     */
    private final String desc;

}
