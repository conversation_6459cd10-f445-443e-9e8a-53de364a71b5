package com.bdyl.line.web.model.request;

import java.math.BigDecimal;
import java.util.List;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import lombok.Data;

import com.bdyl.line.web.model.dto.PointDTO;

/**
 * 摄像头模型配置请求
 */
@Data
public class CameraModelConfigRequest {
    /**
     * 摄像头id
     */
    @NotNull(message = "摄像头ID不能为空")
    private Long cameraId;
    /**
     * 模型编码
     */
    @NotBlank(message = "模型编码不能为空")
    private String modelCode;
    /**
     * 置信度
     */
    private BigDecimal confidence;
    /**
     * 区域点
     */
    private List<PointDTO> regionPoints;
}
