package com.bdyl.line.web.model.response.home;

import lombok.Data;

/**
 * 首页设备统计响应对象，包含设备总数、在线数、离线数、在线率。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class HomeDeviceStatsResponse {
    /**
     * 设备总数
     */
    private Integer total;
    /**
     * 在线数
     */
    private Integer online;
    /**
     * 离线数
     */
    private Integer offline;
    /**
     * 在线率（百分比，0-100）
     */
    private Double onlineRate;

}
