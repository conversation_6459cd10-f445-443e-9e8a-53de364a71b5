package com.bdyl.line.web.model.response.user;

import java.util.Set;

import lombok.Data;

/**
 * 当前用户信息
 */
@Data
public class CurrentUserVO {

    /**
     * 用户ID
     */
    private String userId;
    /**
     * 账户ID
     */
    private String accountId;

    /**
     * 组织ID
     */
    private Long organId;

    /**
     * 组织编码
     */
    private String organCode;

    /**
     * 组织类型 {@link com.bdyl.line.common.constant.enums.RegionLevelEnum}
     */
    private String organType;
    /**
     * 用户名
     */
    private String username;
    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 角色列表
     */
    private Set<String> roles;
    /**
     * 权限列表
     */
    private Set<String> permissions;
    /**
     * 访问令牌
     */
    private String accessToken;
    /**
     * 是否认证
     */
    private boolean authenticated;

}
