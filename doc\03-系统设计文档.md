# 线路巡检管理系统 - 系统设计文档

## 1. 系统架构设计

### 1.1 整体架构

```mermaid
graph TB
    subgraph "前端层"
        A[Web管理端<br/>Vue3 + Vben Admin]
        B[移动端<br/>H5/小程序]
    end
    
    subgraph "网关层"
        C[API网关<br/>Nginx/Spring Gateway]
    end
    
    subgraph "应用层"
        D[巡检管理服务<br/>Spring Boot]
        E[认证服务<br/>UAA]
        F[文件服务<br/>File Service]
    end
    
    subgraph "数据层"
        G[MySQL数据库]
        H[Redis缓存]
        I[文件存储]
    end
    
    subgraph "外部系统"
        J[GB28181平台]
        K[物联网平台]
        L[第三方系统]
    end
    
    A --> C
    B --> C
    C --> D
    C --> E
    C --> F
    D --> G
    D --> H
    F --> I
    D --> J
    D --> K
    D --> L
```

### 1.2 技术架构

#### 1.2.1 后端架构
- **框架**：Spring Boot 3.x + Spring Security
- **数据访问**：MyBatis-Plus + Liquibase
- **缓存**：Redis
- **消息队列**：RabbitMQ（可选）
- **监控**：Spring Boot Actuator + Prometheus

#### 1.2.2 前端架构
- **框架**：Vue 3 + TypeScript
- **UI组件**：Ant Design Vue
- **状态管理**：Pinia
- **路由**：Vue Router 4
- **构建工具**：Vite

#### 1.2.3 数据库架构
- **主数据库**：MySQL 8.0
- **缓存数据库**：Redis 6.x
- **数据库连接池**：HikariCP

## 2. 模块设计

### 2.1 摄像头管理模块

#### 2.1.1 核心实体
```java
// 摄像头实体
CameraEntity {
    Long id;                    // 主键
    String code;                // 摄像头编码
    String name;                // 摄像头名称
    String location;            // 位置信息
    String ip;                  // IP地址
    Integer port;               // 端口
    String status;              // 状态
    Double longitude;           // 经度
    Double latitude;            // 纬度
    Long terminalId;            // 终端ID
    Long organId;               // 组织ID
}
```

#### 2.1.2 主要接口
- `GET /api/line/camera/page` - 分页查询摄像头
- `POST /api/line/camera` - 创建摄像头
- `PUT /api/line/camera/{id}` - 更新摄像头
- `DELETE /api/line/camera/{id}` - 删除摄像头
- `POST /api/line/camera/play` - 播放视频
- `POST /api/line/camera/playback` - 历史回放

### 2.2 巡检计划模块

#### 2.2.1 核心实体
```java
// 巡检计划实体
InspectionPlanEntity {
    Long id;                    // 主键
    String name;                // 计划名称
    String cycleType;           // 周期类型(HOUR/DAY/WEEK/MONTH)
    Integer cycleValue;         // 周期值
    List<Long> cameraIds;       // 摄像头ID列表
    LocalDate startDate;        // 开始日期
    Long responsibleUserId;     // 负责人ID
    String status;              // 状态
    Integer dayValue;           // 日期值(周几/几号)
    List<TimeSlotDTO> timeSlots; // 时间段配置
    LocalTime startTime;        // 开始时间
    LocalTime endTime;          // 结束时间
}
```

#### 2.2.2 主要接口
- `GET /api/line/inspection/plan/page` - 分页查询计划
- `POST /api/line/inspection/plan` - 创建计划
- `PUT /api/line/inspection/plan/{id}` - 更新计划
- `DELETE /api/line/inspection/plan/{id}` - 删除计划
- `PUT /api/line/inspection/plan/{id}/status` - 启用/禁用计划

### 2.3 巡检任务模块

#### 2.3.1 核心实体
```java
// 巡检任务实体
InspectionTaskEntity {
    Long id;                    // 主键
    Long planId;                // 计划ID
    String taskName;            // 任务名称
    String cycleType;           // 周期类型
    Integer cycleValue;         // 周期值
    LocalDateTime scheduledStartTime; // 计划开始时间
    LocalDateTime scheduledEndTime;   // 计划结束时间
    LocalDateTime actualStartTime;    // 实际开始时间
    LocalDateTime actualEndTime;      // 实际结束时间
    String status;              // 状态
    Long responsibleUserId;     // 负责人ID
    Integer cameraCount;        // 摄像头总数
    Integer completedCount;     // 已完成数量
}

// 任务摄像头关联实体
InspectionTaskCameraEntity {
    Long id;                    // 主键
    Long taskId;                // 任务ID
    Long cameraId;              // 摄像头ID
    String cameraName;          // 摄像头名称
    String status;              // 状态
    Integer sortOrder;          // 排序
}
```

#### 2.3.2 主要接口
- `GET /api/line/inspection/task/page` - 分页查询任务
- `GET /api/line/inspection/task/{id}` - 查询任务详情
- `GET /api/line/inspection/task/{id}/cameras` - 查询任务摄像头
- `PUT /api/line/inspection/task/{id}/start` - 开始任务
- `PUT /api/line/inspection/task/{id}/complete` - 完成任务

### 2.4 巡检记录模块

#### 2.4.1 核心实体
```java
// 巡检记录实体
InspectionRecordEntity {
    Long id;                    // 主键
    Long taskId;                // 任务ID
    Long cameraId;              // 摄像头ID
    String result;              // 巡检结果
    String description;         // 描述
    String imageUrls;           // 图片URL列表
    LocalDateTime inspectionTime; // 巡检时间
    Long inspectorId;           // 巡检人员ID
    String inspectorName;       // 巡检人员姓名
    Double longitude;           // 经度
    Double latitude;            // 纬度
}
```

#### 2.4.2 主要接口
- `POST /api/line/inspection/record` - 提交巡检记录
- `GET /api/line/inspection/record/page` - 分页查询记录
- `GET /api/line/inspection/record/task/{taskId}` - 查询任务记录
- `GET /api/line/inspection/record/camera/{cameraId}` - 查询摄像头记录

## 3. 数据库设计

### 3.1 表结构设计

#### 3.1.1 摄像头表 (t_camera)
```sql
CREATE TABLE t_camera (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    organ_id BIGINT NOT NULL,
    terminal_id BIGINT,
    name VARCHAR(128) NOT NULL,
    code VARCHAR(64) NOT NULL UNIQUE,
    channel VARCHAR(32),
    location VARCHAR(255),
    status VARCHAR(32) DEFAULT 'ONLINE',
    remarks TEXT,
    ip VARCHAR(64),
    port INT,
    username VARCHAR(64),
    password VARCHAR(128),
    stream_urls JSON,
    longitude DECIMAL(10,7),
    latitude DECIMAL(10,7),
    creator_id BIGINT,
    updater_id BIGINT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_organ_id (organ_id),
    INDEX idx_terminal_id (terminal_id),
    INDEX idx_code (code)
);
```

#### 3.1.2 巡检计划表 (t_inspection_plan)
```sql
CREATE TABLE t_inspection_plan (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    organ_id BIGINT NOT NULL,
    name VARCHAR(128) NOT NULL,
    cycle_type VARCHAR(32) NOT NULL,
    cycle_value INT NOT NULL,
    camera_ids JSON,
    start_date DATE NOT NULL,
    responsible_user_id BIGINT NOT NULL,
    responsible_user_name VARCHAR(64),
    status VARCHAR(32) DEFAULT 'ENABLE',
    description TEXT,
    day_value INT,
    time_slots JSON,
    start_time TIME,
    end_time TIME,
    creator_id BIGINT,
    updater_id BIGINT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_organ_id (organ_id),
    INDEX idx_status (status),
    INDEX idx_responsible_user_id (responsible_user_id)
);
```

#### 3.1.3 巡检任务表 (t_inspection_task)
```sql
CREATE TABLE t_inspection_task (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    organ_id BIGINT NOT NULL,
    plan_id BIGINT NOT NULL,
    task_name VARCHAR(128) NOT NULL,
    cycle_type VARCHAR(32) NOT NULL,
    cycle_value INT NOT NULL,
    scheduled_start_time DATETIME NOT NULL,
    scheduled_end_time DATETIME NOT NULL,
    actual_start_time DATETIME,
    actual_end_time DATETIME,
    status VARCHAR(32) DEFAULT 'PENDING',
    responsible_user_id BIGINT NOT NULL,
    responsible_user_name VARCHAR(64),
    camera_count INT DEFAULT 0,
    completed_count INT DEFAULT 0,
    creator_id BIGINT,
    updater_id BIGINT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_plan_id (plan_id),
    INDEX idx_status (status),
    INDEX idx_responsible_user_id (responsible_user_id),
    INDEX idx_scheduled_time (scheduled_start_time, scheduled_end_time)
);
```

### 3.2 索引设计

#### 3.2.1 主要索引
- **主键索引**：所有表的id字段
- **组织索引**：organ_id字段，支持数据权限查询
- **状态索引**：status字段，支持状态筛选
- **时间索引**：create_time、update_time字段，支持时间范围查询

#### 3.2.2 复合索引
- **任务时间索引**：(scheduled_start_time, scheduled_end_time)
- **计划状态索引**：(status, organ_id)
- **摄像头组织索引**：(organ_id, status)

## 4. 接口设计

### 4.1 RESTful API设计规范

#### 4.1.1 URL设计
- 基础路径：`/api/line`
- 资源命名：使用名词复数形式
- 版本控制：通过Header或URL参数

#### 4.1.2 HTTP方法
- `GET`：查询操作
- `POST`：创建操作
- `PUT`：更新操作
- `DELETE`：删除操作

#### 4.1.3 响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": "2025-01-01T00:00:00Z"
}
```

### 4.2 主要API接口

#### 4.2.1 摄像头管理接口
```yaml
# 分页查询摄像头
GET /api/line/camera/page
Parameters:
  - page: 页码
  - size: 页大小
  - name: 摄像头名称
  - status: 状态
Response: Page<CameraResponse>

# 播放视频
POST /api/line/camera/play
Body: GBPlayRequest
Response: CameraBoxAndStreamResponse
```

#### 4.2.2 巡检计划接口
```yaml
# 创建巡检计划
POST /api/line/inspection/plan
Body: InspectionPlanRequest
Response: Boolean

# 分页查询巡检计划
GET /api/line/inspection/plan/page
Parameters:
  - page: 页码
  - size: 页大小
  - name: 计划名称
  - cycleType: 周期类型
Response: Page<InspectionPlanResponse>
```

## 5. 安全设计

### 5.1 认证授权
- **JWT Token**：基于JWT的无状态认证
- **角色权限**：基于RBAC的权限控制
- **数据权限**：基于组织的数据隔离

### 5.2 数据安全
- **传输加密**：HTTPS协议
- **存储加密**：敏感数据加密存储
- **SQL注入防护**：参数化查询
- **XSS防护**：输入输出过滤

### 5.3 接口安全
- **请求限流**：防止恶意请求
- **参数校验**：严格的参数验证
- **日志审计**：完整的操作日志
- **异常处理**：统一的异常处理机制

## 6. 性能设计

### 6.1 缓存策略
- **Redis缓存**：热点数据缓存
- **本地缓存**：配置数据缓存
- **CDN加速**：静态资源加速

### 6.2 数据库优化
- **索引优化**：合理的索引设计
- **分页查询**：避免大数据量查询
- **连接池**：数据库连接池优化

### 6.3 系统监控
- **应用监控**：Spring Boot Actuator
- **性能监控**：Prometheus + Grafana
- **日志监控**：ELK Stack
