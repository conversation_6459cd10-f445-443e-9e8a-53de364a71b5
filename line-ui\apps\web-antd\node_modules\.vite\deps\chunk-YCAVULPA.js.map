{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/useRefs.js"], "sourcesContent": ["import { onBeforeUpdate, ref } from 'vue';\nconst useRefs = () => {\n  const refs = ref(new Map());\n  const setRef = key => el => {\n    refs.value.set(key, el);\n  };\n  onBeforeUpdate(() => {\n    refs.value = new Map();\n  });\n  return [setRef, refs];\n};\nexport default useRefs;"], "mappings": ";;;;;;AACA,IAAM,UAAU,MAAM;AACpB,QAAM,OAAO,IAAI,oBAAI,IAAI,CAAC;AAC1B,QAAM,SAAS,SAAO,QAAM;AAC1B,SAAK,MAAM,IAAI,KAAK,EAAE;AAAA,EACxB;AACA,iBAAe,MAAM;AACnB,SAAK,QAAQ,oBAAI,IAAI;AAAA,EACvB,CAAC;AACD,SAAO,CAAC,QAAQ,IAAI;AACtB;AACA,IAAO,kBAAQ;", "names": []}