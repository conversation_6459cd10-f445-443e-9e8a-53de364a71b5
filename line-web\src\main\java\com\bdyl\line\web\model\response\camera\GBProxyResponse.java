package com.bdyl.line.web.model.response.camera;

import lombok.Data;

/**
 * 拉流代理响应DTO
 */
@Data
public class GBProxyResponse {

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 流应用名
     */
    private String app;

    /**
     * 流ID
     */
    private String stream;

    /**
     * rtsp流地址
     */
    private String url;

    /**
     * 拉流方式 0：tcp，1：udp，2：组播
     */
    private String rtpType;

    /**
     * 是否开启MP4录制
     */
    private Boolean enableMp4;

    /**
     * 是否开启RTSP
     */
    private Boolean enableRtsp;

    /**
     * 是否开启RTMP
     */
    private Boolean enableRtmp;

    /**
     * 是否开启HLS
     */
    private Boolean enableHls;

    /**
     * 拉流超时时间（秒）
     */
    private Integer timeoutSec;

    /**
     * 代理状态
     */
    private String status;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
