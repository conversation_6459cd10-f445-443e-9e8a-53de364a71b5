package com.bdyl.line.web.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bdyl.line.web.entity.InspectionTaskCameraEntity;

/**
 * 巡检任务摄像头关联Mapper接口，提供巡检任务摄像头关联相关的数据库操作。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Mapper
public interface InspectionTaskCameraMapper extends BaseMapper<InspectionTaskCameraEntity> {

    /**
     * 根据任务ID查询摄像头列表
     *
     * @param taskId 任务ID
     * @return 摄像头列表
     */
    List<InspectionTaskCameraEntity> selectByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据任务ID统计摄像头数量
     *
     * @param taskId 任务ID
     * @return 摄像头数量
     */
    Integer countByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据任务ID统计已完成的摄像头数量
     *
     * @param taskId 任务ID
     * @return 已完成数量
     */
    Integer countCompletedByTaskId(@Param("taskId") Long taskId);

}
