package com.bdyl.line.web.model.response.inspection;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.bdyl.line.web.entity.handler.TimeSlotTypeHandler;
import com.bdyl.line.web.model.dto.TimeSlotDTO;
import com.bdyl.line.web.model.response.camera.CameraResponse;

/**
 * 巡检计划响应对象
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class InspectionPlanResponse {
    /**
     * 计划ID
     */
    private Long id;

    /**
     * 计划名称
     */
    private String name;

    /**
     * 巡检周期类型
     */
    private String cycleType;

    /**
     * 周期值
     */
    private Integer cycleValue;

    /**
     * 摄像头ID列表
     */
    private List<Long> cameraIds;

    /**
     * 摄像头列表
     */
    private List<CameraResponse> cameras;

    /**
     * 摄像头数量
     */
    private Integer cameraCount;

    /**
     * 启动日期
     */
    private LocalDate startDate;

    /**
     * 巡检负责人ID
     */
    private Long responsibleUserId;

    /**
     * 巡检负责人姓名
     */
    private String responsibleUserName;

    /**
     * 启用状态 {@link com.bdyl.line.common.constant.enums.StatusEnum}
     */
    private String status;

    /**
     * 计划描述
     */
    private String description;

    /**
     * 日期值(周类型:1-7表示星期几，月类型:1-31表示每月几号)
     */
    private Integer dayValue;

    /**
     * 时间段配置(小时级使用，自动转换JSON)
     */
    @TableField(typeHandler = TimeSlotTypeHandler.class)
    private List<TimeSlotDTO> timeSlots;

    /**
     * 开始时间(日/周/月类型使用)
     */
    private LocalTime startTime;

    /**
     * 结束时间(日/周/月类型使用)
     */
    private LocalTime endTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
