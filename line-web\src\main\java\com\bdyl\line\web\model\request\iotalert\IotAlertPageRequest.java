package com.bdyl.line.web.model.request.iotalert;

import java.time.LocalDateTime;

import lombok.Data;

import com.bdyl.boot.data.query.PageRequest;

/**
 * 物联设备报警分页查询请求
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class IotAlertPageRequest extends PageRequest {
    /**
     * 报警类型 {@link com.bdyl.line.common.constant.enums.IotAlertTypeEnum}
     */
    private String type;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 状态 {@link com.bdyl.line.common.constant.enums.IotAlertTypeEnum}
     */
    private String status;

    /**
     * 报警开始时间
     */
    private LocalDateTime createTimeStart;

    /**
     * 报警结束时间
     */
    private LocalDateTime createTimeEnd;
}
