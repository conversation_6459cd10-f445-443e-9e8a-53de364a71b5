<script lang="ts" setup>
import { computed, nextTick, reactive, ref } from 'vue';

import { message } from 'ant-design-vue';

import { addRole_Api, updateRole_Api } from '#/api/core/role';

import { useDictStore } from '#/store';

const dictStore = useDictStore();

// 状态选项
const statusOptions = [
  { label: '启用', value: 'ENABLED' },
  { label: '禁用', value: 'DISABLED' },
];

const emit = defineEmits(['success']);

const open = ref<boolean>(false);
const modalTitle = ref<string>('');
const formRef = ref();
const loading = ref(false);
const labelCol = { span: 6 };
const wrapperCol = { span: 16 };

// 表单数据
const formData = reactive<any>({
  id: undefined,
  name: undefined,
  code: undefined,
  description: undefined,
  status: 'ENABLED',
});

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入角色标识', trigger: 'blur' }],
  description: [{ required: false, message: '请输入描述', trigger: 'blur' }],
};

// 是否为编辑模式
const isEdit = computed(() => !!formData.id);
// 重置表单
const resetForm = async () => {
  await formRef.value?.resetFields();
  Object.assign(formData, {
    id: undefined,
    name: undefined,
    code: undefined,
    description: undefined,
    status: 'ENABLED',
  });
};

// 打开弹窗
const openModal = async (type: string, record?: any) => {
  open.value = true;
  modalTitle.value = type === 'create' ? '新增角色' : '编辑角色';
  await resetForm();

  if (type === 'update' && record) {
    nextTick(() => {
      // 编辑模式，只填充formData中已定义的字段
      Object.keys(formData).forEach((key: any) => {
        if (record[key] !== undefined) {
          formData[key] = record[key];
        }
      });
    });
  }
};
// 关闭弹窗
const closeModal = () => {
  open.value = false;
  resetForm();
};

// 保存角色
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;

    if (isEdit.value) {
      const temp = { ...formData };
      delete temp.id;
      // 更新角色
      await updateRole_Api(formData.id, temp);
      message.success('更新成功');
    } else {
      // 新增角色
      await addRole_Api(formData);
      message.success('添加成功');
    }

    closeModal();
    emit('success');
  } catch (error) {
    console.error('保存角色失败', error);
  } finally {
    loading.value = false;
  }
};

// 暴露组件方法
defineExpose({
  openModal,
});
</script>
<template>
  <a-modal
    v-model:open="open"
    :title="modalTitle"
    :confirm-loading="loading"
    :mask-closable="false"
    width="600px"
    @cancel="closeModal"
  >
    <a-form
      ref="formRef"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :model="formData"
      :rules="formRules"
    >
      <a-form-item label="角色名称" name="name">
        <a-input v-model:value="formData.name" placeholder="请输入角色名称" />
      </a-form-item>

      <a-form-item label="角色标识" name="code">
        <a-input
          v-model:value="formData.code"
          placeholder="请输入角色标识"
          :disabled="isEdit"
        />
      </a-form-item>

      <!-- <a-form-item label="状态" name="status">
        <a-radio-group v-model:value="formData.status">
          <a-radio
            v-for="option in statusOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </a-radio>
        </a-radio-group>
      </a-form-item> -->

      <a-form-item label="描述" name="description">
        <a-textarea
          v-model:value="formData.description"
          placeholder="请输入描述"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>
