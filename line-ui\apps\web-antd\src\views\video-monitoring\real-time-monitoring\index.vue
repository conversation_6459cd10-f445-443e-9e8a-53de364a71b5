<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { useEventBus } from '@vueuse/core';

import DeviceTree from '../components/DeviceTree.vue';
import VideoWall from '../components/VideoWall.vue';
import { getCameraVideoUrl_Api } from '#/api/core/camera';

// 组件引用
const deviceTreeRef = ref();
const videoWallRef = ref();

// 事件总线
const eventBus = useEventBus('device-tree');

// 获取摄像头视频URL
const fetchVideoUrl = async (camera: any) => {
  try {
    console.log('Fetching video URL for camera:', camera);

    // 使用摄像头的code作为deviceId
    const deviceId = camera.code;
    if (!deviceId) {
      throw new Error('摄像头设备ID不存在');
    }

    const response = await getCameraVideoUrl_Api(deviceId);
    if (!response) {
      throw new Error('未获取到视频流地址');
    }

    // 处理API响应数据结构
    let videoUrls: string[] = [];

    if (response && response.streamUrls) {
      videoUrls = response.streamUrls;
    }

    if (videoUrls.length > 0) {
      // 优先选择.ts 或.flv或.mp4格式的流
      const preferredUrl =
        videoUrls.find((url: string) => url.endsWith('.flv')) ||
        videoUrls.find((url: string) => url.endsWith('.mp4')) ||
        videoUrls.find((url: string) => url.endsWith('.ts')) ||
        videoUrls.find((url: string) => url.endsWith('.m3u8'));
      const finalUrl = preferredUrl || videoUrls[0];
      console.log('Setting video URL:', finalUrl);

      // 将视频添加到VideoWall
      if (videoWallRef.value) {
        videoWallRef.value.addVideo({
          url: finalUrl,
          name: camera.name,
          cameraId: camera.id,
          deviceId: deviceId,
          batteryLevel: response.batteryLevel, // 播放接口带回来的电量
          cameraRegions: response.cameraRegions, // 播放接口带回来的线框数据
          longitude: camera.longitude,
          latitude: camera.latitude,
        });
      }

      return finalUrl;
    } else {
      throw new Error('未获取到视频流地址');
    }
  } catch (error) {
    // 确保能够切换播放窗口
    videoWallRef.value.addVideo({
      url: '',
      name: '',
      cameraId: '',
      deviceId: '',
      batteryLevel: '', // 播放接口带回来的电量
      cameraRegions: '', // 播放接口带回来的线框数据
      longitude: '',
      latitude: '',
    });
    console.error('获取视频URL失败:', error);
    throw error;
  }
};

// 批量添加摄像头到VideoWall
const batchAddCameras = async (cameraList: any[]) => {
  if (!cameraList || cameraList.length === 0) {
    console.log('没有摄像头需要添加');
    return;
  }

  // 获取VideoWall当前可用的位置数量
  const availableSlots = videoWallRef.value?.getAvailableSlots() || 1; // 默认1个位置

  // 根据可用位置数量限制添加的摄像头数量
  const camerasToAdd = cameraList.slice(0, availableSlots);

  console.log(
    `批量添加摄像头: ${camerasToAdd.length}/${cameraList.length} 个摄像头`,
  );

  // 并发获取视频URL并添加到VideoWall
  const promises = camerasToAdd.map(async (camera) => {
    try {
      await fetchVideoUrl(camera);
    } catch (error) {
      console.error(`添加摄像头 ${camera.name} 失败:`, error);
    }
  });

  // 等待所有摄像头添加完成
  await Promise.allSettled(promises);

  if (camerasToAdd.length < cameraList.length) {
    console.warn(
      `由于播放位置限制，只添加了 ${camerasToAdd.length} 个摄像头，剩余 ${cameraList.length - camerasToAdd.length} 个摄像头未添加`,
    );
  }
};

// 监听设备树选择事件
const unsubscribe = eventBus.on((event: any) => {
  if (event.type === 'camera-selected') {
    console.log('Camera selected:', event.data);
    // 调用API获取视频URL并添加到VideoWall
    fetchVideoUrl(event.data).catch((error) => {
      console.error('Failed to fetch video URL:', error);
    });
  } else if (event.type === 'organ-selected') {
    console.log('Organ selected-parent:', event.data);
    // 批量添加该组织下的所有摄像头
    if (event.data.cameraList && event.data.cameraList.length > 0) {
      batchAddCameras(event.data.cameraList).catch((error) => {
        console.error('批量添加组织摄像头失败:', error);
      });
    } else {
      console.log('该组织下没有摄像头设备');
    }
  } else if (event.type === 'scene-selected') {
    console.log('Scene selected:', event.data);
    // 批量添加该场景下的所有摄像头
    if (event.data.cameraList && event.data.cameraList.length > 0) {
      batchAddCameras(event.data.cameraList).catch((error) => {
        console.error('批量添加场景摄像头失败:', error);
      });
    } else {
      console.log('该场景下没有摄像头设备');
    }
  }
});

onMounted(() => {
  console.log('Real-time monitoring page mounted');
});

onUnmounted(() => {
  // 取消事件监听
  unsubscribe();
});
</script>

<template>
  <Page class="h-full bg-[hsl(var(--background-deep))]">
    <div class="flex h-full w-full gap-[10px]">
      <div
        class="bg-card flex w-[260px] flex-col gap-[10px] rounded-[var(--radius)] p-2"
      >
        <DeviceTree ref="deviceTreeRef" />
      </div>
      <div class="bg-card flex-[1] rounded-[var(--radius)] p-2">
        <VideoWall ref="videoWallRef" />
      </div>
    </div>
  </Page>
</template>

<style lang="scss" scoped>
// bg-[hsl(var(--border))]
.control-box {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: radial-gradient(
    circle at center,
    hsl(var(--card)) 0%,
    hsl(var(--card)) 30%,
    hsl(var(--border)) 30%,
    hsl(var(--border)) 100%
  );

  > div {
    position: absolute;
  }
}

.ptz-opt {
  &:hover {
    transform: scale(1.5);
  }

  &:active {
    color: hsl(var(--primary));
  }
}
</style>
