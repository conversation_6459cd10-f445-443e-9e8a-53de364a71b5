<script setup lang="ts">
import { ref } from 'vue';
import { message } from 'ant-design-vue';

import { getCameraList_Api } from '#/api/core/inspection-plan';
import LeaFletMap from '#/components/LeaFletMap/index.vue';
import videoPng from '#/assets/images/video_sxt.png';

const emit = defineEmits(['close']);

// 弹窗状态
const open = ref(false);
const loading = ref(false);

// 地图实例和引用
let map: any = null;
const mapRef = ref();

// 摄像头数据
const cameras = ref<any[]>([]);
const taskInfo = ref<any>({});

// 摄像头标记
const cameraMarkers = ref<any[]>([]);

// 摄像头图标配置
const CAMERA_ICON_URL = videoPng;

// 获取任务下的摄像头列表
const getCameraList = async (taskId: string) => {
  try {
    loading.value = true;
    const res: any = await getCameraList_Api(taskId);
    cameras.value = res || [];

    // 如果地图已经初始化，立即渲染摄像头
    if (map) {
      renderCameras();
    }
  } catch (error) {
    console.error('获取摄像头列表失败', error);
    message.error('获取摄像头列表失败');
  } finally {
    loading.value = false;
  }
};

// 地图初始化
const onMapInit = (mapInstance: any) => {
  map = mapInstance;
  renderCameras();
};

// 渲染摄像头标记
const renderCameras = () => {
  if (!map || !mapRef.value || !cameras.value.length) return;

  // 清除现有标记
  clearCameraMarkers();

  // 准备摄像头标记数据
  const markerArray = cameras.value.map((camera: any) => {
    return {
      ...camera,
      latitude: camera.latitude || 0,
      longitude: camera.longitude || 0,
      popup: `
        <div>
          <h4>${camera.cameraName || '未命名摄像头'}</h4>
          <p>状态: ${camera.cameraStatus === 'ONLINE' ? '在线' : '离线'}</p>
          <p>巡检状态: ${getInspectionStatusText(camera.status)}</p>
        </div>
      `,
      markerOptions: {
        icon: {
          iconUrl: CAMERA_ICON_URL,
          iconSize: [24, 24],
          iconAnchor: [12, 12],
          className: `camera-marker ${camera.cameraStatus === 'ONLINE' ? 'online' : 'offline'} ${getInspectionStatusClass(camera.status)}`,
        },
      },
      popupOptions: {
        maxWidth: '200px',
      },
    };
  });

  // 创建标记
  const markers = mapRef.value.createMarker(markerArray, false);
  cameraMarkers.value = markers;

  // 如果有摄像头，调整地图视图以显示所有摄像头
  if (markerArray.length > 0) {
    const bounds = markerArray
      .filter((camera) => camera.latitude && camera.longitude)
      .map((camera) => [camera.latitude, camera.longitude]);

    if (bounds.length > 0) {
      mapRef.value.fitBounds(bounds);
    }
  }
};

// 获取巡检状态文本
const getInspectionStatusText = (status: string) => {
  switch (status) {
    case 'PENDING':
      return '待巡检';
    case 'COMPLETED':
      return '已完成';
    case 'SKIPPED':
      return '已跳过';
    default:
      return '未知';
  }
};

// 获取巡检状态样式类
const getInspectionStatusClass = (status: string) => {
  switch (status) {
    case 'PENDING':
      return 'pending';
    case 'COMPLETED':
      return 'completed';
    case 'SKIPPED':
      return 'skipped';
    default:
      return '';
  }
};

// 清除摄像头标记
const clearCameraMarkers = () => {
  if (cameraMarkers.value.length > 0) {
    cameraMarkers.value.forEach((marker) => {
      if (map && marker) {
        map.removeLayer(marker);
      }
    });
    cameraMarkers.value = [];
  }
};

// 打开弹窗
const openModal = (task: any) => {
  open.value = true;
  taskInfo.value = task;
  getCameraList(task.id);
};

// 关闭弹窗
const closeModal = () => {
  open.value = false;
  taskInfo.value = {};
  cameras.value = [];
  clearCameraMarkers();
};

// 暴露方法
defineExpose({
  openModal,
});
</script>

<template>
  <a-modal
    v-model:open="open"
    :title="`任务摄像头分布 - ${taskInfo.taskName || ''}`"
    width="1000px"
    :mask-closable="false"
    @cancel="closeModal"
  >
    <a-spin :spinning="loading">
      <div class="camera-map-content">
        <div class="map-container">
          <LeaFletMap ref="mapRef" @mapInited="onMapInit" />

          <!-- 图例 -->
          <div class="legend-box">
            <div class="legend-title">图例</div>
            <div class="legend-item">
              <div class="legend-icon online">
                <img class="img-box" :src="videoPng" alt="" />
              </div>
              <div class="legend-label">巡检摄像头</div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>

    <template #footer>
      <a-button @click="closeModal">关闭</a-button>
    </template>
  </a-modal>
</template>

<style lang="scss" scoped>
.camera-map-content {
  height: 600px;

  .stats-bar {
    padding: 12px;
    background: #f5f5f5;
    border-radius: 6px;
  }

  .map-container {
    height: 600px;
    position: relative;
    border-radius: 8px;
    overflow: hidden;

    .legend-box {
      position: absolute;
      z-index: 9999;
      bottom: 20px;
      right: 10px;
      background: rgba(255, 255, 255, 0.95);
      padding: 12px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      min-width: 120px;

      .legend-title {
        font-weight: bold;
        margin-bottom: 8px;
        font-size: 14px;
        color: #333;
      }

      .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 6px;

        &:last-child {
          margin-bottom: 0;
        }

        .legend-icon {
          width: 20px;
          height: 20px;
          margin-right: 8px;

          .img-box {
            width: 100%;
            height: 100%;
          }

          &.offline {
            filter: grayscale(100%);
          }

          &.completed {
            filter: hue-rotate(120deg) saturate(1.2);
          }

          &.pending {
            filter: hue-rotate(30deg) saturate(1.3);
          }
        }

        .legend-label {
          font-size: 12px;
          color: #666;
        }
      }
    }
  }
}

/* 摄像头标记样式 */
:deep(.leaflet-marker-icon) {
  transition: filter 0.3s ease;

  &.online {
    // 在线状态保持原色
  }

  &.offline {
    filter: grayscale(100%); /* 灰色 */
  }

  &.completed {
    filter: hue-rotate(120deg) saturate(1.2); /* 绿色 */
  }

  &.pending {
    filter: hue-rotate(30deg) saturate(1.3); /* 橙色 */
  }

  &.skipped {
    filter: hue-rotate(0deg) saturate(1.5); /* 红色 */
  }
}
</style>
