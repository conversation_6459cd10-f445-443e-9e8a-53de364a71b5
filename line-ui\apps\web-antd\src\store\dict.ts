import { defineStore } from 'pinia';

import { allDictApi } from '#/api/index';
import { ref } from 'vue';

export const useDictStore = defineStore('dict', () => {
  //是否加载字典
  const isLoadDict = ref(false);

  //所有字段数据
  const dictList = ref<any>({});

  const getAllDictList = async () => {
    dictList.value = await allDictApi();
    isLoadDict.value = true;
  };

  const getDictOptions = (
    dictName: string,
    valueType?: 'number' | 'string',
  ) => {
    if (!dictList.value[dictName]) {
      return [];
    }
    const options = JSON.parse(JSON.stringify(dictList.value[dictName]));

    if (options && options.length > 0) {
      if (valueType) {
        options.forEach((option: any) => {
          option.dictValue =
            valueType === 'number'
              ? parseInt(option.dictValue)
              : valueType === 'string'
                ? String(option.dictValue)
                : option.dictValue;
        });
        return options;
      } else {
        return options;
      }
    } else {
      return [];
    }
  };

  const getDictLable = (
    dictName: string,
    dictValue: any,
    valueType?: 'number' | 'string',
  ) => {
    if (!dictList.value[dictName]) {
      return '';
    }
    const options = JSON.parse(JSON.stringify(dictList.value[dictName]));
    if (options) {
      const item = options.find((option: any) => {
        if (valueType) {
          return valueType === 'number'
            ? parseInt(option.dictValue) === parseInt(dictValue)
            : valueType === 'string'
              ? String(option.dictValue) === String(dictValue)
              : option.dictValue === dictValue;
        } else {
          return option.dictValue === dictValue;
        }
      });
      if (item) {
        return item.dictLabel;
      } else {
        return '';
      }
    } else {
      return '';
    }
  };

  const getDictDescription = (
    dictName: string,
    dictValue: any,
    valueType?: 'number' | 'string',
  ) => {
    if (!dictList.value[dictName]) {
      return '';
    }
    const options = JSON.parse(JSON.stringify(dictList.value[dictName]));
    if (options) {
      const item = options.find((option: any) => {
        if (valueType) {
          return valueType === 'number'
            ? parseInt(option.dictValue) === parseInt(dictValue)
            : valueType === 'string'
              ? String(option.dictValue) === String(dictValue)
              : option.dictValue === dictValue;
        } else {
          return option.dictValue === dictValue;
        }
      });
      if (item) {
        return item.description || '';
      } else {
        return '';
      }
    } else {
      return '';
    }
  };

  function $reset() {
    isLoadDict.value = false;
    dictList.value = {};
  }

  return {
    $reset,
    isLoadDict,
    getAllDictList,
    getDictOptions,
    getDictLable,
    getDictDescription,
  };
});
