package com.bdyl.line.web.model.request.inspection;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import lombok.Data;

/**
 * 巡检记录请求对象
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class InspectionRecordRequest {

    /**
     * 巡检图片
     */
    private String image;
    /**
     * 任务摄像头关联ID
     */
    @NotNull(message = "任务摄像头关联ID不能为空")
    private Long taskCameraId;

    /**
     * 是否正常
     */
    @NotNull(message = "巡检结果不能为空")
    private Boolean isNormal;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remarks;
}
