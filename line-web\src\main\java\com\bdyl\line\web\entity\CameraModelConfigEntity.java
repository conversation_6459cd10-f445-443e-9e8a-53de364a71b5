package com.bdyl.line.web.entity;

import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.bdyl.line.web.entity.handler.PointListTypeHandler;
import com.bdyl.line.web.model.dto.PointDTO;

/**
 * 摄像头-模型配置实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_camera_model_config", autoResultMap = true)
public class CameraModelConfigEntity extends BaseEntity {
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 组织ID
     */
    private Long organId;
    /**
     * 摄像头ID
     */
    private Long cameraId;
    /**
     * 模型编码
     */
    private String modelCode;
    /**
     * 置信度
     */
    private BigDecimal confidence;
    /**
     * 区域点集
     */
    @TableField(typeHandler = PointListTypeHandler.class)
    private List<PointDTO> regionPoints;
}
