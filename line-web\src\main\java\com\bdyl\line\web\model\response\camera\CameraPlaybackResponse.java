package com.bdyl.line.web.model.response.camera;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;

/**
 * 摄像头回放
 */
@Data
public class CameraPlaybackResponse {

    /**
     * 视频m3u8文件名
     */
    private GBPlaybackInfoDTO playbackInfoDTO;

    /**
     * 摄像头框
     */
    private List<Point> cameraBoxes;

    /**
     * 填充摄像头框
     */
    public void fillBoxes() {

        List<Point> points = new ArrayList<>();
        Point point1 = new Point();
        point1.setX(0);
        point1.setY(0);
        Point point2 = new Point();
        point2.setX(100);
        point2.setY(0);
        Point point3 = new Point();
        point3.setX(100);
        point3.setY(100);
        Point point4 = new Point();
        point4.setX(0);
        point4.setY(100);
        points.add(point1);
        points.add(point2);
        points.add(point3);
        points.add(point4);
        cameraBoxes = points;

    }

    /**
     * 坐标点对象
     */
    @Data
    public static class Point {
        /**
         * x坐标
         */
        private int x;
        /**
         * y坐标
         */
        private int y;
    }

}
