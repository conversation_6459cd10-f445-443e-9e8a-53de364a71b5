package com.bdyl.line.web.model.response.inspection;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 巡检记录响应对象
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class InspectionRecordExportDTO {

    /**
     * 任务名称
     */
    @ExcelProperty("任务名称")
    private String taskName;

    /**
     * 摄像头名称
     */
    @ExcelProperty("摄像头名称")
    private String cameraName;

    /**
     * 巡检图片(单张)
     */
    @ExcelProperty("巡检图片")
    private String inspectionImage;

    /**
     * 是否正常
     */
    @ExcelProperty("是否正常")
    private String isNormal;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remarks;

    /**
     * 巡检时间
     */
    @ExcelProperty("巡检时间")
    private LocalDateTime inspectionTime;

    /**
     * 巡检人员姓名
     */
    @ExcelProperty("巡检人员")
    private String inspectorName;

    /**
     * 巡检周期 {@link com.bdyl.line.common.constant.enums.InspectionCycleEnum}
     */
    @ExcelProperty("巡检周期")
    private String inspectionCycle;

    /**
     * 计划开始时间
     */
    @ExcelProperty("计划开始时间")
    private LocalDateTime scheduledStartTime;

    /**
     * 计划结束时间
     */
    @ExcelProperty("计划结束时间")
    private LocalDateTime scheduledEndTime;
}
