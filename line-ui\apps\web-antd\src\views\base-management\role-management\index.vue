<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import {
  getRoleList_Api,
  deleteRole_Api,
  updateRole_Api,
} from '#/api/core/role';
import tableComp from '#/components/TableComp/table.vue';

import EditModal from './components/EditModal.vue';
import PowerModal from './components/PowerModal.vue';

import { formatDate } from '@vben/utils';

// 状态选项
const statusOptions = [
  { label: '启用', value: 'ENABLED' },
  { label: '禁用', value: 'DISABLED' },
];

// 查询条件
const queryParam = reactive<any>({
  name: undefined, // 角色名称
  status: undefined, // 状态
  page: 1,
  size: 10,
});

const loading = ref(false);
const modalFormRef = ref();
const modalPowerRef = ref();

// 表格数据源
const tableData = reactive<any>({
  data: {},
});

// 表格相关配置
const columns = [
  { title: '角色名称', dataIndex: 'name' },
  { title: '角色类型', dataIndex: 'roleType' },
  { title: '角色标识', dataIndex: 'code' },
  { title: '描述', dataIndex: 'description', ellipsis: true },
  { title: '状态', dataIndex: 'status' },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    customRender: ({ record }: any) => {
      return formatDate(record.createTime, 'YYYY-MM-DD HH:mm:ss');
    },
  },
  { title: '操作', dataIndex: 'operation', width: '250px' },
];

// 加载角色列表
const loadRoleList = async () => {
  loading.value = true;
  try {
    const res = await getRoleList_Api(queryParam);

    // 更新表格数据源
    const temp = {
      data: res.data || res.records || [],
      page: res.page || res.current || 1,
      size: res.size || 10,
      total: res.total || 0,
    };
    tableData.data = temp;
  } catch (error) {
    console.error('获取角色列表失败', error);
    message.error('获取角色列表失败');
  } finally {
    loading.value = false;
  }
};
const success = (data: any) => {
  queryParam.page = data.pi;
  queryParam.size = data.ps;
  loadRoleList();
};

// 查询
const searchTable = () => {
  queryParam.page = 1;
  loadRoleList();
};

// 重置
const resetTable = () => {
  queryParam.name = undefined;
  queryParam.status = undefined;
  queryParam.page = 1;
  queryParam.size = 10;
  loadRoleList();
};

// 添加角色
const handleAdd = () => {
  modalFormRef.value.openModal('create');
};

// 编辑角色
const handleEdit = (record: any) => {
  modalFormRef.value.openModal('update', record);
};

// 权限配置
const handlePower = (record: any) => {
  modalPowerRef.value.openModal(record);
};

// 删除角色
const handleDelete = async (record: any) => {
  try {
    await deleteRole_Api(record.id);
    message.success('操作成功');
    loadRoleList();
  } catch (error) {
    console.error('删除角色失败', error);
  }
};

// 修改角色状态
const handleStatusChange = (record: any) => {
  const temp = {
    status: record.status,
    code: record.code,
    name: record.name,
    description: record.description,
  };

  updateRole_Api(record.id, temp)
    .then(() => {
      message.success('操作成功');
      loadRoleList();
    })
    .catch((error) => {
      console.error('更新角色状态失败', error);
      loadRoleList(); // 刷新数据，恢复原状态
    });
};

// 角色保存成功回调
const handleSaveSuccess = () => {
  loadRoleList();
};

onMounted(() => {
  loadRoleList();
});
</script>

<template>
  <Page>
    <a-card class="table_header_search mb-5">
      <a-row :gutter="20">
        <a-col :span="6">
          <label>角色名称：</label>
          <div class="table_header_wrp_cont">
            <a-input
              v-model:value="queryParam.name"
              allow-clear
              placeholder="请输入角色名称"
            />
          </div>
        </a-col>
        <a-col :span="6">
          <label>状&emsp;态：</label>
          <div class="table_header_wrp_cont">
            <a-select
              v-model:value="queryParam.status"
              allow-clear
              style="width: 100%"
              placeholder="请选择状态"
            >
              <a-select-option
                v-for="option in statusOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </a-select-option>
            </a-select>
          </div>
        </a-col>

        <a-col :span="5">
          <a-button type="primary" class="searchBtn" @click="searchTable">
            查询
          </a-button>
          <a-button class="refBtn" @click="resetTable">重置</a-button>
        </a-col>
      </a-row>
    </a-card>

    <a-card size="small">
      <div class="table_action_btn_wrp">
        <a-button class="addBtn" type="primary" @click="handleAdd">
          新建
        </a-button>
      </div>
      <table-Comp
        :columns="columns"
        :data-source="tableData.data"
        :loading="loading"
        :scroll="{ x: 1200 }"
        @is-loading-fuc="(e) => (loading = e)"
        @success="success"
      >
        <!-- 角色类型 -->
        <template #roleType="{ record }">
          <a-tag
            v-if="record.roleType === 'CUSTOM'"
            color="processing"
            :bordered="false"
          >
            内置角色
          </a-tag>
          <a-tag v-else color="blue" :bordered="false"> 自定义角色 </a-tag>
        </template>

        <!-- 状态列 -->
        <template #status="{ record }">
          <a-switch
            v-model:checked="record.status"
            checkedValue="ENABLED"
            unCheckedValue="DISABLED"
            @change="handleStatusChange(record)"
          />
        </template>

        <!-- 操作列 -->
        <template #operation="{ record }">
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-button type="link" @click="handlePower(record)">权限配置</a-button>
          <a-popconfirm
            title="数据删除后不可恢复，请确认后继续?"
            @confirm="handleDelete(record)"
          >
            <a-button type="link" danger>删除</a-button>
          </a-popconfirm>
        </template>
      </table-Comp>
    </a-card>

    <!-- 角色弹窗 -->
    <EditModal ref="modalFormRef" @success="handleSaveSuccess" />

    <!-- 权限配置弹窗 -->
    <PowerModal ref="modalPowerRef" @success="handleSaveSuccess" />
  </Page>
</template>
