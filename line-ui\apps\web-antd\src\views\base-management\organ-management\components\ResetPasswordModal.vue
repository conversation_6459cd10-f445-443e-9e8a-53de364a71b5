<script lang="ts" setup>
import { nextTick, reactive, ref } from 'vue';

import { message } from 'ant-design-vue';

// import { resetPassword_Api } from '#/api/core/organ';

const emit = defineEmits(['success']);

const open = ref<boolean>(false);
const modalTitle = ref<string>('');
const formRef = ref();
const loading = ref(false);
const labelCol = { span: 6 };
const wrapperCol = { span: 16 };

// 表单数据
const formData = reactive<any>({
  id: undefined,
  oldPassword: undefined,
  newPassword: undefined,
  confirmPassword: undefined,
});

// 表单验证规则
const formRules = {
  oldPassword: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
  newPassword: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { max: 72, message: '密码最长72个字符', trigger: 'blur' },
    {
      validator: (_rule: any, value: string) => {
        if (value && value !== formData.newPassword) {
          return Promise.reject('两次输入的密码不一致');
        }
        return Promise.resolve();
      },
      trigger: 'blur',
    },
  ],
};
// 重置表单
const resetForm = async () => {
  await formRef.value?.resetFields();
  Object.assign(formData, {
    id: undefined,
    oldPassword: undefined,
    newPassword: undefined,
    confirmPassword: undefined,
  });
};

// 打开弹窗
const openModal = async (record: any) => {
  open.value = true;
  modalTitle.value = '修改密码：' + record.name;
  await resetForm();

  if (record) {
    formData.id = record.id;
  }
};

// 关闭弹窗
const closeModal = () => {
  open.value = false;
  resetForm();
};

// 重置密码
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;

    // await resetPassword_Api(formData);
    message.success('密码修改成功');

    closeModal();
    emit('success');
  } catch (error) {
    console.error('重置密码失败', error);
  } finally {
    loading.value = false;
  }
};

// 暴露组件方法
defineExpose({
  openModal,
});
</script>
<template>
  <a-modal
    v-model:open="open"
    :title="modalTitle"
    :confirm-loading="loading"
    :mask-closable="false"
    width="600px"
    @cancel="closeModal"
  >
    <a-form
      ref="formRef"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :model="formData"
      :rules="formRules"
    >
      <a-form-item label="旧密码" name="oldPassword">
        <a-input-password
          v-model:value="formData.oldPassword"
          placeholder="请输入旧密码"
        />
      </a-form-item>

      <a-form-item label="新密码" name="newPassword">
        <a-input-password
          v-model:value="formData.newPassword"
          placeholder="请输入新密码"
        />
      </a-form-item>

      <a-form-item label="再次输入新密码" name="confirmPassword">
        <a-input-password
          v-model:value="formData.confirmPassword"
          placeholder="请再次输入新密码"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>
