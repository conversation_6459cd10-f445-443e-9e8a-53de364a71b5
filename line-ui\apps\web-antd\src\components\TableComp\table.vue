<!--表格封装-->
<template>
  <div>
    <a-table
      :dataSource="tabListData"
      :columns="columns"
      :pagination="paginationProp"
      :row-key="(record) => record.id"
      :loading="loading"
      :row-selection="isSelectRow ? rowSelection : null"
      :scroll="scroll"
    >
      <!--  转换    -->
      <template #bodyCell="{ column, record }">
        <slot :name="column.dataIndex" :record="record"></slot>
      </template>
      <!--      展开每行-->
      <template #expandedRowRender="{ column, record }" v-if="isSelectRow">
        <slot name="expRow" :record="record"></slot>
      </template>
    </a-table>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, defineProps, defineEmits, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
const props = defineProps({
  dataSource: { type: Object, default: () => {} }, // 数据源
  columns: { type: Array, default: [] }, // 头部
  scroll: { type: Object, default: {} }, // 滚动条
  loading: { type: Boolean, default: true }, // 加载
  isExpRow: { type: Boolean, default: false }, // 是否展开行
  isSelectRow: { type: Boolean, default: false }, // 是添加选框
});
const emit = defineEmits([
  'success',
  'isLoadingFuc',
  'onChangeTableSelect',
  'onSelectTableSelect',
  'onTableSelectAll',
]); // 定义 success 事件，用于操作成功后的回调
//分页相关
let total = ref<number>(0); // 总条数
let pageSize = ref<number>(10); // 每页多少条数据
let currentPage = ref<number>(1); // 当前第几页
const paginationProp = ref({
  pageSize,
  current: currentPage,
  total,
  showTotal: (total) => `共 ${total} 条数据`,
  onChange: (page) => {
    currentPage.value = page;
    emit('success', { pi: currentPage.value, ps: pageSize.value });
  },
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
  onShowSizeChange: (page, size) => {
    currentPage.value = page;
    pageSize.value = size;
  },
});
const tabListData = ref();
const getTbList = async () => {
  const res = props.dataSource;
  if (res) {
    total.value = Number(res.total) || 0;
    pageSize.value = Number(res.size) || 10;
    currentPage.value = Number(res.page) || 1;
    tabListData.value = res.data || [];
    emit('isLoadingFuc', false);
    // 判断当前页是否有数据 没有则刷新表格到第一页
    if (Number(res.page) !== 1 && res.data?.length === 0) {
      emit('success', { pi: 1, ps: 10 });
    }
  } else {
    emit('isLoadingFuc', true);
    message.error('获取数据失败！');
  }
};
// 选框
const rowSelection = ref({
  onChange: (selectedRowKeys, selectedRows) => {
    emit('onTableChange', {
      selectedRowKeys: selectedRowKeys,
      selectedRows: selectedRows,
    });
  },
  onSelect: (record, selected, selectedRows) => {
    emit('onTableSelect', { selected: selected, selectedRows: selectedRows });
  },
  onSelectAll: (selected, selectedRows, changeRows) => {
    emit('onTableSelectAll', {
      selected: selected,
      selectedRows: selectedRows,
      changeRows: changeRows,
    });
  },
});
watch(
  () => props.dataSource,
  (value) => {
    getTbList();
  },
  // 配置意味着watch会在组件实例创建时立即以当前的props.dataSource值触发一次回调，
  // 而不是等到props.dataSource第一次变化时才触发
  // { immediate: true },
);
onMounted(() => {});
</script>

<style lang="scss" scoped>
:deep(.ant-table-measure-row) {
  display: none;
}
</style>
