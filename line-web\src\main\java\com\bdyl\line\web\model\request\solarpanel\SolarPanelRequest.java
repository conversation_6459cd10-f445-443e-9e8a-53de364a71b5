package com.bdyl.line.web.model.request.solarpanel;

import jakarta.validation.constraints.NotNull;

import lombok.Data;

/**
 * 太阳能电池板请求对象。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class SolarPanelRequest {
    /**
     * 终端ID
     */
    @NotNull(message = "终端ID不能为空")
    private Long terminalId;

    /**
     * 所属组织ID
     */
    private Long organId;
    /**
     * 蓄电池温度(℃)
     */
    private Double batteryTemperature;
    /**
     * 蓄电池电压(V)
     */
    private Double batteryVoltage;
    /**
     * 蓄电池电流(A)
     */
    private Double batteryCurrent;
    /**
     * 蓄电池剩余电量(%)
     */
    private Double batteryLevel;
    /**
     * 直流负载电压(V)
     */
    private Double dcLoadVoltage;
    /**
     * 直流负载电流(A)
     */
    private Double dcLoadCurrent;
    /**
     * 直流负载功率(W)
     */
    private Double dcLoadPower;
    /**
     * 当日用电量(kWh)
     */
    private Double dcLoadEnergyToday;
    /**
     * PV组件电压(V)
     */
    private Double pvVoltage;
    /**
     * PV组件电流(A)
     */
    private Double pvCurrent;
    /**
     * PV组件发电功率(W)
     */
    private Double pvPower;
}
