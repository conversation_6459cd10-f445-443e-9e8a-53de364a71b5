package com.bdyl.line.web.controller;

import java.time.LocalDate;
import java.util.List;

import jakarta.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.*;

import com.bdyl.boot.JsonResult;
import com.bdyl.boot.data.query.Page;
import com.bdyl.line.web.model.request.solarpanel.SolarPanelPageRequest;
import com.bdyl.line.web.model.request.solarpanel.SolarPanelRequest;
import com.bdyl.line.web.model.response.solarpanel.SolarPanelHourStatItem;
import com.bdyl.line.web.model.response.solarpanel.SolarPanelResponse;
import com.bdyl.line.web.service.SolarPanelService;
import com.bdyl.line.web.utils.CurrentUserBizHelper;

/**
 * 太阳能电池板管理
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@RestController
@RequestMapping("/solar-panel")
@RequiredArgsConstructor
public class SolarPanelController {
    /**
     * 太阳能电池板service
     */
    private final SolarPanelService solarPanelService;

    /**
     * 新增太阳能电池板实时数据
     *
     * @param request 请求对象
     * @return 是否成功
     */
    @PostMapping
    public JsonResult<Boolean> create(@Valid @RequestBody SolarPanelRequest request) {
        Long currentOrganId = CurrentUserBizHelper.getCurrentOrganId();
        return JsonResult.success(solarPanelService.create(request, currentOrganId));
    }

    /**
     * 分页查询太阳能电池板数据
     *
     * @param request 分页请求
     * @return 分页结果
     */
    @GetMapping("/page")
    public JsonResult<Page<SolarPanelResponse>> page(@Valid SolarPanelPageRequest request) {
        return JsonResult.success(solarPanelService.page(request));
    }

    /**
     * 查询蓄电池最新实时数据
     *
     * @param terminalId 终端ID
     * @return 实时数据
     */
    @GetMapping("/realtime")
    public JsonResult<SolarPanelResponse> getLatest(@RequestParam Long terminalId) {
        return JsonResult.success(solarPanelService.getLatestByTerminalId(terminalId));
    }

    /**
     * 查询蓄电池某天24小时统计数据
     *
     * @param terminalId 终端ID
     * @param day 日期(yyyy-MM-dd)
     * @return 24小时统计
     */
    @GetMapping("/stat24h")
    public JsonResult<List<SolarPanelHourStatItem>> stat24Hour(@RequestParam Long terminalId,
        @RequestParam LocalDate day) {
        return JsonResult.success(solarPanelService.stat24Hour(terminalId, day));
    }
}
