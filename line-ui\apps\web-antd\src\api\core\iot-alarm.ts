import { requestClient } from '#/api/request';

enum Api {
  iotAlarm = '/api/line/iot-alerts',
}

export namespace IotAlarmApi {
  export interface AlarmListParams {
    page: number;
    size: number;
    type?: string;
    tenantId?: string;
    [propName: string]: any;
  }

  export interface Records {
    id: number;
    tenantId: number;
    type: string;
    deviceName: string;
    region: string;
    address: string;
    sceneName: string;
    verifyStatus: string;
    image: string;
    video: string;
    creatorId: number;
    updaterId: number;
    createTime: string;
    updateTime: string;
    organId: number;
  }

  export interface AlarmListResult {
    data: Records[];
    orders: any[];
    page: number;
    pages: number;
    size: number;
    total: number;
  }

  export interface AlarmCheckParams {
    id: string;
    checkResult: string;
    remark?: string;
  }
}

// 分页获取物联设备报警列表
export const getIotAlarmList_Api = (params: IotAlarmApi.AlarmListParams) => {
  return requestClient.get<IotAlarmApi.AlarmListResult>(
    `${Api.iotAlarm}/page`,
    {
      params,
    },
  );
};

// 获取物联设备报警详情
export const getIotAlarm_Api = (id: string) => {
  return requestClient.get<IotAlarmApi.Records>(`${Api.iotAlarm}/${id}`);
};


// 报警数据导出
export const exportIotAlarm_Api = (params:any) => {
   return requestClient.download(`${Api.iotAlarm}/export`, { params });
}
