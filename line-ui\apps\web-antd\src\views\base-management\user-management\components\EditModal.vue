<script lang="ts" setup>
import { computed, nextTick, onMounted, reactive, ref } from 'vue';

import { message } from 'ant-design-vue';

import {
  addUser_Api,
  updateUser_Api,
  getRegionByOrgan_Api,
  getRoleList_Api,
  getDepartmentList_Api,
} from '#/api/core';
import { useDictStore } from '#/store';

import { useUserStore } from '@vben/stores';

const userStore = useUserStore();

const organType = userStore.userInfo?.organType;
// const regionCode = userStore.userInfo?.regionCode;

const emit = defineEmits(['success', 'getRoleData']);

const open = ref<boolean>(false);
const modalTitle = ref<string>('');
const formRef = ref();
const loading = ref(false);
const labelCol = { span: 6 };
const wrapperCol = { span: 16 };

const dictStore = useDictStore();

// 根据当前用户的组织类型过滤区域级别选项
const regionLevelOptions = computed(() => {
  const allOptions = dictStore.getDictOptions('RegionLevelEnum');

  // 如果是市级用户，只显示市级选项
  if (organType === 'CITY') {
    return allOptions.filter((item: any) => item.dictValue === 'CITY');
  }

  // 如果是省级用户，只显示省级和市级选项
  if (organType === 'PROVINCE') {
    return allOptions.filter((item: any) => 
      ['PROVINCE', 'CITY'].includes(item.dictValue)
    );
  }

  // 平台级用户可以看到所有选项
  return allOptions;
});

// 区域相关
const regionOptions = ref<any[]>([]);

// 表单数据
const formData = reactive<any>({
  id: undefined,
  organType: undefined,
  name: undefined,
  account: undefined,
  phone: undefined,
  roleIds: [],
  organId: undefined,
  regionCode: undefined,
  departmentId: undefined,
  password: undefined,
  remark: undefined,
});

// 手机号验证规则
const validatePhone = (_rule: any, value: string) => {
  if (!value) {
    return Promise.resolve();
  }
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(value)) {
    return Promise.reject(new Error('请输入正确的手机号码'));
  }
  return Promise.resolve();
};

// 表单验证规则
const formRules = {
  organType: [{ required: true, message: '请选择组织类型', trigger: 'change' }],
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  account: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { max: 72, message: '密码最长72个字符', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' },
  ],
  roleIds: [{ required: true, message: '请选择角色', trigger: 'change' }],
  regionCode: [{ required: true, message: '请选择地区', trigger: 'change' }],
};

// 是否为编辑模式
const isEdit = computed(() => !!formData.id);

// 重置表单
const resetForm = async () => {
  await formRef.value?.resetFields();
  Object.assign(formData, {
    id: undefined,
    organType: undefined,
    name: undefined,
    account: undefined,
    password: undefined,
    phone: undefined,
    roleIds: [],
    organId: undefined,
    regionCode: undefined,
    departmentId: undefined,
    remark: undefined,
  });
};

// 获取所有角色
const roleOptions = ref<any>([]);
const getRoleData = () => {
  getRoleList_Api({ page: 1, size: 10000 }).then((res: any) => {
    roleOptions.value = res.data.map((item: any) => ({
      label: item.name,
      value: item.id,
    }));
    emit('getRoleData', roleOptions.value);
  });
};

// 获取所有部门
const departmentOptions = ref<any>([]);
const getDepartmentData = () => {
  getDepartmentList_Api({ page: 1, size: 10000 }).then((res: any) => {
    departmentOptions.value = res.data.map((item: any) => ({
      label: item.name,
      value: item.id,
    }));
  });
};

// 获取组织树
const loadInitialRegions = async () => {
  const res = await getRegionByOrgan_Api();
  return res.children || [];
};

// 打开弹窗
const openModal = async (type: string, record?: any) => {
  open.value = true;
  modalTitle.value = type === 'create' ? '新增用户' : '编辑用户';
  await resetForm();

  // 加载初始区域数据
  regionOptions.value = await loadInitialRegions();

  if (type === 'update' && record) {
    nextTick(() => {
      // 编辑模式，只填充formData中已定义的字段
      Object.keys(formData).forEach((key: any) => {
        if (record[key] !== undefined) {
          formData[key] = record[key];
          if (key === 'roleIds' && !record[key]) {
            formData[key] = [];
          }
        }
      });
    });
  }
};
// 关闭弹窗
const closeModal = () => {
  open.value = false;
  resetForm();
};

// 保存用户
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;

    if (
      formData.regionCode &&
      formData.regionCode.length &&
      Array.isArray(formData.regionCode)
    ) {
      formData.regionCode = formData.regionCode[formData.regionCode.length - 1];
    }

    if (isEdit.value) {
      // 更新用户
      await updateUser_Api(formData.id, formData);
      message.success('更新成功');
    } else {
      // 新增用户
      await addUser_Api(formData);
      message.success('添加成功');
    }

    closeModal();
    emit('success');
  } catch (error) {
    console.error('保存用户失败', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  getRoleData();
  getDepartmentData();
});

// 暴露组件方法
defineExpose({
  openModal,
});
</script>
<template>
  <a-modal
    v-model:open="open"
    :title="modalTitle"
    :confirm-loading="loading"
    :mask-closable="false"
    width="600px"
    @cancel="closeModal"
  >
    <a-form
      ref="formRef"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :model="formData"
      :rules="formRules"
    >
      <a-form-item label="组织类型" name="organType">
        <a-radio-group v-model:value="formData.organType">
          <a-radio
            v-for="item in regionLevelOptions"
            :key="item.dictValue"
            :value="item.dictValue"
          >
            {{ item.dictLabel }}
          </a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item
        label="地区"
        name="regionCode"
        v-if="formData.organType !== 'PLATFORM'"
      >
        <a-cascader
          v-model:value="formData.regionCode"
          :options="regionOptions"
          placeholder="请选择地区"
          style="width: 100%"
          change-on-select
          :field-names="{ label: 'fullRegionName', value: 'regionCode' }"
        />
      </a-form-item>
      <a-form-item label="用户姓名" name="name">
        <a-input v-model:value="formData.name" placeholder="请输入用户姓名" />
      </a-form-item>
      <a-form-item label="用户手机号" name="phone">
        <a-input
          v-model:value="formData.phone"
          placeholder="请输入用户手机号"
        />
      </a-form-item>
      <a-form-item label="用户账号" name="account">
        <a-input
          v-model:value="formData.account"
          placeholder="请输入用户账号"
        />
      </a-form-item>
      <a-form-item v-if="!isEdit" label="用户密码" name="password">
        <a-input-password
          v-model:value="formData.password"
          placeholder="请输入用户密码"
        />
      </a-form-item>

      <a-form-item label="角色" name="roleIds">
        <a-select
          v-model:value="formData.roleIds"
          mode="multiple"
          placeholder="请选择角色"
          style="width: 100%"
          :options="roleOptions"
        />
      </a-form-item>

      <a-form-item label="部门" name="departmentId">
        <a-select
          v-model:value="formData.departmentId"
          placeholder="请选择部门"
          style="width: 100%"
          :options="departmentOptions"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>
