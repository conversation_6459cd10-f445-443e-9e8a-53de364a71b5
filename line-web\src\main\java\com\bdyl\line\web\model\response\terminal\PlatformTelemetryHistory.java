package com.bdyl.line.web.model.response.terminal;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 历史遥测记录实体类 用于保存所有的遥测记录，不进行去重
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PlatformTelemetryHistory {

    /**
     * 终端编码
     */
    private String terminalCode;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 遥测数据
     */
    private List<DataProperty> data;

    /**
     * 创建时间
     */
    private String createTime;
}
