package com.bdyl.line.web.model.request.inspection;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import lombok.Data;

import com.bdyl.line.web.model.dto.TimeSlotDTO;

/**
 * 巡检计划请求对象
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class InspectionPlanRequest {
    /**
     * 计划名称
     */
    @NotBlank(message = "计划名称不能为空")
    @Size(max = 128, message = "计划名称长度不能超过128个字符")
    private String name;

    /**
     * 巡检周期类型 {@link com.bdyl.line.common.constant.enums.InspectionCycleEnum}
     */
    @NotBlank(message = "巡检周期类型不能为空")
    private String cycleType;

    /**
     * 周期值
     */
    @NotNull(message = "周期值不能为空")
    private Integer cycleValue;

    /**
     * 摄像头ID列表
     */
    @NotEmpty(message = "摄像头列表不能为空")
    private List<Long> cameraIds;

    /**
     * 启动日期
     */
    @NotNull(message = "启动日期不能为空")
    private LocalDate startDate;

    /**
     * 巡检负责人ID
     */
    @NotNull(message = "巡检负责人不能为空")
    private Long responsibleUserId;

    /**
     * 计划描述
     */
    @Size(max = 255, message = "计划描述长度不能超过255个字符")
    private String description;

    /**
     * 日期值(周类型:1-7表示星期几，月类型:1-31表示每月几号) 周类型：1=周一, 2=周二, ..., 7=周日 月类型：1-31表示每月几号
     */
    private Integer dayValue;

    /**
     * 时间段配置(天类型使用) 天类型可以配置多个时间段，如：2:00-3:00, 12:00-13:00
     */
    private List<TimeSlotDTO> timeSlots;

    /**
     * 开始时间(日/周/月类型使用)
     */
    private LocalTime startTime;

    /**
     * 结束时间(日/周/月类型使用)
     */
    private LocalTime endTime;
}
