import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    name: 'BaseManagement',
    path: '/base-management',
    meta: {
      icon: 'mdi:database',
      order: 100,
      title: $t('page.basemanagement.title'),
      authority: ['basic:menu'],
    },
    children: [
      {
        name: 'organManagement',
        path: 'organ-management',
        component: () =>
          import('#/views/base-management/organ-management/index.vue'),
        meta: {
          icon: 'ant-design:cluster',
          order: 30,
          title: $t('page.basemanagement.organTitle'),
          authority: ['organ:menu'],
        },
      },
      {
        name: 'UserManagement',
        path: 'user-management',
        component: () =>
          import('#/views/base-management/user-management/index.vue'),
        meta: {
          icon: 'mdi:user',
          order: 40,
          title: $t('page.basemanagement.userTitle'),
          authority: ['user:menu'],
        },
      },
      {
        name: 'RoleManagement',
        path: 'role-management',
        component: () =>
          import('#/views/base-management/role-management/index.vue'),
        meta: {
          icon: 'mdi:account-lock',
          order: 50,
          title: $t('page.basemanagement.roleTitle'),
          authority: ['role:menu'],
        },
      },
      {
        name: 'departmentManagement',
        path: 'department-management',
        component: () =>
          import('#/views/base-management/department-management/index.vue'),
        meta: {
          icon: 'ant-design:apartment',
          order: 60,
          title: $t('page.basemanagement.departmentTitle'),
          authority: ['department:menu'],
        },
      },
      // {
      //   name: 'devicesManagement',
      //   path: 'devices-management',
      //   component: () =>
      //     import('#/views/base-management/devices-management/index.vue'),
      //   meta: {
      //     icon: 'mdi:user-card-details',
      //     hideInMenu: true,
      //     order: 70,
      //     title: $t('page.basemanagement.devicesTitle'),
      //   },
      // },
    ],
  },
];

export default routes;
