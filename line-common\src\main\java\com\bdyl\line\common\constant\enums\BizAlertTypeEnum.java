package com.bdyl.line.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务报警类型枚举
 *
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@AllArgsConstructor
public enum BizAlertTypeEnum {

    // 重型机械入侵报警、非法人员/车辆闯入报警、土堆堆积报警、电线杆倾斜报警

    /**
     * 测试用的
     */
    PERSON("person", "人员检测", "检测是否有人"),
    /**
     * 打架
     */
    FIGHT("fight", "打架检测", "检测是否有人打架"),
    /**
     * 抽烟
     */
    SMOKING("smoking_face", "抽烟检测", "检测是否有人抽烟"),
    /**
     * 动土识别
     */
    EXCAVATION_DETECTION("EXCAVATION_DETECTION", "土堆检测", "土堆堆积报警"),
    /**
     * 周界入侵
     */
    PERIMETER_INTRUSION("PERIMETER_INTRUSION", "人员车辆检测", "重型机械入侵报警"),
    /**
     * 电线杆倾倒
     */
    POLE_COLLAPSE("POLE_COLLAPSE", "电线杆倾斜", "电线杆倾斜报警"),
    /**
     * 工程车识别
     */
    CONSTRUCTION_VEHICLE("CONSTRUCTION_VEHICLE", "重型机械检测", "重型机械入侵报警");

    /**
     * value
     */
    private final String value;
    /**
     * 名称
     */
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据枚举值获取枚举类
     *
     * @param value 枚举值
     * @return 枚举类
     */
    public static BizAlertTypeEnum fromValue(String value) {
        for (BizAlertTypeEnum type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown type: " + value);
    }

    /**
     * 根据枚举名称获取枚举类
     *
     * @param name 枚举名称
     * @return 枚举类
     */
    public static BizAlertTypeEnum fromName(String name) {
        for (BizAlertTypeEnum type : values()) {
            if (type.name.equals(name)) {
                return type;

            }
        }
        throw new IllegalArgumentException("Unknown type: " + name);
    }

}
