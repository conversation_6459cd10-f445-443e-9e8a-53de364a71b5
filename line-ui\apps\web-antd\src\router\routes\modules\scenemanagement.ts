import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    name: 'SceneManagement',
    path: '/scene-management',
    meta: {
      icon: 'mdi:map', // 可根据实际需求替换图标（如使用其他mdi图标）
      order: 15, // 建议根据现有路由顺序调整（basemanagement为20，videomonitoring可设为25，alarmmanagement为30，此处设为35）
      title: $t('page.scenemanagement.title'), // 需在国际化文件中添加对应翻译
      authority: ['scene:menu'],
    },
    children: [
      {
        name: 'SceneList',
        path: 'scene-list',
        component: () =>
          import('#/views/scene-management/scene-list/index.vue'), // 需确保对应视图文件存在
        meta: {
          icon: 'mdi:format-list-bulleted',
          order: 40,
          title: $t('page.scenemanagement.listTitle'), // 需在国际化文件中添加对应翻译
          authority: ['sceneManage:menu'],
        },
      },
      {
        name: 'SceneDivision',
        path: 'scene-division',
        component: () =>
          import('#/views/scene-management/scene-division/index.vue'), // 需确保对应视图文件存在
        meta: {
          icon: 'mdi:map-marker',
          order: 45,
          title: $t('page.scenemanagement.divisionTitle'), // 需在国际化文件中添加对应翻译
          authority: ['region:menu'],
        },
      },
    ],
  },
];

export default routes;
