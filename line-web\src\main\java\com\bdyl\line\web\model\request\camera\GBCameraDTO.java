package com.bdyl.line.web.model.request.camera;

import java.time.LocalDateTime;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 设备数据传输对象
 */
@Data
public class GBCameraDTO {

    /**
     * 国标设备编码
     */
    @NotBlank(message = "设备编码不能为空")
    @Size(max = 64, message = "设备编码长度不能超过64个字符")
    private String deviceCode;

    /**
     * 设备名称
     */
    @Size(max = 128, message = "设备名称长度不能超过128个字符")
    private String name;

    /**
     * 厂商
     */
    @Size(max = 128, message = "厂商长度不能超过128个字符")
    private String manufacturer;

    /**
     * 型号
     */
    @Size(max = 64, message = "型号长度不能超过64个字符")
    private String model;

    /**
     * 固件版本
     */
    @Size(max = 64, message = "固件版本长度不能超过64个字符")
    private String firmware;

    /**
     * 设备接入密码
     */
    @Size(max = 128, message = "设备接入密码长度不能超过128个字符")
    private String password;

    /**
     * 安装地址
     */
    @Size(max = 256, message = "安装地址长度不能超过256个字符")
    private String installAddress;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 父设备/上级平台ID
     */
    private String parentId;

    /**
     * 在线状态（0离线/1在线）
     */
    private Boolean online;

    /**
     * 最近注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registerTime;

    /**
     * 最近心跳时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime keepaliveTime;

    /**
     * 设备IP
     */
    private String ip;

    /**
     * 设备端口
     */
    private Integer port;

    /**
     * 传输协议（UDP/TCP/TLS）
     */
    @Size(max = 16, message = "传输协议长度不能超过16个字符")
    private String transport;

    /**
     * 设备注册过期时间（秒）
     */
    private Long expires;
}
