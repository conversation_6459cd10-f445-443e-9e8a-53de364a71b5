package com.bdyl.line.web.model.response.organ;

import java.util.List;

import lombok.Data;

import com.bdyl.line.common.constant.enums.RegionLevelEnum;

/**
 * 组织相应体
 */
@Data
public class OrganTreeDTO {
    /**
     * 机构id
     */
    private Long id;

    /**
     * 父组织ID
     */
    private Long parentId;
    /**
     * 组织类型 {@link RegionLevelEnum}
     */
    private String orgType;
    /**
     * 地区
     */
    private String regionCode;

    /**
     * 完整区划(用于前端展示)
     */
    private String fullRegionName;

    /**
     * 子集
     */
    List<OrganTreeDTO> children;

}
