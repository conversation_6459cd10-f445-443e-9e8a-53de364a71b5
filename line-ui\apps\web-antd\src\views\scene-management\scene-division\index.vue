<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { Page } from '@vben/common-ui';
import { message } from 'ant-design-vue';

import { getSceneCameraCount_Api, bindCameras_Api } from '#/api/core/scene';
import { getCameras_Api } from '#/api/core/camera';
import LeaFletMap from '#/components/LeaFletMap/index.vue';
import videoPng from '#/assets/images/video_sxt.png';

// 地图实例和引用
let map: any = null;
const mapRef = ref();

// 场景摄像头数量
const sceneCameraCount = ref<any>([]);
const cameras = ref<any>([]);

// 操作模式状态
const operationMode = ref<'none' | 'batch' | 'single'>('none');
const isDrawing = ref(false);

// 摄像头标记和选中状态
const cameraMarkers = ref<any[]>([]);
const selectedCameras = ref<any[]>([]);
const drawnPolygon = ref<any>(null);

// 弹窗状态
const showBindModal = ref(false);
const selectedSceneId = ref<string>('');

// 选中的场景
const selectedScene = ref<any>(null);

// 摄像头图标配置 - 使用现有的视频图标
const CAMERA_ICON_URL = videoPng;

// 获取场景数据
const loadSceneCameraCount = async () => {
  try {
    const res = await getSceneCameraCount_Api();
    sceneCameraCount.value = res;
  } catch (error) {
    console.error('获取场景摄像头数量失败', error);
  }
};

// 获取所有摄像头
const getCameras = async () => {
  try {
    const res = await getCameras_Api();
    cameras.value = res || [];

    // 如果地图已经初始化，立即渲染摄像头
    if (map) {
      renderCameras();
    }
  } catch (error) {
    console.error('获取摄像头列表失败', error);
  }
};

// 地图初始化
const onMapInit = (mapInstance: any) => {
  map = mapInstance;
  renderCameras();
};

// 渲染摄像头标记
const renderCameras = () => {
  if (!map || !mapRef.value || !cameras.value.length) return;

  // 清除现有标记
  clearCameraMarkers();

  // 准备摄像头标记数据
  const markerArray = cameras.value.map((camera: any) => {
    // 检查是否已选中
    const isSelected = selectedCameras.value.some((c) => c.id === camera.id);

    return {
      ...camera,
      latitude: camera.latitude,
      longitude: camera.longitude,
      popup: camera.name || '未命名摄像头',
      markerOptions: {
        icon: {
          iconUrl: CAMERA_ICON_URL,
          iconSize: [24, 24],
          iconAnchor: [12, 12],
          // 使用CSS过滤器改变图标颜色
          className: `camera-marker ${camera.status === 'ONLINE' ? 'online' : 'offline'} ${isSelected ? 'selected' : ''}`,
        },
      },
      popupOptions: {
        maxWidth: '200px',
      },
    };
  });

  // 创建标记
  const markers = mapRef.value.createMarker(markerArray, false);

  // 为每个标记添加点击事件
  markers.forEach((marker: any, index: number) => {
    marker.on('click', () => {
      if (operationMode.value === 'single') {
        handleCameraClick(cameras.value[index], marker);
      }
    });
  });

  cameraMarkers.value = markers;

  const bounds = markerArray.map((marker: any) => [
    marker.latitude,
    marker.longitude,
  ]);
  map.fitBounds(bounds, { padding: [20, 20] });
};

// 清除摄像头标记
const clearCameraMarkers = () => {
  if (cameraMarkers.value.length > 0) {
    cameraMarkers.value.forEach((marker) => {
      if (map && marker) {
        marker.remove();
      }
    });
    cameraMarkers.value = [];
  }
};

// 处理摄像头点击
const handleCameraClick = (camera: any, marker: any) => {
  if (operationMode.value !== 'single') return;

  const index = selectedCameras.value.findIndex((c) => c.id === camera.id);

  if (index > -1) {
    // 取消选中
    selectedCameras.value.splice(index, 1);
    // 更新标记样式
    updateMarkerClass(
      marker,
      camera.status === 'ONLINE' ? 'online' : 'offline',
      false,
    );
  } else {
    // 选中
    selectedCameras.value.push(camera);
    // 设置选中样式
    updateMarkerClass(
      marker,
      camera.status === 'ONLINE' ? 'online' : 'offline',
      true,
    );
  }
};

// 更新标记样式类
const updateMarkerClass = (
  marker: any,
  statusClass: string,
  isSelected: boolean,
) => {
  if (marker && marker._icon) {
    const iconElement = marker._icon;
    // 清除所有状态类
    iconElement.className = iconElement.className.replace(
      /\b(online|offline|selected)\b/g,
      '',
    );
    // 添加新的状态类
    iconElement.classList.add(statusClass);
    if (isSelected) {
      iconElement.classList.add('selected');
    }
  }
};

// 批量框选摄像头
const handleBatchSelect = () => {
  if (operationMode.value === 'batch') {
    // 退出批量模式
    exitBatchMode();
    return;
  }

  operationMode.value = 'batch';
  selectedCameras.value = [];

  // 启动绘制模式
  startDrawing();
};

// 单个摄像头添加
const handleSingleSelect = () => {
  if (operationMode.value === 'single') {
    // 退出单个模式
    exitSingleMode();
    return;
  }

  operationMode.value = 'single';
  selectedCameras.value = [];

  // 重新渲染摄像头以启用点击事件
  renderCameras();
};

// 启动绘制
const startDrawing = () => {
  if (!mapRef.value) return;

  isDrawing.value = true;

  // 创建绘制控件
  mapRef.value.createDrawControl({
    options: {
      position: 'topleft',
      draw: {
        marker: false,
        circlemarker: false,
        circle: false,
        rectangle: false,
        polyline: false,
        polygon: {
          allowIntersection: false,
          showArea: true,
          drawError: {
            color: '#e1e100',
            message: '<strong>绘制错误!</strong> 多边形不能自相交!',
          },
          shapeOptions: {
            color: '#ff9900',
            weight: 3,
            opacity: 0.8,
            fillColor: '#ff9900',
            fillOpacity: 0.3,
          },
        },
      },
      edit: {
        remove: true,
      },
    },
    multiple: false,
  });
};

// 绘制完成处理
const onDrawCreated = (e: any) => {
  if (operationMode.value !== 'batch') return;

  drawnPolygon.value = e.layer;
  isDrawing.value = false;

  // 获取多边形坐标
  const latLngs = e.layer.getLatLngs()[0];
  const polygonCoords = latLngs.map((latLng: any) => [latLng.lat, latLng.lng]);

  // 检查哪些摄像头在多边形内
  const camerasInPolygon = cameras.value.filter((camera: any) => {
    return mapRef.value.IsPtInPoly(
      [camera.latitude, camera.longitude],
      polygonCoords,
    );
  });

  selectedCameras.value = camerasInPolygon;

  // 显示绑定弹窗
  if (selectedCameras.value.length > 0) {
    showBindModal.value = true;
  } else {
    message.warning('未选中任何摄像头');
    clearDrawing(true);
  }
};

// 清空绘制
const clearDrawing = (notClearDrawControl?: Boolean) => {
  if (drawnPolygon.value && map) {
    map.removeLayer(drawnPolygon.value);
    drawnPolygon.value = null;
  }

  if (mapRef.value && mapRef.value.clearDrawControl && !notClearDrawControl) {
    mapRef.value.clearDrawControl();
  }
};

// 退出批量模式
const exitBatchMode = () => {
  operationMode.value = 'none';
  isDrawing.value = false;
  selectedCameras.value = [];
  clearDrawing();
};

// 退出单个模式
const exitSingleMode = () => {
  operationMode.value = 'none';
  selectedCameras.value = [];
  // 重新渲染摄像头恢复原始状态
  renderCameras();
};

// 确定选择（单个模式）
const confirmSingleSelection = () => {
  if (selectedCameras.value.length === 0) {
    message.warning('请至少选中1个摄像头');
    return;
  }

  showBindModal.value = true;
};

// 绑定摄像头到场景
const handleBindCameras = async () => {
  if (!selectedSceneId.value) {
    message.warning('请选择场景');
    return;
  }

  if (selectedCameras.value.length === 0) {
    message.warning('请选择摄像头');
    return;
  }

  try {
    const cameraIds = selectedCameras.value.map((camera) => camera.id);
    await bindCameras_Api(selectedSceneId.value, cameraIds);

    message.success(`成功绑定 ${selectedCameras.value.length} 个摄像头到场景`);

    // 重置状态
    resetOperationState();

    // 刷新场景数据
    loadSceneCameraCount();
  } catch (error) {
    console.error('绑定摄像头失败', error);
    message.error('绑定摄像头失败');
  }
};

// 取消绑定
const handleCancelBind = () => {
  showBindModal.value = false;
  selectedSceneId.value = '';

  if (operationMode.value === 'batch') {
    clearDrawing(true);
  }
};

// 重置操作状态
const resetOperationState = () => {
  operationMode.value = 'none';
  isDrawing.value = false;
  selectedCameras.value = [];
  showBindModal.value = false;
  selectedSceneId.value = '';
  selectedScene.value = null;

  clearDrawing();
  renderCameras();
};

// 点击场景处理
const handleSceneClick = (scene: any) => {
  if (selectedScene.value?.sceneId === scene.sceneId) {
    // 已选中状态取消选中
    showAllCameras();
    return;
  }

  selectedScene.value = scene;

  // 如果场景有摄像头列表，渲染这些摄像头
  if (scene.cameraList && scene.cameraList.length > 0) {
    renderSceneCameras(scene.cameraList);
  } else {
    // 如果没有摄像头，显示所有摄像头
    renderCameras();
  }
};

// 显示所有摄像头
const showAllCameras = () => {
  selectedScene.value = null;
  renderCameras();
};

// 渲染场景下的摄像头
const renderSceneCameras = (sceneCameras: any[]) => {
  if (!map || !mapRef.value) return;

  // 清除现有标记
  clearCameraMarkers();

  // 准备场景摄像头标记数据
  const markerArray = sceneCameras.map((camera: any) => {
    // 检查是否已选中
    const isSelected = selectedCameras.value.some((c) => c.id === camera.id);

    return {
      ...camera,
      latitude: camera.latitude,
      longitude: camera.longitude,
      popup: camera.name || '未命名摄像头',
      markerOptions: {
        icon: {
          iconUrl: CAMERA_ICON_URL,
          iconSize: [24, 24],
          iconAnchor: [12, 12],
          // 使用CSS过滤器改变图标颜色 - 场景摄像头使用特殊样式
          className: `camera-marker ${camera.status === 'ONLINE' ? 'online' : 'offline'} ${isSelected ? 'selected' : ''}`,
        },
      },
      popupOptions: {
        maxWidth: '200px',
      },
    };
  });

  // 创建标记
  const markers = mapRef.value.createMarker(markerArray, false);

  // 为每个标记添加点击事件
  markers.forEach((marker: any, index: number) => {
    marker.on('click', () => {
      if (operationMode.value === 'single') {
        handleCameraClick(sceneCameras[index], marker);
      }
    });
  });

  cameraMarkers.value = markers;

  const bounds = markerArray.map((marker: any) => [
    marker.latitude,
    marker.longitude,
  ]);
  map.fitBounds(bounds, { padding: [20, 20] });
};

onMounted(() => {
  loadSceneCameraCount();
  getCameras();
});
</script>

<template>
  <Page>
    <!-- 操作按钮区域 -->
    <div class="btn-box">
      <a-space v-if="operationMode === 'none'">
        <a-button type="primary" @click="handleBatchSelect">
          批量框选摄像头
        </a-button>
        <a-button type="primary" @click="handleSingleSelect">
          单个摄像头添加
        </a-button>
      </a-space>

      <!-- 批量模式操作按钮 -->
      <a-space v-if="operationMode === 'batch'">
        <a-button @click="clearDrawing" :disabled="!drawnPolygon">
          清空画线
        </a-button>
        <a-button @click="exitBatchMode"> 退出 </a-button>
      </a-space>

      <!-- 单个模式操作按钮 -->
      <a-space v-if="operationMode === 'single'">
        <a-button type="primary" @click="confirmSingleSelection">
          确定
        </a-button>
        <a-button @click="exitSingleMode"> 退出 </a-button>
      </a-space>
    </div>

    <div class="division-content">
      <div class="map-box">
        <LeaFletMap
          ref="mapRef"
          @mapInited="onMapInit"
          @draw-created="onDrawCreated"
        />

        <div class="legend-box">
          <div class="legend-item">
            <div class="legend-icon online">
              <img class="img-box" :src="videoPng" alt="" />
            </div>
            <div class="legend-label">在线设备</div>
          </div>
          <div class="legend-item">
            <div class="legend-icon offline">
              <img class="img-box" :src="videoPng" alt="" />
            </div>
            <div class="legend-label">离线设备</div>
          </div>
        </div>
      </div>
      <div class="scene-box">
        <div class="scene-title">场景（{{ sceneCameraCount.length }}）</div>
        <div
          class="scene-item"
          v-for="item in sceneCameraCount"
          :key="item.id"
          :class="{ active: selectedScene?.sceneId === item.sceneId }"
          @click="handleSceneClick(item)"
        >
          <div class="scene-item-content">
            <div class="scene-item-title">{{ item.sceneName }}</div>
            <div class="scene-item-desc">{{ item.sceneDesc }}</div>
          </div>
          <div class="scene-item-right">
            <img class="img-box" :src="videoPng" alt="" />
            <span>{{ item.cameraCount }}个</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 绑定场景弹窗 -->
    <a-modal
      v-model:open="showBindModal"
      title="绑定摄像头到场景"
      @ok="handleBindCameras"
      @cancel="handleCancelBind"
      :ok-text="'确定'"
      :cancel-text="'取消'"
    >
      <div class="bind-modal-content">
        <p>
          已选中 <strong>{{ selectedCameras.length }}</strong> 个摄像头
        </p>
        <div class="scene-select">
          <label>选择场景：</label>
          <a-select
            v-model:value="selectedSceneId"
            placeholder="请选择场景"
            style="width: 200px"
          >
            <a-select-option
              v-for="scene in sceneCameraCount"
              :key="scene.sceneId"
              :value="scene.sceneId"
            >
              {{ scene.sceneName }}
            </a-select-option>
          </a-select>
        </div>
      </div>
    </a-modal>
  </Page>
</template>

<style lang="scss" scoped>
.division-content {
  display: grid;
  grid-template-columns: 80% 20%;
  gap: 10px;
  height: calc(100vh - 140px);
  .map-box {
    overflow: hidden;
    border-radius: 10px;
    position: relative;
    .legend-box {
      position: absolute;
      z-index: 9999;
      bottom: 20px;
      right: 10px;
      background: rgba(255, 255, 255, 0.8);
      padding: 10px;
      border-radius: 10px;
      .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        .legend-icon {
          width: 20px;
          height: 20px;
          margin-right: 10px;
          // border-radius: 50%;
          &.offline {
            filter: grayscale(100%); /* 灰色 */
          }
        }
      }
    }
  }

  .scene-item {
    border-radius: 10px;
  }
}

.scene-box {
  border: 1px solid #e4e4e7;
  border-radius: 10px;
  padding: 10px;
  overflow: auto;
  height: calc(100vh - 130px);
  overflow-x: hidden;
  overflow-y: auto;
  background-color: #fff;
  .scene-title {
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 18px;
  }
  .scene-item {
    border: 1px solid #e4e4e7;
    border-radius: 10px;
    padding: 10px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background-color: #f5f5f5;
      border-color: #1890ff;
    }

    &.active {
      background-color: #e6f7ff;
      border-color: #1890ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }

    .scene-item-content {
      width: 80%;
    }
    .scene-item-title {
      font-weight: bold;
      margin-bottom: 5px;
    }
    .scene-item-desc {
      color: #333;
      font-size: 12px;
    }
    .scene-item-right {
      white-space: nowrap;
      display: flex;
      .img-box {
        width: 20px;
        height: 20px;
        margin-right: 5px;
        margin-top: 1px;
        vertical-align: middle;
      }
    }
  }
}

.bind-modal-content {
  padding: 20px 0;

  p {
    margin-bottom: 20px;
    font-size: 16px;
  }

  .scene-select {
    display: flex;
    align-items: center;
    gap: 10px;

    label {
      font-weight: 500;
      min-width: 80px;
    }
  }
}

.btn-box {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 0 10px;
}

/* 摄像头标记样式 */
:deep(.leaflet-marker-icon) {
  transition: filter 0.3s ease;

  &.online {
    /* 在线摄像头默认样式 */
  }

  &.offline {
    filter: grayscale(100%); /* 灰色 */
  }

  &.selected {
    filter: hue-rotate(130deg) saturate(1.5); /* 紫色 */
  }
}
</style>
