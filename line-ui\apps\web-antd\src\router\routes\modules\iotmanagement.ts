import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    name: 'IoTManagement',
    path: '/iot-management',
    meta: {
      icon: 'mdi:chip', // 物联管理父路由图标（可根据实际需求替换mdi图标）
      order: 25, // 排序建议（参考basemanagement=20, videomonitoring=25, alarmmanagement=30, videoinspection=35）
      title: $t('page.iotmanagement.title'), // 需在国际化文件中添加对应翻译项
      authority: ['iot:menu'],
    },
    children: [
      {
        name: 'TerminalManagement',
        path: 'terminal-management',
        component: () =>
          import('#/views/iot-management/terminal-management/index.vue'), // 需确保对应视图文件存在
        meta: {
          icon: 'mdi:terminal', // 终端管理子路由图标
          order: 45,
          title: $t('page.iotmanagement.terminalTitle'), // 需在国际化文件中添加对应翻译项
          authority: ['terminal:menu'],
        },
      },
      {
        name: 'videoManage',
        path: 'video-management',
        component: () =>
          import('#/views/iot-management/video-management/index.vue'), // 需确保对应视图文件存在
        meta: {
          icon: 'mdi:video-wireless', // 视频管理子路由图标
          order: 50,
          title: $t('page.iotmanagement.videoTitle'), // 需在国际化文件中添加对应翻译项
          authority: ['terminal:menu'],
        },
      },
      {
        name: 'TerminalInforManagement',
        path: 'terminal-Infor-management',
        component: () =>
          import('#/views/iot-management/terminal-management/infor.vue'), // 需确保对应视图文件存在
        meta: {
          hideInMenu: true,
          title: $t('page.iotmanagement.terminalInfoTitle'), // 需在国际化文件中添加对应翻译项
        },
      },
    ],
  },
];

export default routes;
