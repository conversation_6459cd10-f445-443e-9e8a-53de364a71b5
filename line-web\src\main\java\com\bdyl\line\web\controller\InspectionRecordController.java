package com.bdyl.line.web.controller;

import java.io.IOException;
import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.*;

import com.bdyl.boot.JsonResult;
import com.bdyl.boot.data.query.Page;
import com.bdyl.line.web.model.request.inspection.InspectionRecordPageRequest;
import com.bdyl.line.web.model.request.inspection.InspectionRecordRequest;
import com.bdyl.line.web.model.response.inspection.InspectionRecordExportDTO;
import com.bdyl.line.web.model.response.inspection.InspectionRecordResponse;
import com.bdyl.line.web.service.InspectionRecordService;
import com.bdyl.line.web.utils.ExcelUtil;

/**
 * 巡检记录管理
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@RestController
@RequestMapping("/inspection/record")
@RequiredArgsConstructor
public class InspectionRecordController {

    /**
     * 巡检记录service
     */
    private final InspectionRecordService inspectionRecordService;

    /**
     * 提交巡检记录
     *
     * @param request 巡检记录请求对象
     * @return 巡检记录响应
     */
    @PostMapping
    public JsonResult<InspectionRecordResponse> submit(@Valid @RequestBody InspectionRecordRequest request) {
        return JsonResult.success(inspectionRecordService.submitRecord(request));
    }

    /**
     * 分页查询巡检记录
     *
     * @param request 分页请求
     * @return 分页结果
     */
    @GetMapping("/page")
    public JsonResult<Page<InspectionRecordResponse>> page(@Valid InspectionRecordPageRequest request) {
        return JsonResult.success(inspectionRecordService.pageRecords(request));
    }

    /**
     * 查询某个摄像头的巡检记录
     *
     * @param taskCameraId 任务摄像头ID
     * @return 巡检记录
     */
    @GetMapping("/detail/{taskCameraId}")
    public JsonResult<InspectionRecordResponse> getTaskRecordByTaskCameraId(@PathVariable Long taskCameraId) {
        return JsonResult.success(inspectionRecordService.getTaskRecordByTaskCameraId(taskCameraId));
    }

    /**
     * 导出
     *
     * @param request 筛选条件
     * @param response 响应
     */
    @GetMapping("/export")
    public void export(InspectionRecordPageRequest request, HttpServletResponse response) throws IOException {

        List<InspectionRecordExportDTO> export = inspectionRecordService.export(request);
        ExcelUtil.export(response, export, InspectionRecordExportDTO.class, "巡检任务记录");
    }

}
