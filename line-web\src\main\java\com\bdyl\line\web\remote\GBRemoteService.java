package com.bdyl.line.web.remote;

import java.util.Collections;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.UriComponentsBuilder;

import com.bdyl.boot.JsonResult;
import com.bdyl.boot.data.query.Page;
import com.bdyl.line.web.config.LineProperties;
import com.bdyl.line.web.model.request.camera.*;
import com.bdyl.line.web.model.response.camera.GBPlaybackInfoDTO;
import com.bdyl.line.web.model.response.camera.GBProxyResponse;
import com.bdyl.line.web.model.response.camera.GBStreamResponse;

/**
 * 国标视频平台远程调用客户端
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GBRemoteService {

    /**
     * 远程调用
     */
    private final RestTemplate restTemplate;
    /**
     * 系统配置
     */
    private final LineProperties lineProperties;

    /**
     * 构建UAA远程调用通用请求头
     *
     * @return HttpHeaders
     */
    private HttpHeaders buildUaaHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        // 从当前请求上下文获取token和tenant
        RequestAttributes attrs = RequestContextHolder.getRequestAttributes();
        if (attrs instanceof ServletRequestAttributes servletAttrs) {
            HttpServletRequest req = servletAttrs.getRequest();
            String token = req.getHeader("Authorization");
            String tenant = req.getHeader("X-Tenant-Realm");
            if (StringUtils.hasText(token)) {
                if (token.startsWith("Bearer ")) {
                    token = token.substring(7);
                }
                headers.setBearerAuth(token);
            }
            if (StringUtils.hasText(tenant)) {
                headers.set("X-Tenant-Realm", tenant);
            }
        }
        return headers;
    }

    /**
     * 获取摄像头实时播放地址
     *
     * @param request 播放请求参数
     * @return 媒体流响应信息
     */
    public GBStreamResponse getPlayUrl(GBPlayRequest request) {
        String url = lineProperties.getGbUrl() + "/media/play";

        HttpHeaders headers = buildUaaHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<GBPlayRequest> entity = new HttpEntity<>(request, headers);

        try {
            log.info("Requesting GB video play URL for device: {}, channel: {}", request.getDeviceId(),
                request.getChannelId());

            ResponseEntity<JsonResult<GBStreamResponse>> response = restTemplate.exchange(url, HttpMethod.POST, entity,
                new ParameterizedTypeReference<JsonResult<GBStreamResponse>>() {});

            JsonResult<GBStreamResponse> result = response.getBody();
            if (result != null && "success".equalsIgnoreCase(result.getCode())) {
                GBStreamResponse data = result.getData();
                log.info("Successfully got play URL for device: {}, sessionId: {}", request.getDeviceId(),
                    data.getSessionId());
                return data;
            } else {
                log.warn("Failed to get play URL for device: {}, error: {}", request.getDeviceId(),
                    result != null ? result.getMsg() : "Unknown error");
            }
            return null;

        } catch (Exception e) {
            log.error("Error getting play URL for device: {}", request.getDeviceId(), e);
            return GBStreamResponse.failed("REMOTE_ERROR", "Failed to get play URL: " + e.getMessage());
        }
    }

    /**
     * 分页查询设备列表
     *
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param search 查询条件
     * @return 设备列表
     */
    public List<GBCameraDTO> queryDevices(int page, int size, GBCameraQueryDTO search) {
        String url = lineProperties.getGbUrl() + "/device";

        // 构建查询参数
        UriComponentsBuilder builder =
            UriComponentsBuilder.fromHttpUrl(url).queryParam("page", page).queryParam("size", size);
        // 添加可选的查询条件
        if (search != null) {
            if (search.getDeviceCode() != null) {
                builder.queryParam("deviceCode", search.getDeviceCode());
            }
            if (search.getName() != null) {
                builder.queryParam("name", search.getName());
            }
            if (search.getManufacturer() != null) {
                builder.queryParam("manufacturer", search.getManufacturer());
            }
            if (search.getModel() != null) {
                builder.queryParam("model", search.getModel());
            }
            if (search.getOnline() != null) {
                builder.queryParam("online", search.getOnline());
            }
            if (search.getParentId() != null) {
                builder.queryParam("parentId", search.getParentId());
            }
        }

        HttpHeaders headers = buildUaaHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        try {
            log.info("Querying devices with page: {}, size: {}, search: {}", page, size, search);

            HttpEntity<Void> entity = new HttpEntity<>(headers);
            var response = restTemplate.exchange(builder.toUriString(), HttpMethod.GET, entity,
                new ParameterizedTypeReference<JsonResult<Page<GBCameraDTO>>>() {});
            JsonResult<Page<GBCameraDTO>> body = response.getBody();
            if (body != null && "success".equalsIgnoreCase(body.getCode())) {
                log.info("Successfully queried {} devices", body.getData() != null ? body.getData().getTotal() : 0);
                return body.getData().getData();
            }
            return null;
        } catch (Exception e) {
            log.error("Error querying devices", e);
            throw new RuntimeException("Failed to query devices: " + e.getMessage(), e);
        }
    }

    /**
     * 创建拉流代理
     *
     * @param request 拉流代理请求参数
     * @return 创建结果
     */
    public Boolean createProxy(GBProxyRequest request) {
        String url = lineProperties.getGbUrl() + "/device/proxy";

        HttpHeaders headers = buildUaaHeaders();
        HttpEntity<GBProxyRequest> entity = new HttpEntity<>(request, headers);

        try {
            log.info("Creating proxy for device: {}, stream: {}", request.getDeviceId(), request.getStream());

            ResponseEntity<JsonResult<Object>> response = restTemplate.exchange(url, HttpMethod.POST, entity,
                new ParameterizedTypeReference<JsonResult<Object>>() {});

            JsonResult<Object> result = response.getBody();
            if (result != null && "success".equalsIgnoreCase(result.getCode())) {
                log.info("Successfully created proxy for device: {}, stream: {}", request.getDeviceId(),
                    request.getStream());
                return true;
            } else {
                log.warn("Failed to create proxy for device: {}, error: {}", request.getDeviceId(),
                    result != null ? result.getMsg() : "Unknown error");
                return false;
            }

        } catch (Exception e) {
            log.error("Error creating proxy for device: {}", request.getDeviceId(), e);
            return false;
        }
    }

    /**
     * 删除拉流代理
     *
     * @param deviceCode 设备ID
     * @return 删除结果
     */
    public Boolean deleteProxy(String deviceCode) {
        String url = lineProperties.getGbUrl() + "/device/proxy/" + deviceCode;

        HttpHeaders headers = buildUaaHeaders();
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            log.info("Deleting proxy for device: {}", deviceCode);

            ResponseEntity<JsonResult<Object>> response = restTemplate.exchange(url, HttpMethod.DELETE, entity,
                new ParameterizedTypeReference<JsonResult<Object>>() {});

            JsonResult<Object> result = response.getBody();
            if (result != null && "success".equalsIgnoreCase(result.getCode())) {
                log.info("Successfully deleted proxy for device: {}", deviceCode);
                return true;
            } else {
                log.warn("Failed to delete proxy for device: {}, error: {}", deviceCode,
                    result != null ? result.getMsg() : "Unknown error");
                return false;
            }

        } catch (Exception e) {
            log.error("Error deleting proxy for device: {}", deviceCode, e);
            return false;
        }
    }

    /**
     * 根据摄像头编码获取拉流代理
     *
     * @param deviceCode 摄像头编码
     * @return 代理详情
     */
    public GBProxyResponse getProxyDetail(String deviceCode) {
        String url = lineProperties.getGbUrl() + "/device/proxy/" + deviceCode;

        // 构建查询参数
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);

        HttpHeaders headers = buildUaaHeaders();
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {

            ResponseEntity<JsonResult<GBProxyResponse>> response = restTemplate.exchange(builder.toUriString(),
                HttpMethod.GET, entity, new ParameterizedTypeReference<JsonResult<GBProxyResponse>>() {});

            JsonResult<GBProxyResponse> jsonResult = response.getBody();
            if (jsonResult != null && "success".equalsIgnoreCase(jsonResult.getCode())) {
                log.info("Successfully queried {} proxy", jsonResult.getData());
                return jsonResult.getData();
            } else {
                log.warn("Failed to query proxies, error: {}",
                    jsonResult != null ? jsonResult.getMsg() : "Unknown error");
                return null;
            }

        } catch (Exception e) {
            log.error("Error querying proxies", e);
            return null;
        }
    }

    /**
     * 分页查询拉流代理
     *
     * @param queryRequest 查询参数
     * @return 代理列表
     */
    public JsonResult<Page<GBProxyResponse>> getProxyDetail(GBProxyQueryRequest queryRequest) {
        String url = lineProperties.getGbUrl() + "/device/proxy/page";

        // 构建查询参数
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        if (queryRequest.getPage() != null) {
            builder.queryParam("page", queryRequest.getPage());
        }
        if (queryRequest.getSize() != null) {
            builder.queryParam("size", queryRequest.getSize());
        }
        if (queryRequest.getDeviceId() != null) {
            builder.queryParam("deviceId", queryRequest.getDeviceId());
        }
        if (queryRequest.getStream() != null) {
            builder.queryParam("stream", queryRequest.getStream());
        }

        HttpHeaders headers = buildUaaHeaders();
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            log.info("Querying proxies with parameters: {}", queryRequest);

            ResponseEntity<JsonResult<Page<GBProxyResponse>>> response = restTemplate.exchange(builder.toUriString(),
                HttpMethod.GET, entity, new ParameterizedTypeReference<JsonResult<Page<GBProxyResponse>>>() {});

            JsonResult<Page<GBProxyResponse>> result = response.getBody();
            if (result != null && "success".equalsIgnoreCase(result.getCode())) {
                log.info("Successfully queried {} proxies", result.getData() != null ? result.getData().getTotal() : 0);
                return result;
            } else {
                log.warn("Failed to query proxies, error: {}", result != null ? result.getMsg() : "Unknown error");
                return result;
            }

        } catch (Exception e) {
            log.error("Error querying proxies", e);
            return JsonResult.failed("REMOTE_ERROR", "Failed to query proxies: " + e.getMessage());
        }
    }

    /**
     * 获取m3u8文件 - 视频回放
     *
     * @param request 视频回放请求参数
     * @return m3u8文件信息
     */
    public GBPlaybackInfoDTO getPlaybackM3u8(GBPlaybackRequest request) {
        String url = lineProperties.getGbUrl() + "/device/video/playback";

        HttpHeaders headers = buildUaaHeaders();
        HttpEntity<GBPlaybackRequest> entity = new HttpEntity<>(request, headers);

        try {
            log.info("Getting playback m3u8 for device: {}, stream: {}, time: {} - {}", request.getDeviceId(),
                request.getStreamId(), request.getStartTime(), request.getEndTime());

            ResponseEntity<JsonResult<GBPlaybackInfoDTO>> response = restTemplate.exchange(url, HttpMethod.POST, entity,
                new ParameterizedTypeReference<JsonResult<GBPlaybackInfoDTO>>() {});

            JsonResult<GBPlaybackInfoDTO> result = response.getBody();
            if (result != null && "success".equalsIgnoreCase(result.getCode())) {
                log.info("Successfully got playback m3u8 for device: {}, stream: {}", request.getDeviceId(),
                    request.getStreamId());
                // 这里返回的是接口全路径,需要取出最后的m3u8文件名
                GBPlaybackInfoDTO resultData = result.getData();
                String filename = resultData.getM3u8Url();
                if (filename != null) {
                    filename = filename.substring(filename.lastIndexOf("/") + 1);
                }
                resultData.setM3u8Url(filename);
                return resultData;
            } else {
                log.warn("Failed to get playback m3u8 for device: {}, error: {}", request.getDeviceId(),
                    result != null ? result.getMsg() : "Unknown error");
                return null;
            }

        } catch (Exception e) {
            log.error("Error getting playback m3u8 for device: {}", request.getDeviceId(), e);
            return null;
        }
    }

    /**
     * 视频回放 - 获取m3u8文件内容
     *
     * @param fileName m3u8文件名
     * @return m3u8文件资源
     */
    public ResponseEntity<Resource> getPlaybackVideo(String fileName) {
        String url = lineProperties.getGbUrl() + "/playback/m3u8/" + fileName;

        HttpHeaders headers = buildUaaHeaders();
        // 移除Content-Type设置，让服务端决定响应类型
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            log.info("Getting playback video for file: {}", fileName);

            ResponseEntity<Resource> response = restTemplate.exchange(url, HttpMethod.GET, entity, Resource.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Successfully got playback video for file: {}", fileName);
                return response;
            } else {
                log.warn("Failed to get playback video for file: {}, status: {}", fileName, response.getStatusCode());
                return ResponseEntity.status(response.getStatusCode()).build();
            }

        } catch (Exception e) {
            log.error("Error getting playback video for file: {}", fileName, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取设备某个时间段的视频录像ts
     *
     * @param request 请求参数
     * @return ts列表
     */
    public List<String> getPlaybackTs(GBPlayTSRequest request) {
        String url = lineProperties.getGbUrl() + "/device/proxy/ts";

        HttpHeaders headers = buildUaaHeaders();
        HttpEntity<GBPlayTSRequest> entity = new HttpEntity<>(request, headers);

        log.info("Getting playback ts for device: {}, time: {}", request.getDeviceCode(), request.getDateTime());

        ResponseEntity<JsonResult<List<String>>> response = restTemplate.exchange(url, HttpMethod.POST, entity,
            new ParameterizedTypeReference<JsonResult<List<String>>>() {});
        JsonResult<List<String>> result = response.getBody();
        if (result != null && "success".equalsIgnoreCase(result.getCode())) {
            log.info("Successfully got playback ts for device: {}, time: {}", request.getDeviceCode(),
                request.getDateTime());
            return result.getData();
        }
        log.warn("Failed to get playback ts for device: {}, error: {}", request.getDeviceCode(),
            result != null ? result.getMsg() : "Unknown error");
        return Collections.emptyList();

    }
}
