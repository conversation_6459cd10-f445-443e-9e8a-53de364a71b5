<script lang="ts" setup>
import { ref } from 'vue';

import { message } from 'ant-design-vue';
const mediaUrl = import.meta.env.VITE_GLOB_LOAD_ERROR_URL;

// 弹窗相关
const visible = ref(false);
const currentRecord = ref<any>(null);

// 打开弹窗
const openModal = (record: any) => {
  currentRecord.value = record;
  visible.value = true;
};

// 关闭弹窗
const closeModal = () => {
  visible.value = false;
  currentRecord.value = null;
};

// 图片加载错误处理
const handleImageError = () => {
  message.error('图片加载失败');
};

// 暴露方法给父组件
defineExpose({
  openModal,
  closeModal,
});
</script>

<template>
  <a-modal
    v-model:open="visible"
    title="查看截图"
    width="800px"
    :footer="null"
    @cancel="closeModal"
  >
    <div v-if="currentRecord" class="screenshot-modal">
      <!-- 截图展示 -->
      <div class="image-section">
        <div class="image-container">
          <a-image
            v-if="currentRecord.inspectionImage"
            :src="mediaUrl + currentRecord.inspectionImage"
            :alt="`${currentRecord.cameraName}巡检截图`"
            :preview="true"
            class="screenshot-image"
            @error="handleImageError"
          />
          <div v-else class="no-image">
            <a-empty description="暂无截图" />
          </div>
        </div>
      </div>
    </div>

    <div v-else class="loading-container">
      <a-spin size="large" />
    </div>
  </a-modal>
</template>

<style lang="scss" scoped>
.screenshot-modal {
  .info-section {
    margin-bottom: 20px;
  }

  .image-section {
    .image-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      .image-title {
        font-weight: 500;
        font-size: 14px;
        color: #333;
      }
    }

    .image-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300px;
      background: #fafafa;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;

      .screenshot-image {
        max-width: 100%;
        max-height: 400px;
        border-radius: 4px;
      }

      .no-image {
        width: 100%;
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
