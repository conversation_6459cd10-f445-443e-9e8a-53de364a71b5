package com.bdyl.line.web.model.request.user;

import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 用户导入DTO
 *
 * <AUTHOR>
 */
@Data
public class UserImportDTO {
    /**
     * 组织名称
     */
    @ExcelProperty("组织名称")
    private String organName;
    /**
     * 组织类型
     */
    @ExcelProperty("组织类型")
    private String organType;
    /**
     * 地区编码
     */
    @ExcelIgnore
    private String regionCode;
    /**
     * 用户姓名
     */
    @ExcelProperty("用户姓名")
    private String name;
    /**
     * 用户手机号
     */
    @ExcelProperty("手机号")
    private String phone;
    /**
     * 用户账号
     */
    @ExcelProperty("账号")
    private String account;

    /**
     * 用户角色
     */
    @ExcelIgnore
    private List<Long> roleIds;
    /**
     * 用户角色
     */
    @ExcelProperty("角色列表")
    private String roleNames;

    /**
     * 用户所属部门
     */
    @ExcelIgnore()
    private Long departmentId;
    /**
     * 用户所属部门
     */
    @ExcelProperty("部门名称")
    private String departmentName;

    /**
     * 失败原因
     */
    @ExcelProperty("失败原因")
    private String failReason;
}
