package com.bdyl.line.web.timer;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.bdyl.boot.tenant.Tenant;
import com.bdyl.boot.tenant.TenantContext;
import com.bdyl.boot.tenant.TenantContextHolder;
import com.bdyl.line.common.constant.enums.DeviceBoxStatusEnum;
import com.bdyl.line.common.constant.enums.IotAlertStatusEnum;
import com.bdyl.line.common.constant.enums.IotAlertTypeEnum;
import com.bdyl.line.web.entity.SolarPanelEntity;
import com.bdyl.line.web.entity.TerminalEntity;
import com.bdyl.line.web.model.request.camera.CameraSynRequest;
import com.bdyl.line.web.model.request.iotalert.IotAlertRequest;
import com.bdyl.line.web.model.response.organ.OrganResponse;
import com.bdyl.line.web.model.response.region.RegionResponse;
import com.bdyl.line.web.model.response.terminal.DataProperty;
import com.bdyl.line.web.model.response.terminal.PlatformTelemetryHistory;
import com.bdyl.line.web.model.response.terminal.TerminalResponse;
import com.bdyl.line.web.remote.PlatformRemoteService;
import com.bdyl.line.web.remote.model.IotDeviceType;
import com.bdyl.line.web.service.*;
import com.bdyl.line.web.utils.LineUserContext;

/**
 * 去中台查询报警
 */
@Slf4j
@Component
public class IotAlertTimer {

    /**
     * 组织service
     */
    @Autowired
    private OrganService organService;

    /**
     * 区划service
     */
    @Autowired
    private RegionService regionService;

    /**
     * 报警service
     */
    @Autowired
    private IotAlertService iotAlertService;
    /**
     * 终端service
     */
    @Autowired
    private TerminalService terminalService;
    /**
     * 平台远程调用类
     */
    @Autowired
    private PlatformRemoteService platformRemoteService;
    /**
     * 电池板service
     */
    @Autowired
    private SolarPanelService solarPanelService;

    /**
     * 查询物联报警
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void synIotAlertTimer() {
        try {
            log.info("======开始同步物联报警======");
            LineUserContext.setSkipPermission(true);
            TenantContextHolder.setTenantContext(new TenantContext(new Tenant("1", "1"), false));
            synIotAlert();
        } catch (Exception e) {
            log.error("=====从平台同步物联报警异常====", e);
        } finally {
            LineUserContext.clear();
            TenantContextHolder.resetTenantContext();
        }

    }

    private void synIotAlert() {

        // 查询系统中所有终端
        List<TerminalEntity> terminalEntityList = terminalService.list();
        if (CollectionUtils.isEmpty(terminalEntityList)) {
            return;
        }
        // 查询每个终端的实时数据
        terminalEntityList.forEach(terminalEntity -> {
            String terminalCode = terminalEntity.getCode();
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusMinutes(1);

            // 通过终端找到绑定的电池板设备
            List<CameraSynRequest> cameraSynRequests =
                platformRemoteService.listCamerasByTerminalCode(terminalCode, IotDeviceType.BATTERY);
            if (CollectionUtils.isEmpty(cameraSynRequests)) {
                return;
            }
            CameraSynRequest first = cameraSynRequests.getFirst();
            List<PlatformTelemetryHistory> platformTelemetryHistoryList =
                platformRemoteService.listTelemetryByCode(first.getDeviceCode(), startTime, endTime);

            if (CollectionUtils.isEmpty(platformTelemetryHistoryList)) {
                return;
            }

            // 找到最新的传感器数据(createTime最新的)
            PlatformTelemetryHistory latestTelemetryDate = platformTelemetryHistoryList.stream()
                .max(Comparator.comparing(PlatformTelemetryHistory::getCreateTime)).get();
            // 返回的数据里面没有终端编码
            latestTelemetryDate.setTerminalCode(terminalCode);

            // 判断是否需要报警
            judgeIotAlert(latestTelemetryDate);
            // 保存传感器数据
            saveTelemetryDate(latestTelemetryDate, terminalEntity);

        });
    }

    private void saveTelemetryDate(PlatformTelemetryHistory latestTelemetryDate, TerminalEntity terminalEntity) {
        List<DataProperty> data = latestTelemetryDate.getData();
        SolarPanelEntity solarPanelEntity = new SolarPanelEntity();
        solarPanelEntity.setTerminalId(terminalEntity.getId());
        solarPanelEntity.setOrganId(terminalEntity.getOrganId());

        TerminalEntity changedTerminalEntity = new TerminalEntity();
        BeanUtils.copyProperties(terminalEntity, changedTerminalEntity);
        data.forEach(property -> {
            // 解析pv组件
            parsePV(property, solarPanelEntity);
            // 解析直流负载
            parseDC(property, solarPanelEntity);
            // 解析蓄电池状态
            parseBattery(property, solarPanelEntity);
            // 更新终端状态
            updateTerminalStatus(changedTerminalEntity, property);

        });
        solarPanelService.save(solarPanelEntity);
        // 更新终端状态
        if (!changedTerminalEntity.equals(terminalEntity)) {
            terminalService.updateById(changedTerminalEntity);
        }
    }

    private void updateTerminalStatus(TerminalEntity terminalEntity, DataProperty property) {

        Object value = property.getValue();
        if (value instanceof Integer multiValue) {
            if (property.getName().equals("remainingCapacity")) {
                terminalEntity.setBatteryLevel(multiValue);

            } else if (property.getName().equals("boxStatus")) {
                if (multiValue == 1) {
                    terminalEntity.setBoxStatus(DeviceBoxStatusEnum.NORMAL.getValue());
                } else {
                    terminalEntity.setBoxStatus(DeviceBoxStatusEnum.TILT.getValue());
                }

            }
        }
    }

    private void parseBattery(DataProperty property, SolarPanelEntity solarPanelEntity) {
        Object value = property.getValue();

        if (property.getName().equals("temperature")) {
            if (value instanceof Double temperature) {
                solarPanelEntity.setBatteryTemperature(temperature);
            }
        } else if (property.getName().equals("voltage")) {
            if (value instanceof Double batteryVoltage) {
                solarPanelEntity.setBatteryVoltage(batteryVoltage);
            }
        } else if (property.getName().equals("current")) {
            if (value instanceof Double batteryCurrent) {
                solarPanelEntity.setBatteryCurrent(batteryCurrent);
            }
        } else if (property.getName().equals("remainingCapacity")) {
            if (value instanceof Double batteryLevel) {
                solarPanelEntity.setBatteryLevel(batteryLevel);
            }
        }
    }

    private void parsePV(DataProperty property, SolarPanelEntity solarPanelEntity) {

        Object value = property.getValue();
        if (property.getName().equals("arrayVoltage")) {
            if (value instanceof Double pvVoltage) {
                solarPanelEntity.setPvVoltage(pvVoltage);
            }
        } else if (property.getName().equals("arrayCurrent")) {
            if (value instanceof Double pvCurrent) {
                solarPanelEntity.setPvCurrent(pvCurrent);
            }
        } else if (property.getName().equals("generatingPower")) {
            if (value instanceof Double pvPower) {
                solarPanelEntity.setPvPower(pvPower);
            }
        }

    }

    private void parseDC(DataProperty property, SolarPanelEntity solarPanelEntity) {

        Object value = property.getValue();
        if (property.getName().equals("loadVoltage")) {
            if (value instanceof Double dcLoadVoltage) {
                solarPanelEntity.setDcLoadVoltage(dcLoadVoltage);
            }
        } else if (property.getName().equals("loadCurrent")) {
            if (value instanceof Double dcLoadCurrent) {
                solarPanelEntity.setDcLoadCurrent(dcLoadCurrent);
            }
        } else if (property.getName().equals("loadPower")) {
            if (value instanceof Double dcLoadPower) {
                solarPanelEntity.setDcLoadPower(dcLoadPower);
            }
        } else if (property.getName().equals("dailyPowerConsumption")) {
            if (value instanceof Double dcLoadEnergyToday) {
                solarPanelEntity.setDcLoadEnergyToday(dcLoadEnergyToday);
            }
        }
    }

    private void judgeIotAlert(PlatformTelemetryHistory latestTelemetryDate) {
        if (latestTelemetryDate == null) {
            return;
        }

        String terminalCode = latestTelemetryDate.getTerminalCode();
        // 保存物联报警
        List<DataProperty> data = latestTelemetryDate.getData();
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        data.forEach(property -> {
            if (property.getName().equals("remainingCapacity")) {

                Object value = property.getValue();
                if (value instanceof Double batteryLevel) {
                    if (batteryLevel < 90) {
                        String content = "蓄电池电量低于90%";
                        buildIotAlert(terminalCode, IotAlertTypeEnum.LOW_BATTERY, content);
                    }
                }
            } else if (property.getName().equals("boxStatus")) {
                Object value = property.getValue();
                if (value instanceof String boxStatus) {
                    if (!boxStatus.equals("NORMAL")) {
                        String content = "设备箱状态异常";
                        buildIotAlert(terminalCode, IotAlertTypeEnum.DEVICE_BOX_ABNORMAL, content);
                    }
                }
            }
        });
    }

    /**
     * 构建物联报警
     *
     * @param terminalCode 终端编码
     * @param type 报警类型
     * @param content 报警内容
     */
    private void buildIotAlert(String terminalCode, IotAlertTypeEnum type, String content) {

        TerminalResponse terminalResponse = terminalService.getByCode(terminalCode);
        if (terminalResponse == null) {
            log.error("未找到终端:{}", terminalCode);
            return;
        }

        // 获取组织
        Long organId = terminalResponse.getOrganId();
        OrganResponse organResponse = organService.getOrganById(organId);
        if (organResponse == null) {
            log.error("未找到组织:{}", organId);
            return;
        }
        // 获取区域名称
        RegionResponse regionResponse = regionService.getByCode(organResponse.getRegionCode());
        if (regionResponse == null) {
            log.error("未找到区域:{}", organResponse.getRegionCode());
            return;
        }
        IotAlertRequest iotAlertRequest = new IotAlertRequest();
        iotAlertRequest.setType(type.getValue());
        iotAlertRequest.setContent(content);
        iotAlertRequest.setTerminalName(terminalResponse.getName());
        iotAlertRequest.setRegion(regionResponse.getName());
        iotAlertRequest.setAddress(terminalResponse.getLocation());
        iotAlertRequest.setOrganId(organId);
        iotAlertRequest.setStatus(IotAlertStatusEnum.NOT_RECOVERED.getValue());

        iotAlertService.create(iotAlertRequest);
    }

}
