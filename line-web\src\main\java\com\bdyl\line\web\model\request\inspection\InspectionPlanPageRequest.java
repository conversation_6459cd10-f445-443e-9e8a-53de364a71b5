package com.bdyl.line.web.model.request.inspection;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.bdyl.boot.data.query.PageRequest;

/**
 * 巡检计划分页查询请求对象
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InspectionPlanPageRequest extends PageRequest {
    /**
     * 计划名称
     */
    private String name;

    /**
     * 巡检周期类型 {@link com.bdyl.line.common.constant.enums.InspectionCycleEnum}
     */
    private String cycleType;

    /**
     * 启用状态 {@link com.bdyl.line.common.constant.enums.StatusEnum}
     */
    private String status;

    /**
     * 开始检索时间(创建时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    /**
     * 结束检索时间(创建时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

}
