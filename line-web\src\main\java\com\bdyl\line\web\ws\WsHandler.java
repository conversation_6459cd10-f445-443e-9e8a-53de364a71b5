package com.bdyl.line.web.ws;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;
import org.springframework.web.util.UriTemplate;

/**
 * WebSocket消息处理器 处理WebSocket连接的建立、关闭和消息发送 支持按设备编码管理WebSocket会话
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class WsHandler extends TextWebSocketHandler {

    /**
     * WebSocket执行器
     */
    private final Executor webSocketExecutor;

    /**
     * 存储设备编码到WebSocket会话集合的映射 使用ConcurrentHashMap确保线程安全
     */
    private final Map<String, Set<WebSocketSession>> deviceSessions = new ConcurrentHashMap<>();

    /**
     * 构造函数注入Executor，使用@Lazy避免循环依赖
     *
     * @param webSocketExecutor Executor
     */
    public WsHandler(@Lazy @Qualifier("webSocketExecutor") Executor webSocketExecutor) {
        this.webSocketExecutor = webSocketExecutor;
    }

    /**
     * 处理WebSocket连接建立 提取设备编码并存储会话信息
     *
     * @param session WebSocket会话
     */
    @Override
    public void afterConnectionEstablished(@NonNull WebSocketSession session) throws Exception {
        try {
            String deviceCode = extractDeviceCode(session);
            if (deviceCode != null) {
                deviceSessions.computeIfAbsent(deviceCode, k -> ConcurrentHashMap.newKeySet()).add(session);
                log.info("设备 {} 的WebSocket连接已建立，当前连接数: {}", deviceCode, deviceSessions.get(deviceCode).size());
            }
        } catch (Exception e) {
            log.error("WebSocket连接建立失败: {}", e.getMessage(), e);
            if (session.isOpen()) {
                session.close(CloseStatus.SERVER_ERROR);
            }
        }
    }

    @Override
    protected void handleTextMessage(@NonNull WebSocketSession session, @NonNull TextMessage message) throws Exception {
        webSocketExecutor.execute(() -> {
            try {
                String payload = message.getPayload();
                log.info("server 接收到消息: {}", payload);
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage("server 发送给的消息 " + payload + "，发送时间:" + LocalDateTime.now()));
                }
            } catch (Exception e) {
                log.error("处理WebSocket消息失败: {}", e.getMessage(), e);
                try {
                    if (session.isOpen()) {
                        session.close(CloseStatus.SERVER_ERROR);
                    }
                } catch (IOException ex) {
                    log.error("关闭WebSocket会话失败: {}", ex.getMessage(), ex);
                }
            }
        });
    }

    @Override
    protected void handleBinaryMessage(@NonNull WebSocketSession session, @NonNull BinaryMessage message) {
        webSocketExecutor.execute(() -> {
            try {
                log.info("处理二进制消息");
                // 处理二进制消息的逻辑
            } catch (Exception e) {
                log.error("处理二进制消息失败: {}", e.getMessage(), e);
                try {
                    if (session.isOpen()) {
                        session.close(CloseStatus.SERVER_ERROR);
                    }
                } catch (IOException ex) {
                    log.error("关闭WebSocket会话失败: {}", ex.getMessage(), ex);
                }
            }
        });
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        log.error("WebSocket传输错误: {}", exception.getMessage(), exception);
        try {
            if (session.isOpen()) {
                session.close(CloseStatus.SERVER_ERROR);
            }
        } catch (IOException e) {
            log.error("关闭WebSocket会话失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理WebSocket连接关闭 移除设备会话信息
     *
     * @param session WebSocket会话
     * @param status 关闭状态
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        try {
            String deviceCode = extractDeviceCode(session);
            if (deviceCode != null) {
                Set<WebSocketSession> sessions = deviceSessions.get(deviceCode);
                if (sessions != null) {
                    sessions.remove(session);
                    if (sessions.isEmpty()) {
                        deviceSessions.remove(deviceCode);
                    }
                    log.info("设备 {} 的WebSocket连接已关闭，状态: {}，剩余连接数: {}", deviceCode, status, sessions.size());
                }
            }
        } catch (Exception e) {
            log.error("处理WebSocket连接关闭失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从WebSocket会话URI中提取设备编码
     *
     * @param session WebSocket会话
     * @return 设备编码，如果提取失败则返回null
     */
    private String extractDeviceCode(WebSocketSession session) {
        String path = Objects.requireNonNull(session.getUri()).getPath();
        UriTemplate template = new UriTemplate("/ws/line/{deviceCode}");
        Map<String, String> parameters = template.match(path);
        return parameters.get("deviceCode");
    }

    /**
     * 向指定设备的所有会话发送消息 如果设备连接不存在或已关闭，则记录错误日志
     *
     * @param deviceCode 设备编码
     * @param message 要发送的消息内容
     */
    public void sendMessageToDevice(String deviceCode, String message) {
        Set<WebSocketSession> sessions = deviceSessions.get(deviceCode);
        if (sessions != null && !sessions.isEmpty()) {
            sessions.forEach(session -> {
                if (session.isOpen()) {
                    webSocketExecutor.execute(() -> {
                        try {
                            session.sendMessage(new TextMessage(message));
                        } catch (IOException e) {
                            log.error("向设备 {} 的会话发送消息失败: {}", deviceCode, e.getMessage(), e);
                            try {
                                if (session.isOpen()) {
                                    session.close(CloseStatus.SERVER_ERROR);
                                }
                            } catch (IOException ex) {
                                log.error("关闭WebSocket会话失败: {}", ex.getMessage(), ex);
                            }
                        }
                    });
                }
            });
        } else {
            log.debug("设备 {} 没有活跃的WebSocket连接", deviceCode);
        }
    }
}
