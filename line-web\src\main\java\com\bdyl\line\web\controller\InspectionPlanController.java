package com.bdyl.line.web.controller;

import java.io.IOException;
import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.*;

import com.bdyl.boot.JsonResult;
import com.bdyl.boot.data.query.Page;
import com.bdyl.line.web.model.request.inspection.InspectionPlanPageRequest;
import com.bdyl.line.web.model.request.inspection.InspectionPlanRequest;
import com.bdyl.line.web.model.response.inspection.InspectionPlanExportDTO;
import com.bdyl.line.web.model.response.inspection.InspectionPlanResponse;
import com.bdyl.line.web.service.InspectionPlanService;
import com.bdyl.line.web.utils.CurrentUserBizHelper;
import com.bdyl.line.web.utils.ExcelUtil;

/**
 * 巡检计划管理
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@RestController
@RequestMapping("/inspection/plan")
@RequiredArgsConstructor
public class InspectionPlanController {

    /**
     * 巡检计划service
     */
    private final InspectionPlanService inspectionPlanService;

    /**
     * 创建巡检计划
     *
     * @param request 巡检计划请求对象
     * @return 是否创建成功
     */
    @PostMapping
    public JsonResult<Boolean> create(@Valid @RequestBody InspectionPlanRequest request) {
        Long currentOrganId = CurrentUserBizHelper.getCurrentOrganId();
        return JsonResult.success(inspectionPlanService.createPlan(request, currentOrganId));
    }

    /**
     * 更新巡检计划
     *
     * @param id 计划ID
     * @param request 巡检计划请求对象
     * @return 是否更新成功
     */
    @PutMapping("/{id}")
    public JsonResult<Boolean> update(@PathVariable Long id, @Valid @RequestBody InspectionPlanRequest request) {
        return JsonResult.success(inspectionPlanService.updatePlan(id, request));
    }

    /**
     * 删除巡检计划
     *
     * @param id 计划ID
     * @return 是否删除成功
     */
    @DeleteMapping("/{id}")
    public JsonResult<Boolean> delete(@PathVariable Long id) {
        return JsonResult.success(inspectionPlanService.deletePlan(id));
    }

    /**
     * 获取巡检计划详情
     *
     * @param id 计划ID
     * @return 计划详情
     */
    @GetMapping("/{id}")
    public JsonResult<InspectionPlanResponse> get(@PathVariable Long id) {
        return JsonResult.success(inspectionPlanService.getPlanDetail(id));
    }

    /**
     * 分页查询巡检计划
     *
     * @param request 分页请求
     * @return 分页结果
     */
    @GetMapping("/page")
    public JsonResult<Page<InspectionPlanResponse>> page(@Valid InspectionPlanPageRequest request) {
        return JsonResult.success(inspectionPlanService.pagePlans(request));
    }

    /**
     * 启用/禁用巡检计划
     *
     * @param id 计划ID
     * @param status 状态
     * @return 是否成功
     */
    @PutMapping("/{id}/status")
    public JsonResult<Boolean> updateStatus(@PathVariable Long id, @RequestParam String status) {
        return JsonResult.success(inspectionPlanService.updatePlanStatus(id, status));
    }

    /**
     * 导出
     *
     * @param request 筛选条件
     * @param response 响应
     */
    @GetMapping("/export")
    public void export(InspectionPlanPageRequest request, HttpServletResponse response) throws IOException {

        List<InspectionPlanExportDTO> export = inspectionPlanService.export(request);
        ExcelUtil.export(response, export, InspectionPlanExportDTO.class, "巡检计划");
    }
}
