package com.bdyl.line.web.model.response.camera;

import java.util.List;

import lombok.Data;

/**
 * 组织树节点响应对象，包含终端数量。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class OrganTreeWithCameraCountResponse {
    /**
     * 组织ID
     */
    private Long id;
    /**
     * 组织名称
     */
    private String name;
    /**
     * 子组织
     */
    private List<OrganTreeWithCameraCountResponse> children;
    /**
     * 终端数量
     */
    private Integer cameraCount;
}
