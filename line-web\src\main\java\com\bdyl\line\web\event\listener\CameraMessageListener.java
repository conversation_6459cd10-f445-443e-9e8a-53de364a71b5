package com.bdyl.line.web.event.listener;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import com.bdyl.boot.tenant.Tenant;
import com.bdyl.boot.tenant.TenantContext;
import com.bdyl.boot.tenant.TenantContextHolder;
import com.bdyl.boot.util.JsonUtil;
import com.bdyl.line.common.constant.enums.BizAlertTypeEnum;
import com.bdyl.line.web.entity.SceneCameraEntity;
import com.bdyl.line.web.entity.TerminalEntity;
import com.bdyl.line.web.event.CameraMessageEvent;
import com.bdyl.line.web.mapper.SceneCameraMapper;
import com.bdyl.line.web.model.dto.PointDTO;
import com.bdyl.line.web.model.request.bizalert.BizAlertRequest;
import com.bdyl.line.web.model.response.bizalert.PlatformBizAlertSynDTO;
import com.bdyl.line.web.model.response.camera.CameraModelConfigResponse;
import com.bdyl.line.web.model.response.camera.CameraResponse;
import com.bdyl.line.web.model.response.organ.OrganResponse;
import com.bdyl.line.web.model.response.region.RegionResponse;
import com.bdyl.line.web.model.response.terminal.DataProperty;
import com.bdyl.line.web.remote.PlatformRemoteService;
import com.bdyl.line.web.service.*;
import com.bdyl.line.web.utils.GeometricUtil;
import com.bdyl.line.web.utils.LineUserContext;
import com.bdyl.line.web.ws.WebsocketMessage;
import com.bdyl.line.web.ws.WsHandler;

/**
 * 摄像头消息处理服务
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Service
public class CameraMessageListener {

    /**
     * 组织service
     */
    @Autowired
    private OrganService organService;
    /**
     * 区划service
     */
    @Autowired
    private RegionService regionService;
    /**
     * 终端service
     */
    @Autowired
    private TerminalService terminalService;
    /**
     * 平台远程调用类
     */
    @Autowired
    private PlatformRemoteService platformRemoteService;
    /**
     * 业务报警service
     */
    @Autowired
    private BizAlertService bizAlertService;
    /**
     * 摄像头service
     */
    @Autowired
    private CameraService cameraService;
    /**
     * 场景和摄像头绑定关系
     */
    @Autowired
    private SceneCameraMapper sceneCameraMapper;

    /**
     * 场景和摄像头绑定关系
     */
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * websocket处理类
     */
    @Autowired
    private WsHandler wsHandler;
    /**
     * 摄像头模型配置service
     */
    @Autowired
    private CameraModelConfigService cameraModelConfigService;

    /**
     * 处理报警事件
     *
     * @param event 事件
     */
    @EventListener
    public void handleCameraMessage(CameraMessageEvent event) {

        log.info("收到摄像头消息：{}", event.getMessage());

        try {
            log.info("======开始中台的算法报警======");
            LineUserContext.setSkipPermission(true);
            TenantContextHolder.setTenantContext(new TenantContext(new Tenant("1", "1"), false));
            newSynBizAlert(event);
        } catch (Exception e) {
            log.error("=====处理中台的算法报警出现异常====", e);
        } finally {
            LineUserContext.clear();
            TenantContextHolder.resetTenantContext();
        }
    }

    /**
     * 直接收到单条信息去判断是否生成报警
     */
    private void newSynBizAlert(CameraMessageEvent event) {

        String cameraCode = event.getCameraCode();
        CameraResponse cameraResponse = cameraService.getByCode(cameraCode);
        if (cameraResponse == null) {
            log.error("未找到摄像头:{}", cameraCode);
            return;
        }

        WebsocketMessage message = event.getMessage();
        if (message == null || CollectionUtils.isEmpty(message.getMessage())) {
            log.error("收到设备{}的算法识别数据为null", cameraCode);
            return;
        }
        // 获取算法数据数据
        List<DataProperty> dataProperties = message.getMessage();

        ModelResult modelResult = buildModelResult(cameraCode, dataProperties);
        if (modelResult.point() == null || StringUtils.isEmpty(modelResult.modelName())
            || modelResult.score() == null) {
            log.error("未找到算法识别数据");
            return;
        }
        //
        // boolean match = judgeInAreaAndReachScore(cameraResponse, modelResult);
        // if (match) {
        // }
        buildBizAlert(cameraResponse, modelResult.modelCode());

    }

    private boolean judgeInAreaAndReachScore(CameraResponse cameraResponse, ModelResult modelResult) {

        GeometricUtil.Point point = modelResult.point();
        Long cameraId = cameraResponse.getId();
        CameraModelConfigResponse configDetail = cameraModelConfigService.getDetail(cameraId, modelResult.modelCode);

        if (modelResult.score() < configDetail.getConfidence().doubleValue()) {
            return false;
        }
        List<PointDTO> regionPoints = configDetail.getRegionPoints();
        // 如果没有画区域，默认检测全景
        if (CollectionUtils.isEmpty(regionPoints)) {
            return true;
        }
        // 判断是否在区域内
        List<GeometricUtil.Point> region =
            regionPoints.stream().map(item -> new GeometricUtil.Point(item.getX(), item.getY())).toList();
        return GeometricUtil.isPointInPolygon(point, region);
    }

    private ModelResult buildModelResult(String cameraCode, List<DataProperty> dataProperties) {
        GeometricUtil.Point point = null;
        String modelName = null;
        String modeCode = null;
        Double score = null;
        for (DataProperty dataProperty : dataProperties) {
            if ("boundingBoxes".equals(dataProperty.getName())) {
                String valueString = (String) dataProperty.getValue();
                List<Double> boundingBox = null;
                try {
                    boundingBox = objectMapper.readValue(valueString, new TypeReference<List<Double>>() {});
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
                // 计算中心点坐标
                double centerX = (boundingBox.get(0) + boundingBox.get(2)) / 2.0;
                double centerY = (boundingBox.get(1) + boundingBox.get(3)) / 2.0;
                point = new GeometricUtil.Point(centerX, centerY);
            } else if ("modelName".equals(dataProperty.getName())) {
                modelName = (String) dataProperty.getValue();
            } else if ("modelCode".equals(dataProperty.getName())) {
                modeCode = (String) dataProperty.getValue();
            } else if ("score".equals(dataProperty.getName())) {
                String value = (String) dataProperty.getValue();
                score = Double.parseDouble(value);
            }
        }
        // 发送给前端
        WebsocketMessage websocketMessage = new WebsocketMessage();
        websocketMessage.setMessage(dataProperties);
        websocketMessage.setTimestamp(System.currentTimeMillis());
        websocketMessage.setMessageType(WebsocketMessage.MessageType.RECOGNIZE_BOX.name());
        wsHandler.sendMessageToDevice(cameraCode, JsonUtil.toJson(websocketMessage));
        return new ModelResult(point, modelName, modeCode, score);
    }

    private record ModelResult(GeometricUtil.Point point, String modelName, String modelCode, Double score) {
    }

    private void buildBizAlert(CameraResponse cameraResponse, String modelCode) {
        String cameraCode = cameraResponse.getCode();

        // 获取组织
        Long organId = cameraResponse.getOrganId();
        OrganResponse organResponse = organService.getOrganById(organId);
        if (organResponse == null) {
            log.error("未找到组织:{}", organId);
            return;
        }
        // 获取区域名称
        RegionResponse regionResponse = regionService.getByCode(organResponse.getRegionCode());
        if (regionResponse == null) {
            log.error("未找到区域:{}", organResponse.getRegionCode());
            return;
        }
        // 获取该摄像头绑定的场景
        LambdaQueryWrapper<SceneCameraEntity> wrapper =
            new LambdaQueryWrapper<SceneCameraEntity>().eq(SceneCameraEntity::getCameraId, cameraResponse.getId());
        List<SceneCameraEntity> sceneCameraEntities = sceneCameraMapper.selectList(wrapper);

        BizAlertRequest alertRequest = new BizAlertRequest();
        BizAlertTypeEnum bizAlertTypeEnum = BizAlertTypeEnum.fromValue(modelCode);
        alertRequest.setType(bizAlertTypeEnum.getValue());
        alertRequest.setCameraCode(cameraCode);
        alertRequest.setOrganId(organId);
        alertRequest.setRegion(regionResponse.getName());
        alertRequest.setAddress(cameraResponse.getLocation());
        alertRequest.setSceneIds(sceneCameraEntities.stream().map(SceneCameraEntity::getSceneId).toList());
        bizAlertService.create(alertRequest);
    }

    /**
     * 去中台查询是否有新报警
     */
    private void oldSynBizAlert() {
        // 查询系统中所有的终端
        List<TerminalEntity> terminalEntityList = terminalService.list();
        if (CollectionUtils.isEmpty(terminalEntityList)) {
            return;
        }
        // 查询所有终端下的摄像头的报警情况
        terminalEntityList.forEach(terminalEntity -> {

            Long terminalId = terminalEntity.getId();
            List<CameraResponse> cameraList = cameraService.listByTerminalId(terminalId);
            if (CollectionUtils.isEmpty(cameraList)) {
                return;
            }

            // 查询每个摄像头的报警记录
            cameraList.forEach(cameraResponse -> {
                String code = cameraResponse.getCode();
                LocalDateTime endTime = LocalDateTime.now();
                LocalDateTime startTime = endTime.minusMinutes(1);
                List<PlatformBizAlertSynDTO> platformBizAlertSynDTOS =
                    platformRemoteService.listAlarmByCameraCode(code, startTime, endTime);

                platformBizAlertSynDTOS.forEach(platformBizAlertSynDTO -> {
                    buildBizAlert(platformBizAlertSynDTO, cameraResponse);
                });
            });

        });
    }

    /**
     * 构建业务报警
     *
     * @param alertSynRequest 报警请求
     */
    private void buildBizAlert(PlatformBizAlertSynDTO alertSynRequest, CameraResponse cameraResponse) {
        String cameraCode = alertSynRequest.getDeviceCode();

        // 获取组织
        Long organId = cameraResponse.getOrganId();
        OrganResponse organResponse = organService.getOrganById(organId);
        if (organResponse == null) {
            log.error("未找到组织:{}", organId);
            return;
        }
        // 获取区域名称
        RegionResponse regionResponse = regionService.getByCode(organResponse.getRegionCode());
        if (regionResponse == null) {
            log.error("未找到区域:{}", organResponse.getRegionCode());
            return;
        }
        // 获取该摄像头绑定的场景
        LambdaQueryWrapper<SceneCameraEntity> wrapper =
            new LambdaQueryWrapper<SceneCameraEntity>().eq(SceneCameraEntity::getCameraId, cameraResponse.getId());
        List<SceneCameraEntity> sceneCameraEntities = sceneCameraMapper.selectList(wrapper);

        BizAlertRequest alertRequest = new BizAlertRequest();
        BizAlertTypeEnum bizAlertTypeEnum = BizAlertTypeEnum.fromValue(alertSynRequest.getRuleName());
        alertRequest.setType(bizAlertTypeEnum.getValue());
        alertRequest.setCameraCode(cameraCode);
        alertRequest.setOrganId(organId);
        alertRequest.setRegion(regionResponse.getName());
        alertRequest.setAddress(cameraResponse.getLocation());
        alertRequest.setSceneIds(sceneCameraEntities.stream().map(SceneCameraEntity::getSceneId).toList());
        bizAlertService.create(alertRequest);

    }
}
