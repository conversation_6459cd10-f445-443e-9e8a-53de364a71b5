package com.bdyl.line.web.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.bdyl.line.web.entity.SolarPanelEntity;
import com.bdyl.line.web.mapper.SolarPanelMapper;
import com.bdyl.line.web.model.request.solarpanel.SolarPanelPageRequest;
import com.bdyl.line.web.model.request.solarpanel.SolarPanelRequest;
import com.bdyl.line.web.model.response.solarpanel.SolarPanelHourStatItem;
import com.bdyl.line.web.model.response.solarpanel.SolarPanelResponse;
import com.bdyl.line.web.service.SolarPanelService;

/**
 * 太阳能电池板服务实现类。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
@RequiredArgsConstructor
public class SolarPanelServiceImpl extends ServiceImpl<SolarPanelMapper, SolarPanelEntity>
    implements SolarPanelService {
    /**
     * 电池板mapper
     */
    private final SolarPanelMapper solarPanelMapper;

    @Override
    public boolean create(SolarPanelRequest request, Long currentOrganId) {
        SolarPanelEntity entity = new SolarPanelEntity();
        BeanUtils.copyProperties(request, entity);
        if (request.getOrganId() == null) {
            entity.setOrganId(currentOrganId);
        }
        return solarPanelMapper.insert(entity) > 0;
    }

    @Override
    public com.bdyl.boot.data.query.Page<SolarPanelResponse> page(SolarPanelPageRequest request) {
        LambdaQueryWrapper<SolarPanelEntity> wrapper = new LambdaQueryWrapper<>();
        if (request.getTerminalId() != null) {
            wrapper.eq(SolarPanelEntity::getTerminalId, request.getTerminalId());
        }
        Page<SolarPanelEntity> mybatisPage = new Page<>(request.getPage(), request.getSize());
        Page<SolarPanelEntity> resultPage = solarPanelMapper.selectPage(mybatisPage, wrapper);
        List<SolarPanelResponse> records = resultPage.getRecords().stream().map(entity -> {
            SolarPanelResponse response = new SolarPanelResponse();
            BeanUtils.copyProperties(entity, response);
            response.setId(entity.getId());
            return response;
        }).collect(Collectors.toList());
        return com.bdyl.boot.data.query.Page.of(request.getPage(), request.getSize(), resultPage.getTotal(),
            Collections.emptyList(), records);
    }

    @Override
    public SolarPanelResponse getLatestByTerminalId(Long terminalId) {
        SolarPanelEntity entity = solarPanelMapper.selectLatestByTerminalId(terminalId);
        if (entity == null) {
            return null;
        }
        SolarPanelResponse response = new SolarPanelResponse();
        BeanUtils.copyProperties(entity, response);
        response.setId(entity.getId());
        return response;
    }

    @Override
    public List<SolarPanelHourStatItem> stat24Hour(Long terminalId, LocalDate day) {
        LocalDateTime start = day.atStartOfDay();
        LocalDateTime end = day.atTime(LocalTime.MAX);
        return solarPanelMapper.selectHourStatByTerminalIdAndDay(terminalId, start, end);
    }

}
