package com.bdyl.line.web.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 场景摄像头实体类，对应场景摄像头表。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@TableName(value = "t_scene_camera")
public class SceneCameraEntity extends BaseEntity {

    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 组织ID
     */
    private Long organId;
    /**
     * 场景ID
     */
    private Long sceneId;
    /**
     * 摄像头ID
     */
    private Long cameraId;
}
