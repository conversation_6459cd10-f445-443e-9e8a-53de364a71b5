<script lang="ts" setup>
import { reactive, ref } from 'vue';

import { message } from 'ant-design-vue';

import { getCameraList_Api, deleteCamera_Api } from '#/api/core';
import tableComp from '#/components/TableComp/table.vue';
import { useDictStore } from '#/store/dict';
import { formatDate } from '@vben/utils';

const dictStore = useDictStore();

const emit = defineEmits(['success']);

const open = ref<boolean>(false);
const modalTitle = ref<string>('');
const loading = ref(false);
const currentOrgan = ref<any>(null);

// 查询条件
const queryParam = reactive<any>({
  name: undefined, // 设备名称
  code: undefined, // 设备编码
  location: undefined, // 位置
  page: 1,
  size: 10,
});

// 表格数据源
const tableData = reactive<any>({
  data: {},
});

// 表格相关配置
const columns = [
  { title: '设备名称', dataIndex: 'name', width: '150px' },
  { title: '设备编码', dataIndex: 'code', width: '150px' },
  { title: '位置', dataIndex: 'location', width: '200px' },
  { title: '状态', dataIndex: 'status', width: '100px' },
  { title: '描述', dataIndex: 'remarks', width: '200px' },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: '180px',
    customRender: ({ record }: { record: any }) => {
      return formatDate(record.createTime, 'YYYY-MM-DD HH:mm:ss');
    },
  },
  // { title: '操作', dataIndex: 'operation', width: '100px' },
];

// 终端状态选项
// const terminalStatusOptions = dictStore.getDictOptions('CameraStatusEnum');

// 加载设备列表
const loadDeviceList = async () => {
  loading.value = true;
  try {
    const params = {
      ...queryParam,
      organId: currentOrgan.value?.id,
    };
    const res = await getCameraList_Api(params);

    // 更新表格数据源
    const temp = {
      data: res.data,
      page: res.page,
      size: res.size,
      total: res.total,
    };
    tableData.data = temp;
  } catch (error) {
    console.error('获取设备列表失败', error);
    message.error('获取设备列表失败');
  } finally {
    loading.value = false;
  }
};

const success = (data: any) => {
  queryParam.page = data.pi;
  queryParam.size = data.ps;
  loadDeviceList();
};

// 查询
const searchTable = () => {
  queryParam.page = 1;
  loadDeviceList();
};

// 重置
const resetTable = () => {
  queryParam.name = undefined;
  queryParam.code = undefined;
  queryParam.location = undefined;
  queryParam.page = 1;
  queryParam.size = 10;
  loadDeviceList();
};

// 移除设备
// const handleRemove = async (record: any) => {
//   try {
//     await deleteCamera_Api(record.id);
//     message.success('移除成功');
//     loadDeviceList();
//     emit('success');
//   } catch (error) {
//     message.error('移除失败');
//     console.error('移除设备失败', error);
//   }
// };

// 打开弹窗
const openModal = async (organ: any) => {
  open.value = true;
  currentOrgan.value = organ;
  modalTitle.value = `设备管理 - ${organ.platformName || organ.name}`;

  // 重置查询条件
  queryParam.name = undefined;
  queryParam.code = undefined;
  queryParam.location = undefined;
  queryParam.page = 1;
  queryParam.size = 10;

  // 加载设备列表
  await loadDeviceList();
};

// 关闭弹窗
const closeModal = () => {
  open.value = false;
  currentOrgan.value = null;
};

// 暴露组件方法
defineExpose({
  openModal,
});
</script>

<template>
  <a-modal
    v-model:open="open"
    :title="modalTitle"
    :mask-closable="false"
    width="1200px"
    :footer="null"
    @cancel="closeModal"
  >
    <!-- 搜索区域 -->
    <a-card class="table_header_search mb-4" size="small">
      <a-row :gutter="16">
        <a-col :span="6">
          <label>设备名称：</label>
          <div class="table_header_wrp_cont">
            <a-input
              v-model:value="queryParam.name"
              placeholder="请输入设备名称"
              allow-clear
            />
          </div>
        </a-col>
        <a-col :span="6">
          <label>设备编码：</label>
          <div class="table_header_wrp_cont">
            <a-input
              v-model:value="queryParam.code"
              placeholder="请输入设备编码"
              allow-clear
            />
          </div>
        </a-col>
        <a-col :span="6">
          <label>位置：</label>
          <div class="table_header_wrp_cont">
            <a-input
              v-model:value="queryParam.location"
              placeholder="请输入位置"
              allow-clear
            />
          </div>
        </a-col>
        <a-col :span="6">
          <a-space>
            <a-button type="primary" class="searchBtn" @click="searchTable">
              查询
            </a-button>
            <a-button class="refBtn" @click="resetTable">重置</a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 表格区域 -->
    <a-card size="small">
      <!-- <div class="table_action_btn_wrp">
        <a-button class="addBtn" type="primary"> 新建 </a-button>
        <a-button class="addBtn"> 批量导入 </a-button>
      </div> -->

      <table-Comp
        :columns="columns"
        :data-source="tableData.data"
        :loading="loading"
        :scroll="{ x: 1000 }"
        @is-loading-fuc="(e) => (loading = e)"
        @success="success"
      >
        <!-- 状态列 -->
        <template #status="{ record }">
          <a-tag :color="record.status === 'ONLINE' ? 'success' : 'error'">
            {{ dictStore.getDictLable('CameraStatusEnum', record.status) }}
          </a-tag>
        </template>

        <!-- 描述列 -->
        <template #remarks="{ record }">
          <a-tooltip
            v-if="record.remarks && record.remarks.length > 20"
            :title="record.remarks"
          >
            <span>{{ record.remarks.substring(0, 20) }}...</span>
          </a-tooltip>
          <span v-else>{{ record.remarks || '-' }}</span>
        </template>

        <!-- 操作列 -->
        <!-- <template #operation="{ record }">
          <a-popconfirm
            title="确定要移除该设备吗？"
            @confirm="handleRemove(record)"
          >
            <a-button type="link" danger size="small">移除</a-button>
          </a-popconfirm>
        </template> -->
      </table-Comp>
    </a-card>
  </a-modal>
</template>

<style lang="scss" scoped>
.table_header_search {
  .table_header_wrp_cont {
    margin-top: 4px;
  }

  label {
    font-weight: 500;
    color: #333;
  }
}
</style>
