{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/KeyCode.js"], "sourcesContent": ["/**\n * @ignore\n * some key-codes definition and utils from closure-library\n * <AUTHOR>\n */\nconst KeyCode = {\n  /**\n   * MAC_ENTER\n   */\n  MAC_ENTER: 3,\n  /**\n   * BACKSPACE\n   */\n  BACKSPACE: 8,\n  /**\n   * TAB\n   */\n  TAB: 9,\n  /**\n   * NUMLOCK on FF/Safari Mac\n   */\n  NUM_CENTER: 12,\n  /**\n   * ENTER\n   */\n  ENTER: 13,\n  /**\n   * SHIFT\n   */\n  SHIFT: 16,\n  /**\n   * CTRL\n   */\n  CTRL: 17,\n  /**\n   * ALT\n   */\n  ALT: 18,\n  /**\n   * PAUSE\n   */\n  PAUSE: 19,\n  /**\n   * CAPS_LOCK\n   */\n  CAPS_LOCK: 20,\n  /**\n   * ESC\n   */\n  ESC: 27,\n  /**\n   * SPACE\n   */\n  SPACE: 32,\n  /**\n   * PAGE_UP\n   */\n  PAGE_UP: 33,\n  /**\n   * PAGE_DOWN\n   */\n  PAGE_DOWN: 34,\n  /**\n   * END\n   */\n  END: 35,\n  /**\n   * HOME\n   */\n  HOME: 36,\n  /**\n   * LEFT\n   */\n  LEFT: 37,\n  /**\n   * UP\n   */\n  UP: 38,\n  /**\n   * RIGHT\n   */\n  RIGHT: 39,\n  /**\n   * DOWN\n   */\n  DOWN: 40,\n  /**\n   * PRINT_SCREEN\n   */\n  PRINT_SCREEN: 44,\n  /**\n   * INSERT\n   */\n  INSERT: 45,\n  /**\n   * DELETE\n   */\n  DELETE: 46,\n  /**\n   * ZERO\n   */\n  ZERO: 48,\n  /**\n   * ONE\n   */\n  ONE: 49,\n  /**\n   * TWO\n   */\n  TWO: 50,\n  /**\n   * THREE\n   */\n  THREE: 51,\n  /**\n   * FOUR\n   */\n  FOUR: 52,\n  /**\n   * FIVE\n   */\n  FIVE: 53,\n  /**\n   * SIX\n   */\n  SIX: 54,\n  /**\n   * SEVEN\n   */\n  SEVEN: 55,\n  /**\n   * EIGHT\n   */\n  EIGHT: 56,\n  /**\n   * NINE\n   */\n  NINE: 57,\n  /**\n   * QUESTION_MARK\n   */\n  QUESTION_MARK: 63,\n  /**\n   * A\n   */\n  A: 65,\n  /**\n   * B\n   */\n  B: 66,\n  /**\n   * C\n   */\n  C: 67,\n  /**\n   * D\n   */\n  D: 68,\n  /**\n   * E\n   */\n  E: 69,\n  /**\n   * F\n   */\n  F: 70,\n  /**\n   * G\n   */\n  G: 71,\n  /**\n   * H\n   */\n  H: 72,\n  /**\n   * I\n   */\n  I: 73,\n  /**\n   * J\n   */\n  J: 74,\n  /**\n   * K\n   */\n  K: 75,\n  /**\n   * L\n   */\n  L: 76,\n  /**\n   * M\n   */\n  M: 77,\n  /**\n   * N\n   */\n  N: 78,\n  /**\n   * O\n   */\n  O: 79,\n  /**\n   * P\n   */\n  P: 80,\n  /**\n   * Q\n   */\n  Q: 81,\n  /**\n   * R\n   */\n  R: 82,\n  /**\n   * S\n   */\n  S: 83,\n  /**\n   * T\n   */\n  T: 84,\n  /**\n   * U\n   */\n  U: 85,\n  /**\n   * V\n   */\n  V: 86,\n  /**\n   * W\n   */\n  W: 87,\n  /**\n   * X\n   */\n  X: 88,\n  /**\n   * Y\n   */\n  Y: 89,\n  /**\n   * Z\n   */\n  Z: 90,\n  /**\n   * META\n   */\n  META: 91,\n  /**\n   * WIN_KEY_RIGHT\n   */\n  WIN_KEY_RIGHT: 92,\n  /**\n   * CONTEXT_MENU\n   */\n  CONTEXT_MENU: 93,\n  /**\n   * NUM_ZERO\n   */\n  NUM_ZERO: 96,\n  /**\n   * NUM_ONE\n   */\n  NUM_ONE: 97,\n  /**\n   * NUM_TWO\n   */\n  NUM_TWO: 98,\n  /**\n   * NUM_THREE\n   */\n  NUM_THREE: 99,\n  /**\n   * NUM_FOUR\n   */\n  NUM_FOUR: 100,\n  /**\n   * NUM_FIVE\n   */\n  NUM_FIVE: 101,\n  /**\n   * NUM_SIX\n   */\n  NUM_SIX: 102,\n  /**\n   * NUM_SEVEN\n   */\n  NUM_SEVEN: 103,\n  /**\n   * NUM_EIGHT\n   */\n  NUM_EIGHT: 104,\n  /**\n   * NUM_NINE\n   */\n  NUM_NINE: 105,\n  /**\n   * NUM_MULTIPLY\n   */\n  NUM_MULTIPLY: 106,\n  /**\n   * NUM_PLUS\n   */\n  NUM_PLUS: 107,\n  /**\n   * NUM_MINUS\n   */\n  NUM_MINUS: 109,\n  /**\n   * NUM_PERIOD\n   */\n  NUM_PERIOD: 110,\n  /**\n   * NUM_DIVISION\n   */\n  NUM_DIVISION: 111,\n  /**\n   * F1\n   */\n  F1: 112,\n  /**\n   * F2\n   */\n  F2: 113,\n  /**\n   * F3\n   */\n  F3: 114,\n  /**\n   * F4\n   */\n  F4: 115,\n  /**\n   * F5\n   */\n  F5: 116,\n  /**\n   * F6\n   */\n  F6: 117,\n  /**\n   * F7\n   */\n  F7: 118,\n  /**\n   * F8\n   */\n  F8: 119,\n  /**\n   * F9\n   */\n  F9: 120,\n  /**\n   * F10\n   */\n  F10: 121,\n  /**\n   * F11\n   */\n  F11: 122,\n  /**\n   * F12\n   */\n  F12: 123,\n  /**\n   * NUMLOCK\n   */\n  NUMLOCK: 144,\n  /**\n   * SEMICOLON\n   */\n  SEMICOLON: 186,\n  /**\n   * DASH\n   */\n  DASH: 189,\n  /**\n   * EQUALS\n   */\n  EQUALS: 187,\n  /**\n   * COMMA\n   */\n  COMMA: 188,\n  /**\n   * PERIOD\n   */\n  PERIOD: 190,\n  /**\n   * SLASH\n   */\n  SLASH: 191,\n  /**\n   * APOSTROPHE\n   */\n  APOSTROPHE: 192,\n  /**\n   * SINGLE_QUOTE\n   */\n  SINGLE_QUOTE: 222,\n  /**\n   * OPEN_SQUARE_BRACKET\n   */\n  OPEN_SQUARE_BRACKET: 219,\n  /**\n   * BACKSLASH\n   */\n  BACKSLASH: 220,\n  /**\n   * CLOSE_SQUARE_BRACKET\n   */\n  CLOSE_SQUARE_BRACKET: 221,\n  /**\n   * WIN_KEY\n   */\n  WIN_KEY: 224,\n  /**\n   * MAC_FF_META\n   */\n  MAC_FF_META: 224,\n  /**\n   * WIN_IME\n   */\n  WIN_IME: 229,\n  // ======================== Function ========================\n  /**\n   * whether text and modified key is entered at the same time.\n   */\n  isTextModifyingKeyEvent: function isTextModifyingKeyEvent(e) {\n    const {\n      keyCode\n    } = e;\n    if (e.altKey && !e.ctrlKey || e.metaKey ||\n    // Function keys don't generate text\n    keyCode >= KeyCode.F1 && keyCode <= KeyCode.F12) {\n      return false;\n    }\n    // The following keys are quite harmless, even in combination with\n    // CTRL, ALT or SHIFT.\n    switch (keyCode) {\n      case KeyCode.ALT:\n      case KeyCode.CAPS_LOCK:\n      case KeyCode.CONTEXT_MENU:\n      case KeyCode.CTRL:\n      case KeyCode.DOWN:\n      case KeyCode.END:\n      case KeyCode.ESC:\n      case KeyCode.HOME:\n      case KeyCode.INSERT:\n      case KeyCode.LEFT:\n      case KeyCode.MAC_FF_META:\n      case KeyCode.META:\n      case KeyCode.NUMLOCK:\n      case KeyCode.NUM_CENTER:\n      case KeyCode.PAGE_DOWN:\n      case KeyCode.PAGE_UP:\n      case KeyCode.PAUSE:\n      case KeyCode.PRINT_SCREEN:\n      case KeyCode.RIGHT:\n      case KeyCode.SHIFT:\n      case KeyCode.UP:\n      case KeyCode.WIN_KEY:\n      case KeyCode.WIN_KEY_RIGHT:\n        return false;\n      default:\n        return true;\n    }\n  },\n  /**\n   * whether character is entered.\n   */\n  isCharacterKey: function isCharacterKey(keyCode) {\n    if (keyCode >= KeyCode.ZERO && keyCode <= KeyCode.NINE) {\n      return true;\n    }\n    if (keyCode >= KeyCode.NUM_ZERO && keyCode <= KeyCode.NUM_MULTIPLY) {\n      return true;\n    }\n    if (keyCode >= KeyCode.A && keyCode <= KeyCode.Z) {\n      return true;\n    }\n    // Safari sends zero key code for non-latin characters.\n    if (window.navigator.userAgent.indexOf('WebKit') !== -1 && keyCode === 0) {\n      return true;\n    }\n    switch (keyCode) {\n      case KeyCode.SPACE:\n      case KeyCode.QUESTION_MARK:\n      case KeyCode.NUM_PLUS:\n      case KeyCode.NUM_MINUS:\n      case KeyCode.NUM_PERIOD:\n      case KeyCode.NUM_DIVISION:\n      case KeyCode.SEMICOLON:\n      case KeyCode.DASH:\n      case KeyCode.EQUALS:\n      case KeyCode.COMMA:\n      case KeyCode.PERIOD:\n      case KeyCode.SLASH:\n      case KeyCode.APOSTROPHE:\n      case KeyCode.SINGLE_QUOTE:\n      case KeyCode.OPEN_SQUARE_BRACKET:\n      case KeyCode.BACKSLASH:\n      case KeyCode.CLOSE_SQUARE_BRACKET:\n        return true;\n      default:\n        return false;\n    }\n  }\n};\nexport default KeyCode;"], "mappings": ";AAKA,IAAM,UAAU;AAAA;AAAA;AAAA;AAAA,EAId,WAAW;AAAA;AAAA;AAAA;AAAA,EAIX,WAAW;AAAA;AAAA;AAAA;AAAA,EAIX,KAAK;AAAA;AAAA;AAAA;AAAA,EAIL,YAAY;AAAA;AAAA;AAAA;AAAA,EAIZ,OAAO;AAAA;AAAA;AAAA;AAAA,EAIP,OAAO;AAAA;AAAA;AAAA;AAAA,EAIP,MAAM;AAAA;AAAA;AAAA;AAAA,EAIN,KAAK;AAAA;AAAA;AAAA;AAAA,EAIL,OAAO;AAAA;AAAA;AAAA;AAAA,EAIP,WAAW;AAAA;AAAA;AAAA;AAAA,EAIX,KAAK;AAAA;AAAA;AAAA;AAAA,EAIL,OAAO;AAAA;AAAA;AAAA;AAAA,EAIP,SAAS;AAAA;AAAA;AAAA;AAAA,EAIT,WAAW;AAAA;AAAA;AAAA;AAAA,EAIX,KAAK;AAAA;AAAA;AAAA;AAAA,EAIL,MAAM;AAAA;AAAA;AAAA;AAAA,EAIN,MAAM;AAAA;AAAA;AAAA;AAAA,EAIN,IAAI;AAAA;AAAA;AAAA;AAAA,EAIJ,OAAO;AAAA;AAAA;AAAA;AAAA,EAIP,MAAM;AAAA;AAAA;AAAA;AAAA,EAIN,cAAc;AAAA;AAAA;AAAA;AAAA,EAId,QAAQ;AAAA;AAAA;AAAA;AAAA,EAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,EAIR,MAAM;AAAA;AAAA;AAAA;AAAA,EAIN,KAAK;AAAA;AAAA;AAAA;AAAA,EAIL,KAAK;AAAA;AAAA;AAAA;AAAA,EAIL,OAAO;AAAA;AAAA;AAAA;AAAA,EAIP,MAAM;AAAA;AAAA;AAAA;AAAA,EAIN,MAAM;AAAA;AAAA;AAAA;AAAA,EAIN,KAAK;AAAA;AAAA;AAAA;AAAA,EAIL,OAAO;AAAA;AAAA;AAAA;AAAA,EAIP,OAAO;AAAA;AAAA;AAAA;AAAA,EAIP,MAAM;AAAA;AAAA;AAAA;AAAA,EAIN,eAAe;AAAA;AAAA;AAAA;AAAA,EAIf,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,MAAM;AAAA;AAAA;AAAA;AAAA,EAIN,eAAe;AAAA;AAAA;AAAA;AAAA,EAIf,cAAc;AAAA;AAAA;AAAA;AAAA,EAId,UAAU;AAAA;AAAA;AAAA;AAAA,EAIV,SAAS;AAAA;AAAA;AAAA;AAAA,EAIT,SAAS;AAAA;AAAA;AAAA;AAAA,EAIT,WAAW;AAAA;AAAA;AAAA;AAAA,EAIX,UAAU;AAAA;AAAA;AAAA;AAAA,EAIV,UAAU;AAAA;AAAA;AAAA;AAAA,EAIV,SAAS;AAAA;AAAA;AAAA;AAAA,EAIT,WAAW;AAAA;AAAA;AAAA;AAAA,EAIX,WAAW;AAAA;AAAA;AAAA;AAAA,EAIX,UAAU;AAAA;AAAA;AAAA;AAAA,EAIV,cAAc;AAAA;AAAA;AAAA;AAAA,EAId,UAAU;AAAA;AAAA;AAAA;AAAA,EAIV,WAAW;AAAA;AAAA;AAAA;AAAA,EAIX,YAAY;AAAA;AAAA;AAAA;AAAA,EAIZ,cAAc;AAAA;AAAA;AAAA;AAAA,EAId,IAAI;AAAA;AAAA;AAAA;AAAA,EAIJ,IAAI;AAAA;AAAA;AAAA;AAAA,EAIJ,IAAI;AAAA;AAAA;AAAA;AAAA,EAIJ,IAAI;AAAA;AAAA;AAAA;AAAA,EAIJ,IAAI;AAAA;AAAA;AAAA;AAAA,EAIJ,IAAI;AAAA;AAAA;AAAA;AAAA,EAIJ,IAAI;AAAA;AAAA;AAAA;AAAA,EAIJ,IAAI;AAAA;AAAA;AAAA;AAAA,EAIJ,IAAI;AAAA;AAAA;AAAA;AAAA,EAIJ,KAAK;AAAA;AAAA;AAAA;AAAA,EAIL,KAAK;AAAA;AAAA;AAAA;AAAA,EAIL,KAAK;AAAA;AAAA;AAAA;AAAA,EAIL,SAAS;AAAA;AAAA;AAAA;AAAA,EAIT,WAAW;AAAA;AAAA;AAAA;AAAA,EAIX,MAAM;AAAA;AAAA;AAAA;AAAA,EAIN,QAAQ;AAAA;AAAA;AAAA;AAAA,EAIR,OAAO;AAAA;AAAA;AAAA;AAAA,EAIP,QAAQ;AAAA;AAAA;AAAA;AAAA,EAIR,OAAO;AAAA;AAAA;AAAA;AAAA,EAIP,YAAY;AAAA;AAAA;AAAA;AAAA,EAIZ,cAAc;AAAA;AAAA;AAAA;AAAA,EAId,qBAAqB;AAAA;AAAA;AAAA;AAAA,EAIrB,WAAW;AAAA;AAAA;AAAA;AAAA,EAIX,sBAAsB;AAAA;AAAA;AAAA;AAAA,EAItB,SAAS;AAAA;AAAA;AAAA;AAAA,EAIT,aAAa;AAAA;AAAA;AAAA;AAAA,EAIb,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKT,yBAAyB,SAAS,wBAAwB,GAAG;AAC3D,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,EAAE,UAAU,CAAC,EAAE,WAAW,EAAE;AAAA,IAEhC,WAAW,QAAQ,MAAM,WAAW,QAAQ,KAAK;AAC/C,aAAO;AAAA,IACT;AAGA,YAAQ,SAAS;AAAA,MACf,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AACX,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,SAAS,eAAe,SAAS;AAC/C,QAAI,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM;AACtD,aAAO;AAAA,IACT;AACA,QAAI,WAAW,QAAQ,YAAY,WAAW,QAAQ,cAAc;AAClE,aAAO;AAAA,IACT;AACA,QAAI,WAAW,QAAQ,KAAK,WAAW,QAAQ,GAAG;AAChD,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,UAAU,UAAU,QAAQ,QAAQ,MAAM,MAAM,YAAY,GAAG;AACxE,aAAO;AAAA,IACT;AACA,YAAQ,SAAS;AAAA,MACf,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ;AACX,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACX;AAAA,EACF;AACF;AACA,IAAO,kBAAQ;", "names": []}