package com.bdyl.line.web.model.response.organ;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 组织导出DTO
 *
 * <AUTHOR>
 */
@Data
public class OrganExportDTO {
    /**
     * 机构id
     */
    @ExcelProperty("机构ID")
    private Long id;

    /**
     * 组织类型（中文名称）
     */
    @ExcelProperty("组织类型")
    private String orgType;

    /**
     * 地区编码
     */
    @ExcelProperty("地区编码")
    private String regionCode;

    /**
     * 完整区划(用于前端展示)
     */
    @ExcelProperty("区域")
    private String fullRegionName;

    /**
     * 平台名称
     */
    @ExcelProperty("平台名称")
    private String platformName;

    /**
     * 管理员手机
     */
    @ExcelProperty("管理员手机号")
    private String adminPhone;

    /**
     * 管理员账号
     */
    @ExcelProperty("管理员账号")
    private String adminAccount;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

    /**
     * 摄像头数量
     */
    @ExcelProperty("摄像头数量")
    private Integer cameraCount;
}
