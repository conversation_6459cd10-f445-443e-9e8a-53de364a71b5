<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bdyl.line.web.mapper.UserMapper">

    <select id="countUsersByOrganIdsIn" resultType="com.bdyl.line.web.model.response.user.UserCountByOrganDTO">
        SELECT organ_id AS organId, COUNT(*) AS count
        FROM t_user as u
        WHERE u.organ_id IN
        <foreach collection="organIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY organId
    </select>
</mapper>