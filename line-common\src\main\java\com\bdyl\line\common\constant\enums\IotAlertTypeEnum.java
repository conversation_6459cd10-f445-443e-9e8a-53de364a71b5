package com.bdyl.line.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 物联报警类型枚举
 *
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@AllArgsConstructor
public enum IotAlertTypeEnum {

    /**
     * 电量过低
     */
    LOW_BATTERY("LOW_BATTERY", "电量过低", "设备电量过低"),
    /**
     * 设备箱状态异常
     */
    DEVICE_BOX_ABNORMAL("DEVICE_BOX_ABNORMAL", "设备箱状态异常", "设备箱状态异常");

    /**
     * value
     */
    private final String value;
    /**
     * 名称
     */
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据枚举值获取枚举类
     *
     * @param value 枚举值
     * @return 枚举类
     */
    public static IotAlertTypeEnum fromValue(String value) {

        for (IotAlertTypeEnum type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown type: " + value);
    }

}
