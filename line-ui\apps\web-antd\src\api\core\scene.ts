import { requestClient } from '#/api/request';

enum Api {
  scene = '/api/line/scene',
}

export namespace SceneApi {
  export interface SceneListParams {
    name?: string;
    page: number;
    size: number;
  }

  export interface SceneInfo {
    id: string;
    name: string;
    description?: string;
    createTime: string;
  }
}

// 获取场景列表
export const getSceneList_Api = (params: SceneApi.SceneListParams) => {
  return requestClient.get(`${Api.scene}/page`, { params });
};

// 获取所有场景
export const getSceneAllList_Api = () => {
  return requestClient.get(`${Api.scene}/list`);
};

// 添加场景
export const addScene_Api = (
  data: Omit<SceneApi.SceneInfo, 'id' | 'createTime'>,
) => {
  return requestClient.post(`${Api.scene}`, data);
};

// 更新场景
export const updateScene_Api = (
  id: string,
  data: Omit<SceneApi.SceneInfo, 'id' | 'createTime'>,
) => {
  return requestClient.put(`${Api.scene}/${id}`, data);
};

// 删除场景
export const delScene_Api = (id: string) => {
  return requestClient.delete(`${Api.scene}/${id}`);
};

// 获取场景详情
export const getScene_Api = (id: string) => {
  return requestClient.get(`${Api.scene}/${id}`);
};

// 绑定摄像头到场景
export const bindCameras_Api = (id: string, cameraIds: string[]) => {
  return requestClient.put(`${Api.scene}/${id}/bind-cameras`, cameraIds);
};

// 获取各场景摄像头数量
export const getSceneCameraCount_Api = () => {
  return requestClient.get(`/api/line/camera/count/by/scene`);
};
