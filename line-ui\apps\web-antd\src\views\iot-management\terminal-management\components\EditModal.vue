<script lang="ts" setup>
import { computed, nextTick, reactive, ref } from 'vue';

import { message } from 'ant-design-vue';

import { addTerminal_Api, updateTerminal_Api } from '#/api/core/terminal';
import { getOrganList_Api } from '#/api/core';
import { useDictStore } from '#/store';

const emit = defineEmits(['success']);

const open = ref<boolean>(false);
const modalTitle = ref<string>('');
const formRef = ref();
const loading = ref(false);
const labelCol = { span: 6 };
const wrapperCol = { span: 16 };

const dictStore = useDictStore();

// 组织树数据
const organOptions = ref<any[]>([]);
const organLoading = ref(false);

// 表单数据
const formData = reactive<any>({
  id: undefined,
  name: undefined,
  code: undefined,
  location: undefined,
  status: 'ENABLE',
  batteryLevel: undefined,
  boxStatus: 'NORMAL',
  organId: undefined,
  remark: undefined,
});

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入终端名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入终端编码', trigger: 'blur' }],
  location: [{ required: true, message: '请输入终端位置', trigger: 'blur' }],
  status: [{ required: true, message: '请选择终端状态', trigger: 'change' }],
  batteryLevel: [
    { required: true, message: '请输入蓄电池电量', trigger: 'blur' },
    {
      type: 'number',
      min: 0,
      max: 100,
      message: '电量范围为0-100',
      trigger: 'blur',
    },
  ],
  boxStatus: [
    { required: true, message: '请选择设备箱状态', trigger: 'change' },
  ],
  organId: [{ required: true, message: '请选择组织', trigger: 'change' }],
};

// 设备箱状态选项
const boxStatusOptions = dictStore.getDictOptions('DeviceBoxStatusEnum');

// 终端状态选项
const terminalStatusOptions = dictStore.getDictOptions('TerminalStatusEnum');

// 加载组织树数据
const loadOrganData = async () => {
  try {
    organLoading.value = true;
    const res = await getOrganList_Api({ page: 1, size: 10000 });
    organOptions.value = res.data.map((item: any) => {
      return {
        value: item.id,
        label: item.platformName,
      };
    });
  } catch (error) {
    console.error('加载组织数据失败', error);
    message.error('加载组织数据失败');
  } finally {
    organLoading.value = false;
  }
};

// 是否为编辑模式
const isEdit = computed(() => !!formData.id);

// 重置表单
const resetForm = async () => {
  await formRef.value?.resetFields();
  Object.assign(formData, {
    id: undefined,
    name: undefined,
    code: undefined,
    location: undefined,
    status: 'NORMAL',
    batteryLevel: undefined,
    boxStatus: 'NORMAL',
    organId: undefined,
    remark: undefined,
  });
};

// 打开弹窗
const openModal = async (type: string, record?: any) => {
  open.value = true;
  modalTitle.value = type === 'create' ? '新增终端' : '编辑终端';
  await resetForm();

  // 加载组织数据
  await loadOrganData();

  if (type === 'update' && record) {
    nextTick(() => {
      // 编辑模式，只填充formData中已定义的字段
      Object.keys(formData).forEach((key: any) => {
        if (record[key] !== undefined) {
          formData[key] = record[key];
        }
      });
    });
  }
};

// 关闭弹窗
const closeModal = () => {
  open.value = false;
  resetForm();
};

// 保存终端
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;

    if (isEdit.value) {
      // 更新终端
      await updateTerminal_Api(formData.id, formData);
      message.success('更新成功');
    } else {
      // 新增终端
      await addTerminal_Api(formData);
      message.success('添加成功');
    }

    closeModal();
    emit('success');
  } catch (error) {
    console.error('保存终端失败', error);
  } finally {
    loading.value = false;
  }
};

// 暴露组件方法
defineExpose({
  openModal,
});
</script>

<template>
  <a-modal
    v-model:open="open"
    :title="modalTitle"
    :confirm-loading="loading"
    :mask-closable="false"
    width="600px"
    @cancel="closeModal"
  >
    <a-form
      ref="formRef"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :model="formData"
      :rules="formRules"
    >
      <a-form-item label="终端名称" name="name">
        <a-input v-model:value="formData.name" placeholder="请输入终端名称" />
      </a-form-item>

      <a-form-item label="终端编码" name="code">
        <a-input v-model:value="formData.code" placeholder="请输入终端编码" />
      </a-form-item>

      <a-form-item label="终端位置" name="location">
        <a-input
          v-model:value="formData.location"
          placeholder="请输入终端位置"
        />
      </a-form-item>

      <a-form-item label="终端状态" name="status">
        <a-select
          :disabled="isEdit"
          v-model:value="formData.status"
          placeholder="请选择终端状态"
          style="width: 100%"
        >
          <a-select-option
            v-for="item in terminalStatusOptions"
            :key="item.dictValue"
            :value="item.dictValue"
          >
            {{ item.dictLabel }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="蓄电池电量" name="batteryLevel">
        <a-input-number
          :disabled="isEdit"
          v-model:value="formData.batteryLevel"
          :min="0"
          :max="100"
          placeholder="请输入电量百分比"
          style="width: 100%"
          addon-after="%"
        />
      </a-form-item>

      <a-form-item label="设备箱状态" name="boxStatus">
        <a-select
          :disabled="isEdit"
          v-model:value="formData.boxStatus"
          placeholder="请选择设备箱状态"
          style="width: 100%"
        >
          <a-select-option
            v-for="item in boxStatusOptions"
            :key="item.dictValue"
            :value="item.dictValue"
          >
            {{ item.dictLabel }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <!-- <a-form-item label="所属组织" name="organId">
        <a-select
          v-model:value="formData.organId"
          placeholder="请选择组织"
          style="width: 100%"
          :loading="organLoading"
        >
          <a-select-option
            v-for="item in organOptions"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item> -->

      <!-- <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="formData.remark"
          placeholder="请输入备注信息"
          :rows="3"
        />
      </a-form-item> -->
    </a-form>

    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>
