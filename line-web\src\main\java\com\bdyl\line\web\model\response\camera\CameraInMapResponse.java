package com.bdyl.line.web.model.response.camera;

import lombok.Data;

/**
 * 摄像头响应对象，用于摄像头相关接口的返回。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class CameraInMapResponse {
    /**
     * 摄像头ID
     */
    private Long id;
    /**
     * 摄像头名称
     */
    private String name;
    /**
     * 摄像头编码
     */
    private String code;
    /**
     * 摄像头位置
     */
    private String location;

    /**
     * 摄像头状态 {@link com.bdyl.line.common.constant.enums.CameraStatusEnum}
     */
    private String status;

    /**
     * 描述
     */
    private String remarks;

    /**
     * 经度
     */
    private Double longitude;
    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 终端ID
     */
    private Long terminalId;

    /**
     * 终端状态 {@link com.bdyl.line.common.constant.enums.TerminalStatusEnum}
     */
    private String terminalStatus;

    /**
     * 电池电量
     */
    private Integer batteryLevel;

    /**
     * 当前报警类型 {@link com.bdyl.line.common.constant.enums.BizAlertTypeEnum}
     */
    private String currentAlertType;

    /**
     * 当前报警状态 {@link com.bdyl.line.common.constant.enums.VerifyStatusEnum}
     */
    private String currentAlertStatus;
}
