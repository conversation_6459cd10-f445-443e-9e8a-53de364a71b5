{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/loading/src/loading.js", "../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/loading/index.js"], "sourcesContent": ["import { reactive, defineComponent, h, computed, watch, createCommentVNode } from 'vue';\nimport { getConfig, getIcon, getI18n, createEvent, useSize } from '../../ui';\nimport { getSlotVNs } from '../../ui/src/vn';\nimport XEUtils from 'xe-utils';\nexport default defineComponent({\n    name: 'VxeLoading',\n    props: {\n        modelValue: Boolean,\n        icon: {\n            type: String,\n            default: () => getConfig().loading.icon\n        },\n        showIcon: {\n            type: <PERSON><PERSON><PERSON>,\n            default: () => getConfig().loading.showIcon\n        },\n        text: {\n            type: String,\n            default: () => getConfig().loading.text\n        },\n        showText: {\n            type: <PERSON><PERSON><PERSON>,\n            default: () => getConfig().loading.showText\n        },\n        status: String,\n        size: {\n            type: String,\n            default: () => getConfig().loading.size || getConfig().size\n        }\n    },\n    setup(props, context) {\n        const { slots, emit } = context;\n        const xID = XEUtils.uniqueId();\n        const { computeSize } = useSize(props);\n        const reactData = reactive({\n            initialized: false\n        });\n        const computeMaps = {\n            computeSize\n        };\n        const $xeLoading = {\n            xID,\n            props,\n            context,\n            reactData,\n            getComputeMaps: () => computeMaps\n        };\n        const computeLoadingIcon = computed(() => {\n            return props.icon || getIcon().LOADING;\n        });\n        const computeLoadingText = computed(() => {\n            const { text } = props;\n            return XEUtils.isString(text) ? text : getI18n('vxe.loading.text');\n        });\n        const handleInit = () => {\n            if (!reactData.initialized) {\n                reactData.initialized = !!reactData.initialized;\n            }\n        };\n        const dispatchEvent = (type, params, evnt) => {\n            emit(type, createEvent(evnt, { $loading: $xeLoading }, params));\n        };\n        const loadingMethods = {\n            dispatchEvent\n        };\n        const loadingPrivateMethods = {};\n        Object.assign($xeLoading, loadingMethods, loadingPrivateMethods);\n        const renderVN = () => {\n            const { modelValue, showIcon, status } = props;\n            const { initialized } = reactData;\n            const vSize = computeSize.value;\n            const defaultSlot = slots.default;\n            const textSlot = slots.text;\n            const iconSlot = slots.icon;\n            const loadingIcon = computeLoadingIcon.value;\n            const loadingText = computeLoadingText.value;\n            if (!initialized && !modelValue) {\n                return createCommentVNode();\n            }\n            return h('div', {\n                class: ['vxe-loading', {\n                        [`size--${vSize}`]: vSize,\n                        [`theme--${status}`]: status,\n                        'is--visible': modelValue\n                    }]\n            }, defaultSlot\n                ? [\n                    h('div', {\n                        class: 'vxe-loading--wrapper'\n                    }, getSlotVNs(defaultSlot({})))\n                ]\n                : [\n                    h('div', {\n                        class: 'vxe-loading--chunk'\n                    }, [\n                        showIcon && (iconSlot || loadingIcon)\n                            ? h('div', {\n                                class: 'vxe-loading--icon'\n                            }, iconSlot\n                                ? getSlotVNs(iconSlot({}))\n                                : [\n                                    h('i', {\n                                        class: loadingIcon\n                                    })\n                                ])\n                            : h('div', {\n                                class: 'vxe-loading--spinner'\n                            }),\n                        textSlot || loadingText\n                            ? h('div', {\n                                class: 'vxe-loading--text'\n                            }, textSlot ? getSlotVNs(textSlot({})) : `${loadingText}`)\n                            : null\n                    ])\n                ]);\n        };\n        watch(() => props.modelValue, () => {\n            handleInit();\n        });\n        handleInit();\n        $xeLoading.renderVN = renderVN;\n        return $xeLoading;\n    },\n    render() {\n        return this.renderVN();\n    }\n});\n", "import { VxeUI } from '@vxe-ui/core';\nimport VxeLoadingComponent from './src/loading';\nimport { dynamicApp, dynamicStore, checkDynamic } from '../dynamics';\nexport const VxeLoading = Object.assign({}, VxeLoadingComponent, {\n    install(app) {\n        app.component(VxeLoadingComponent.name, VxeLoadingComponent);\n    }\n});\nexport const LoadingController = {\n    open(options) {\n        const opts = Object.assign({}, options);\n        dynamicStore.globalLoading = {\n            modelValue: true,\n            text: opts.text,\n            icon: opts.icon\n        };\n        checkDynamic();\n    },\n    close() {\n        dynamicStore.globalLoading = null;\n    }\n};\ndynamicApp.use(VxeLoading);\nVxeUI.component(VxeLoadingComponent);\nVxeUI.loading = LoadingController;\nexport const Loading = VxeLoading;\nexport default VxeLoading;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,sBAAoB;AACpB,IAAO,kBAAQ,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,OAAO;AAAA,IACH,YAAY;AAAA,IACZ,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,QAAQ;AAAA,IACvC;AAAA,IACA,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,QAAQ;AAAA,IACvC;AAAA,IACA,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,QAAQ;AAAA,IACvC;AAAA,IACA,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,QAAQ;AAAA,IACvC;AAAA,IACA,QAAQ;AAAA,IACR,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAM,UAAU,EAAE,QAAQ,QAAQ,UAAU,EAAE;AAAA,IAC3D;AAAA,EACJ;AAAA,EACA,MAAM,OAAO,SAAS;AAClB,UAAM,EAAE,OAAO,KAAK,IAAI;AACxB,UAAM,MAAM,gBAAAA,QAAQ,SAAS;AAC7B,UAAM,EAAE,YAAY,IAAI,QAAQ,KAAK;AACrC,UAAM,YAAY,SAAS;AAAA,MACvB,aAAa;AAAA,IACjB,CAAC;AACD,UAAM,cAAc;AAAA,MAChB;AAAA,IACJ;AACA,UAAM,aAAa;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,gBAAgB,MAAM;AAAA,IAC1B;AACA,UAAM,qBAAqB,SAAS,MAAM;AACtC,aAAO,MAAM,QAAQ,QAAQ,EAAE;AAAA,IACnC,CAAC;AACD,UAAM,qBAAqB,SAAS,MAAM;AACtC,YAAM,EAAE,KAAK,IAAI;AACjB,aAAO,gBAAAA,QAAQ,SAAS,IAAI,IAAI,OAAO,QAAQ,kBAAkB;AAAA,IACrE,CAAC;AACD,UAAM,aAAa,MAAM;AACrB,UAAI,CAAC,UAAU,aAAa;AACxB,kBAAU,cAAc,CAAC,CAAC,UAAU;AAAA,MACxC;AAAA,IACJ;AACA,UAAM,gBAAgB,CAAC,MAAM,QAAQ,SAAS;AAC1C,WAAK,MAAM,YAAY,MAAM,EAAE,UAAU,WAAW,GAAG,MAAM,CAAC;AAAA,IAClE;AACA,UAAM,iBAAiB;AAAA,MACnB;AAAA,IACJ;AACA,UAAM,wBAAwB,CAAC;AAC/B,WAAO,OAAO,YAAY,gBAAgB,qBAAqB;AAC/D,UAAM,WAAW,MAAM;AACnB,YAAM,EAAE,YAAY,UAAU,OAAO,IAAI;AACzC,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,QAAQ,YAAY;AAC1B,YAAM,cAAc,MAAM;AAC1B,YAAM,WAAW,MAAM;AACvB,YAAM,WAAW,MAAM;AACvB,YAAM,cAAc,mBAAmB;AACvC,YAAM,cAAc,mBAAmB;AACvC,UAAI,CAAC,eAAe,CAAC,YAAY;AAC7B,eAAO,mBAAmB;AAAA,MAC9B;AACA,aAAO,EAAE,OAAO;AAAA,QACZ,OAAO,CAAC,eAAe;AAAA,UACf,CAAC,SAAS,KAAK,EAAE,GAAG;AAAA,UACpB,CAAC,UAAU,MAAM,EAAE,GAAG;AAAA,UACtB,eAAe;AAAA,QACnB,CAAC;AAAA,MACT,GAAG,cACG;AAAA,QACE,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,QACX,GAAG,WAAW,YAAY,CAAC,CAAC,CAAC,CAAC;AAAA,MAClC,IACE;AAAA,QACE,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,QACX,GAAG;AAAA,UACC,aAAa,YAAY,eACnB,EAAE,OAAO;AAAA,YACP,OAAO;AAAA,UACX,GAAG,WACG,WAAW,SAAS,CAAC,CAAC,CAAC,IACvB;AAAA,YACE,EAAE,KAAK;AAAA,cACH,OAAO;AAAA,YACX,CAAC;AAAA,UACL,CAAC,IACH,EAAE,OAAO;AAAA,YACP,OAAO;AAAA,UACX,CAAC;AAAA,UACL,YAAY,cACN,EAAE,OAAO;AAAA,YACP,OAAO;AAAA,UACX,GAAG,WAAW,WAAW,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,WAAW,EAAE,IACvD;AAAA,QACV,CAAC;AAAA,MACL,CAAC;AAAA,IACT;AACA,UAAM,MAAM,MAAM,YAAY,MAAM;AAChC,iBAAW;AAAA,IACf,CAAC;AACD,eAAW;AACX,eAAW,WAAW;AACtB,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO,KAAK,SAAS;AAAA,EACzB;AACJ,CAAC;;;AC3HM,IAAM,aAAa,OAAO,OAAO,CAAC,GAAG,iBAAqB;AAAA,EAC7D,QAAQ,KAAK;AACT,QAAI,UAAU,gBAAoB,MAAM,eAAmB;AAAA,EAC/D;AACJ,CAAC;AACM,IAAM,oBAAoB;AAAA,EAC7B,KAAK,SAAS;AACV,UAAM,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO;AACtC,iBAAa,gBAAgB;AAAA,MACzB,YAAY;AAAA,MACZ,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,IACf;AACA,iBAAa;AAAA,EACjB;AAAA,EACA,QAAQ;AACJ,iBAAa,gBAAgB;AAAA,EACjC;AACJ;AACA,WAAW,IAAI,UAAU;AACzB,MAAM,UAAU,eAAmB;AACnC,MAAM,UAAU;AACT,IAAM,UAAU;AACvB,IAAOC,mBAAQ;", "names": ["XEUtils", "loading_default"]}