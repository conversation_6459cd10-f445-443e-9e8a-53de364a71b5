{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/statusUtils.js"], "sourcesContent": ["import classNames from './classNames';\nconst InputStatuses = ['warning', 'error', ''];\nexport function getStatusClassNames(prefixCls, status, hasFeedback) {\n  return classNames({\n    [`${prefixCls}-status-success`]: status === 'success',\n    [`${prefixCls}-status-warning`]: status === 'warning',\n    [`${prefixCls}-status-error`]: status === 'error',\n    [`${prefixCls}-status-validating`]: status === 'validating',\n    [`${prefixCls}-has-feedback`]: hasFeedback\n  });\n}\nexport const getMergedStatus = (contextStatus, customStatus) => customStatus || contextStatus;"], "mappings": ";;;;;AAEO,SAAS,oBAAoB,WAAW,QAAQ,aAAa;AAClE,SAAO,mBAAW;AAAA,IAChB,CAAC,GAAG,SAAS,iBAAiB,GAAG,WAAW;AAAA,IAC5C,CAAC,GAAG,SAAS,iBAAiB,GAAG,WAAW;AAAA,IAC5C,CAAC,GAAG,SAAS,eAAe,GAAG,WAAW;AAAA,IAC1C,CAAC,GAAG,SAAS,oBAAoB,GAAG,WAAW;AAAA,IAC/C,CAAC,GAAG,SAAS,eAAe,GAAG;AAAA,EACjC,CAAC;AACH;AACO,IAAM,kBAAkB,CAAC,eAAe,iBAAiB,gBAAgB;", "names": []}