package com.bdyl.line.web.model.request.inspection;

import java.time.LocalDate;

import lombok.Data;
import lombok.EqualsAndHashCode;

import com.bdyl.boot.data.query.PageRequest;

/**
 * 巡检记录分页查询请求对象
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InspectionRecordPageRequest extends PageRequest {
    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 摄像头ID
     */
    private Long cameraId;

    /**
     * 摄像头名称
     */
    private String cameraName;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 是否正常
     */
    private Boolean isNormal;

    /**
     * 巡检人员ID
     */
    private Long inspectorId;

    /**
     * 巡检周期类型 {@link com.bdyl.line.common.constant.enums.InspectionCycleEnum}
     */
    private String cycleType;

    /**
     * 巡检负责人ID
     */
    private Long responsibleUserId;
}
