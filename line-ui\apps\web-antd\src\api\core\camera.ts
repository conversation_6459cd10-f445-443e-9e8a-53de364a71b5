import { requestClient, baseRequestClient } from '#/api/request';

enum Api {
  camera = '/api/line/camera',
}

export interface cameraParams {
  page: number;
  size: number;
  name?: string; // 摄像头名称
  code?: string; // 摄像头编码
  organId?: string; // 组织id
  tenantId?: string; // 租户id
  terminalId?: string; // 终端id
}

// 分页获取摄像头列表
export const getCameraList_Api = (params: cameraParams) => {
  return requestClient.get(`${Api.camera}/page`, { params });
};

// 获取摄像头列表
export const getCameras_Api = (params?: any) => {
  return requestClient.get(`${Api.camera}/list`, { params });
};

// 删除摄像头
export const deleteCamera_Api = (id: any) => {
  return requestClient.delete(`${Api.camera}/${id}`);
};

// 获取地图摄像头
export const getCameraMap = () => {
  return requestClient.get(`/api/line/camera/map`);
};

// 根据组织获取摄像头
export const getCameraByOrgan_Api = () => {
  return requestClient.get(`/api/line/camera/tree/by/organ`);
};

// 根据场景获取摄像头
export const getCameraByScene_Api = () => {
  return requestClient.get(`/api/line/camera/tree/by/organ`);
};

// 获取摄像头视频流URL
export const getCameraVideoUrl_Api = (deviceId: string) => {
  return requestClient.post(`/api/line/camera/play`, { deviceId });
};

// 视频回放
export const getVidePlayback_Api = (params: any, headers?: any) => {
  return baseRequestClient.post(`/api/line/camera/playback/play`, params, {
    headers,
  });
};

// 获取回放文件名
export const getPlayBackFilename_Api = (data: any) => {
  return requestClient.post(`/api/line/camera/playback/get/m3u8`, data);
};

// 根据摄像头id列表获取摄像头信息
export const getCameraByIds_Api = (cameraIds: any[]) => {
  return requestClient.get(`/api/line/camera/list/by/ids`, {
    params: { cameraIds },
  });
};

// 获取机构树下摄像头数量
export const getCameraCountByOrganNew_Api = () => {
  return requestClient.get(`/api/line/camera/tree/by/organ`);
};

// 摄像头模型配置
// 获取摄像头所有模型配置
export interface ModelConfigData {
  id: number;
  tenantId: number;
  organId: number;
  cameraId: number;
  modelCode: string;
  confidence: number;
  regionPoints: RegionPoints[];
}

export interface RegionPoints {
  x: number;
  y: number;
}
export const getCameraModelConfig_Api = (cameraId: string) => {
  return requestClient.get<ModelConfigData[]>(
    `/api/line/camera/model-config/${cameraId}`,
  );
};

// 更新模型区域
export interface updateBodyParameter {
  cameraId: number;
  modelCode: string;
  confidence: number;
  regionPoints: RegionPoints[];
}
export const updateCameraModelConfig_Api = (data: updateBodyParameter) => {
  return requestClient.put(`/api/line/camera/model-config/region`, data);
};

// 更新模型置信度
export const updateCameraModelConfigConfidence_Api = (
  data: updateBodyParameter,
) => {
  return requestClient.put(`/api/line/camera/model-config/confidence`, data);
};

// 获取模型配置详情
export const getCameraModelConfigDetail_Api = (
  cameraId: string,
  modelCode: string,
) => {
  return requestClient.get<ModelConfigData>(
    `/api/line/camera/model-config/detail`,
    { params: { cameraId, modelCode } },
  );
};
