<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { Select } from 'ant-design-vue';

import {
  getAlertStatic_Api,
  getAlertStaticByYear_Api,
  getDeviceStatic_Api,
  getIotStatic_Api,
  getSceneStaticByYear_Api,
  getMissedDetectionTrend_Api,
} from '#/api/core/home';

import { getCameraMap } from '#/api/core/camera';

import LeaFletMap from '#/components/LeaFletMap/index.vue';
import videoPng from '#/assets/images/video_sxt.png';

import MissedDetectionTrend from './components/MissedDetectionTrend.vue';
import ScenePieChart from './components/ScenePieChart.vue';
import StackedBarChart from './components/StackedBarChart.vue';
import StatCard from './components/StatCard.vue';
import MapPopupWindow from './components/MapPopupWindow.vue';
import PlayVideo from '#/views/iot-management/terminal-management/playVideo.vue';
import { useRouter } from 'vue-router';
import { useDictStore } from '#/store';

const dictStore = useDictStore();

// 响应式数据
const selectedYear = ref(new Date().getFullYear());
const deviceStats = ref<any>({});
const alertStats = ref<any>({});
const iotStats = ref<any>({});
const videoCoverageStats = ref<any>({});
const yearlyAlertStats = ref<any[]>([]);
const sceneStats = ref<any>({});
const missedDetectionData = ref<any[]>([]);

// 地图和摄像头相关数据
let map: any = null;
const mapRef = ref();
const cameras = ref<any[]>([]);
const cameraMarkers = ref<any[]>([]);

// PlayVideo组件引用
const playVideoRef = ref();

// 摄像头图标配置
const CAMERA_ICON_URL = videoPng;

const router = useRouter();

// 年份选项
const yearOptions = ref<Array<{ label: string; value: number }>>([]);

// 初始化年份选项
const initYearOptions = () => {
  const currentYear = new Date().getFullYear();
  const years = [];
  for (let i = currentYear; i >= currentYear - 5; i--) {
    years.push({ label: `${i}年`, value: i });
  }
  yearOptions.value = years;
};

// 获取设备统计数据
const fetchDeviceStats = async () => {
  try {
    const response = await getDeviceStatic_Api();
    deviceStats.value = response;

    deviceStats.value.onlineRate = deviceStats.value.onlineRate.toFixed(2);
  } catch (error) {
    console.error('获取设备统计失败:', error);
  }
};

// 获取报警统计数据
const fetchAlertStats = async () => {
  try {
    const response = await getAlertStatic_Api();
    alertStats.value = response;
  } catch (error) {
    console.error('获取报警统计失败:', error);
  }
};

// 获取物联报警统计数据
const fetchIotStats = async () => {
  try {
    const response = await getIotStatic_Api();
    iotStats.value = response;
  } catch (error) {
    console.error('获取物联报警统计失败:', error);
  }
};

// 获取视频覆盖率统计数据
const fetchVideoCoverageStats = async () => {
  try {
    // const response = await getVideoCoverageStatic_Api();
    // videoCoverageStats.value = response.data;
  } catch (error) {
    console.error('获取视频覆盖率统计失败:', error);
  }
};

// 获取年度报警统计数据
const fetchYearlyAlertStats = async (year: number) => {
  try {
    const response = await getAlertStaticByYear_Api(year);
    yearlyAlertStats.value = response;
  } catch (error) {
    console.error('获取年度报警统计失败:', error);
  }
};

// 获取场景统计数据
const fetchSceneStats = async (year: number) => {
  try {
    const response = await getSceneStaticByYear_Api(year);
    sceneStats.value = response.typeCountMap;
  } catch (error) {
    console.error('获取场景统计失败:', error);
  }
};

// 获取漏检率趋势数据
const fetchMissedDetectionTrend = async (year: number) => {
  try {
    const response = await getMissedDetectionTrend_Api(year);
    missedDetectionData.value = response;
  } catch (error) {
    console.error('获取漏检率趋势失败:', error);
  }
};

// 获取摄像头数据
const fetchCameras = async () => {
  try {
    const response = await getCameraMap();

    cameras.value = response;

    // 如果地图已经初始化，立即渲染摄像头
    if (map) {
      renderCameras();
    }
  } catch (error) {
    console.error('获取摄像头列表失败:', error);
  }
};

// 渲染摄像头标记
const renderCameras = () => {
  if (!map || !mapRef.value || !cameras.value.length) return;

  // 清除现有标记
  clearCameraMarkers();

  // 准备摄像头标记数据
  const markerArray = cameras.value.map((camera: any) => {
    // 判断摄像头状态
    let statusClass = 'offline'; // 默认离线
    if (camera.status === 'ONLINE') {
      statusClass = 'online';
    }

    return {
      ...camera,
      latitude: camera.latitude,
      longitude: camera.longitude,
      popup: `${camera.name || '未命名摄像头'}`,
      markerOptions: {
        icon: {
          iconUrl: CAMERA_ICON_URL,
          iconSize: [24, 24],
          iconAnchor: [12, 12],
          className: `camera-marker ${statusClass}`,
        },
      },
      popupOptions: {
        maxWidth: '200px',
      },
    };
  });

  // 创建标记
  const markers = mapRef.value.createMarker(
    markerArray,
    true,
    MapPopupWindow,
    'mouseover',
    {
      onPlayVideo: handlePlayVideo,
      onToTerminalDetail: toTerminalDetail,
    },
  );
  cameraMarkers.value = markers;
};

// 清除摄像头标记
const clearCameraMarkers = () => {
  if (cameraMarkers.value.length > 0) {
    cameraMarkers.value.forEach((marker) => {
      if (map && marker) {
        map.removeLayer(marker);
      }
    });
    cameraMarkers.value = [];
  }
};

// 年份变化处理
const handleYearChange = (year: any) => {
  const yearNum = typeof year === 'string' ? parseInt(year) : year;
  if (yearNum && !isNaN(yearNum)) {
    selectedYear.value = yearNum;
    fetchYearlyAlertStats(yearNum);
    fetchSceneStats(yearNum);
    fetchMissedDetectionTrend(yearNum);
  }
};

// 初始化数据
const initData = async () => {
  await Promise.all([
    fetchDeviceStats(),
    fetchAlertStats(),
    fetchIotStats(),
    fetchVideoCoverageStats(),
  ]);

  await Promise.all([
    fetchYearlyAlertStats(selectedYear.value),
    fetchSceneStats(selectedYear.value),
    fetchMissedDetectionTrend(selectedYear.value),
  ]);

  // 获取摄像头和报警数据
  await fetchCameras();
};

// 地图初始化
const onMapInit = (mapInstance: any) => {
  map = mapInstance;
  renderCameras();
};

// 处理视频播放
const handlePlayVideo = (cameraData: any) => {
  if (playVideoRef.value && cameraData) {
    // 构造传递给playVideo组件的数据
    const videoData = {
      id: cameraData.terminalId || cameraData.id,
      name: cameraData.name || '摄像头',
      batteryLevel: cameraData.batteryLevel || 0,
    };
    playVideoRef.value.openModal(videoData);
  }
};

// 跳转到终端详情
const toTerminalDetail = (popupData: any) => {
  router.push('/iot-management/terminal-Infor-management');
  localStorage.setItem(
    'terminalTemp',
    JSON.stringify({
      terminalId: popupData.terminalId,
      terminalStatus: popupData.terminalStatus,
    }),
  );
};

onMounted(() => {
  initYearOptions();
  initData();
});
</script>

<template>
  <Page>
    <div>
      <!-- 顶部统计卡片 -->
      <div class="mb-2 grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-4">
        <!-- 设备统计 -->
        <StatCard
          title="设备统计"
          icon="mdi:devices"
          color="#1890ff"
          :items="[
            { label: '设备总数', value: deviceStats.total || 0, unit: '' },
            {
              label: '在线设备',
              value: deviceStats.online || 0,
              unit: '',
              color: '#52c41a',
            },
            {
              label: '离线设备',
              value: deviceStats.offline || 0,
              unit: '',
              color: '#ff4d4f',
            },
            {
              label: '设备在线率',
              value: deviceStats.onlineRate || 0,
              unit: '%',
              color: '#1890ff',
            },
          ]"
        />

        <!-- 当前报警 -->
        <StatCard
          title="当前报警"
          icon="mdi:alert-circle"
          color="#faad14"
          :items="[
            { label: '当前报警', value: alertStats.total || 0, unit: '' },
            {
              label: dictStore.getDictDescription(
                'BizAlertTypeEnum',
                'EXCAVATION_DETECTION',
              ),
              value: alertStats.typeCountMap?.['EXCAVATION_DETECTION'] || 0,
              unit: '',
            },
            {
              label: dictStore.getDictDescription(
                'BizAlertTypeEnum',
                'PERIMETER_INTRUSION',
              ),
              value: alertStats.typeCountMap?.['PERIMETER_INTRUSION'] || 0,
              unit: '',
            },
            {
              label: dictStore.getDictDescription(
                'BizAlertTypeEnum',
                'POLE_COLLAPSE',
              ),
              value: alertStats.typeCountMap?.['POLE_COLLAPSE'] || 0,
              unit: '',
            },
            {
              label: dictStore.getDictDescription(
                'BizAlertTypeEnum',
                'CONSTRUCTION_VEHICLE',
              ),
              value: alertStats.typeCountMap?.['CONSTRUCTION_VEHICLE'] || 0,
              unit: '',
            },
          ]"
        />

        <!-- 物联报警 -->
        <StatCard
          title="物联报警"
          icon="mdi:wifi-alert"
          color="#722ed1"
          :items="[
            { label: '物联报警', value: iotStats.total || 0, unit: '条' },
            {
              label: '电池电量过低',
              value: iotStats.typeCountMap?.['LOW_BATTERY'] || 0,
              unit: '条',
            },
            {
              label: '设备箱异常',
              value: iotStats.typeCountMap?.['DEVICE_BOX_ABNORMAL'] || 0,
              unit: '条',
            },
          ]"
        />

        <!-- 视频覆盖率 -->
        <StatCard
          title="视频覆盖率"
          icon="mdi:video"
          color="#13c2c2"
          :items="[
            {
              label: '视频覆盖率',
              value: videoCoverageStats.coverageRate || 0,
              unit: '%',
            },
            {
              label: '千公里监控点数',
              value: videoCoverageStats.monitoringPointsPer100km || 0,
              unit: '个/千公里',
            },
          ]"
        />
      </div>

      <!-- 主要内容区域 -->
      <div class="flex gap-2">
        <!-- 左侧图表区域 -->
        <div class="w-1/4 space-y-2">
          <!-- 年份选择器 -->
          <div class="rounded-lg border border-gray-200 bg-white p-2 shadow-sm">
            <div class="flex items-center justify-between">
              <Select
                v-model:value="selectedYear"
                :options="yearOptions"
                class="w-64"
                @change="handleYearChange"
              />
            </div>
          </div>

          <!-- 堆叠柱状图 -->
          <div class="rounded-lg border border-gray-200 bg-white p-2 shadow-sm">
            <h3 class="mb-2 text-lg font-semibold text-gray-800">
              报警类型统计
            </h3>
            <div style="height: 250px">
              <StackedBarChart :data="yearlyAlertStats" />
            </div>
          </div>

          <!-- 饼状图 -->
          <div class="rounded-lg border border-gray-200 bg-white p-2 shadow-sm">
            <h3 class="mb-2 text-lg font-semibold text-gray-800">
              场景报警统计
            </h3>
            <div style="height: 250px">
              <ScenePieChart :data="sceneStats" />
            </div>
          </div>

          <!-- 漏检率趋势 -->
          <div class="rounded-lg border border-gray-200 bg-white p-2 shadow-sm">
            <h3 class="mb-2 text-lg font-semibold text-gray-800">漏检率趋势</h3>
            <div style="height: 250px">
              <MissedDetectionTrend :data="missedDetectionData" />
            </div>
          </div>
        </div>

        <!-- 右侧地图区域 -->
        <div class="flex-1">
          <div
            class="relative h-full rounded-lg border border-gray-200 bg-white shadow-sm"
          >
            <div
              style="
                height: 100%;
                width: 100%;
                border-radius: 5px;
                overflow: hidden;
              "
            >
              <LeaFletMap ref="mapRef" @mapInited="onMapInit" />
            </div>

            <!-- 摄像头状态图例 -->
            <div class="legend-box">
              <div class="legend-item">
                <div class="legend-icon online">
                  <img class="img-box" :src="videoPng" alt="" />
                </div>
                <div class="legend-label">在线设备</div>
              </div>
              <div class="legend-item">
                <div class="legend-icon offline">
                  <img class="img-box" :src="videoPng" alt="" />
                </div>
                <div class="legend-label">离线设备</div>
              </div>
              <div class="legend-item">
                <div class="legend-icon alarm">
                  <img class="img-box" :src="videoPng" alt="" />
                </div>
                <div class="legend-label">报警设备</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 视频播放弹窗 -->
    <PlayVideo ref="playVideoRef" />
  </Page>
</template>

<style lang="scss" scoped>
.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

:deep(.ant-select-selector) {
  border-radius: 6px;
}

:deep(.ant-select-selection-item) {
  font-weight: 500;
}

/* 摄像头状态图例样式 */
.legend-box {
  position: absolute;
  z-index: 9999;
  bottom: 20px;
  right: 10px;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;

    &:last-child {
      margin-bottom: 0;
    }

    .legend-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;

      .img-box {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      &.offline {
        filter: grayscale(100%); /* 灰色 */
      }

      &.alarm {
        filter: hue-rotate(200deg) saturate(2);
      }
    }

    .legend-label {
      font-size: 12px;
      color: #333;
      font-weight: 500;
    }
  }
}

/* 摄像头标记样式 */
:deep(.leaflet-marker-icon) {
  transition: filter 0.3s ease;

  &.offline {
    filter: grayscale(100%); /* 灰色 */
  }

  &.alarm {
    filter: hue-rotate(200deg) saturate(2);
    animation: pulse 2s infinite;
  }
}

/* 报警摄像头闪烁动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
