package com.bdyl.line.web.model.response.home;

import java.util.List;

import lombok.Data;

/**
 * 首页业务报警按月类型统计响应对象。 每月一个对象，typeCounts为类型分布。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class HomeBizAlertMonthTypeCountResponse {
    /**
     * 月份 1-12
     */
    private Integer month;
    /**
     * 各类型报警数量
     */
    private List<AlertTypeCount> typeCounts;

    /**
     * 报警类型数量
     */
    @Data
    public static class AlertTypeCount {
        /**
         * 报警类型
         */
        private String type;
        /**
         * 数量
         */
        private Integer count;
    }
}
