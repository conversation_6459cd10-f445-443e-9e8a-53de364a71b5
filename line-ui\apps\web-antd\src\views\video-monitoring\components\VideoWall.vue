<script setup lang="ts">
import { ref, useTemplateRef } from 'vue';

import { SvgVideo1Screen, SvgVideo4Screen, SvgVideo9Screen } from '@vben/icons';

import { useFullscreen } from '@vueuse/core';
import { message } from 'ant-design-vue';

import EasyPlayer from '#/components/EasyPlayer/index.vue';

const videoUrlList = ref<any[]>([{}]);
const currentVideoIndex = ref<null | number>(null);
const el = useTemplateRef('screen-box');

const { enter } = useFullscreen(el);

const switchScreen = (screenNum: number) => {
  currentVideoIndex.value = null;
  const currentScreenNum = videoUrlList.value.length;
  if (currentScreenNum >= screenNum) {
    videoUrlList.value = videoUrlList.value.slice(0, screenNum);
  } else {
    for (let index = currentScreenNum; index < screenNum; index++) {
      videoUrlList.value[index] = {};
    }
  }
};

// 检查是否存在重复的摄像头
const findExistingCameraIndex = (videoData: any) => {
  return videoUrlList.value.findIndex(
    (item) =>
      (item.cameraId && videoData.cameraId && item.cameraId === videoData.cameraId) ||
      (item.deviceId && videoData.deviceId && item.deviceId === videoData.deviceId),
  );
};

// 创建视频数据对象
const createVideoDataObject = (videoData: any) => {
  return {
    url: videoData.url,
    name: videoData.name,
    cameraId: videoData.cameraId,
    deviceId: videoData.deviceId,
    batteryLevel: videoData.batteryLevel,
    cameraRegions: videoData.cameraRegions,
    longitude: videoData.longitude,
    latitude: videoData.latitude,
  };
};

// 添加视频到VideoWall
const addVideo = (videoData: any) => {
  console.log('Adding video to VideoWall:', videoData);

  // 首先检查是否已经存在相同的摄像头（防止重复添加）
  const existingIndex = findExistingCameraIndex(videoData);

  if (existingIndex !== -1) {
    // 如果已存在相同摄像头，更新现有位置的数据
    console.log('Camera already exists, updating existing position:', existingIndex);
    videoUrlList.value[existingIndex] = createVideoDataObject(videoData);
    // 显示提示信息
    message.info(`摄像头"${videoData.name || videoData.cameraId}"已存在`);
    console.log('VideoWall updated (existing camera):', videoUrlList.value);
    return;
  }

  // 优先添加到currentVideoIndex指定位置
  if (currentVideoIndex.value !== null &&
      currentVideoIndex.value >= 0 &&
      currentVideoIndex.value < videoUrlList.value.length) {
    console.log('Adding to current selected position:', currentVideoIndex.value);
    videoUrlList.value[currentVideoIndex.value] = createVideoDataObject(videoData);
  } else {
    // 如果没有选中位置，查找第一个空的位置
    const emptyIndex = videoUrlList.value.findIndex((item) => !item.url);

    if (emptyIndex !== -1) {
      // 如果有空位置，添加到空位置
      console.log('Adding to empty position:', emptyIndex);
      videoUrlList.value[emptyIndex] = createVideoDataObject(videoData);
    } else {
      // 如果都满了，替换第一个位置
      console.log('All positions occupied, replacing first position');
      videoUrlList.value[0] = createVideoDataObject(videoData);
    }
  }

  console.log('VideoWall updated:', videoUrlList.value);
};

// 清除指定位置的视频
const clearVideo = (index: number) => {
  if (index >= 0 && index < videoUrlList.value.length) {
    videoUrlList.value[index] = {};
    if (currentVideoIndex.value === index) {
      currentVideoIndex.value = null;
    }
  }
};

// 获取可用的播放位置数量
const getAvailableSlots = () => {
  return videoUrlList.value.length;
};

// 获取空闲的播放位置数量
const getEmptySlots = () => {
  return videoUrlList.value.filter((item) => !item.url).length;
};

// 清除所有视频
const clearAllVideos = () => {
  videoUrlList.value = videoUrlList.value.map(() => ({}));
  currentVideoIndex.value = null;
};

// 暴露方法给父组件
defineExpose({
  addVideo,
  getAvailableSlots,
  getEmptySlots,
  clearVideo,
  clearAllVideos,
  switchScreen,
});
</script>

<template>
  <div class="video-wall">
    <div
      class="flex h-[50px] w-full items-center justify-between rounded-[var(--radius)] bg-[hsl(var(--overlay))]"
    >
      <div class="flex pl-[10px]" style="color: #fff; align-items: center">
        <!-- 点击左侧摄像头添加视频监控 -->
        <div
          style="
            width: 40px;
            height: 3px;
            border-radius: 1.5px;
            background-color: #3b82f6;
          "
          class="mr-2"
        ></div>
        蓝色线条代表视频监控范围
      </div>
      <div class="flex items-center gap-[20px] pr-[10px]">
        <!-- 清除所有视频按钮 -->
        <!-- <button
          @click="clearAllVideos"
          class="rounded bg-red-500 px-3 py-1 text-sm text-white transition-colors hover:bg-red-600"
          title="清除所有视频"
        >
          清除全部
        </button> -->

        <!-- 分屏切换按钮 -->
        <SvgVideo1Screen
          class="size-8 cursor-pointer text-white transition-colors hover:text-blue-400"
          @click="switchScreen(1)"
          title="1分屏"
        />
        <SvgVideo4Screen
          class="size-8 cursor-pointer text-white transition-colors hover:text-blue-400"
          @click="switchScreen(4)"
          title="4分屏"
        />
        <SvgVideo9Screen
          class="size-8 cursor-pointer text-white transition-colors hover:text-blue-400"
          @click="switchScreen(9)"
          title="9分屏"
        />
        <!-- <SvgVideo16Screen
          class="size-8 cursor-pointer"
          @click="switchScreen(16)"
        /> -->
      </div>
    </div>
    <div
      ref="screen-box"
      class="grid flex-[1] gap-[5px] overflow-hidden p-[5px]"
      :class="`screen-${videoUrlList.length}`"
    >
      <div
        v-for="(item, index) in videoUrlList"
        :key="`${item.url}${index}`"
        class="video-item relative"
        :class="currentVideoIndex === index ? 'active' : ''"
        @click="currentVideoIndex = index"
      >
        <!-- 视频播放器 -->
        <EasyPlayer
          v-if="item.url"
          :id="`easy-player-${new Date().getTime() + index}`"
          :video-url="item.url"
          :show-battery="true"
          :battery-level="item.batteryLevel || 0"
          :points="item.cameraRegions"
          :latitude="item.latitude"
          :longitude="item.longitude"
          :device-id="item.deviceId"
        />

        <!-- 空状态 -->
        <div
          v-else
          class="flex h-full items-center justify-center text-gray-400"
        >
          <div class="text-center">
            <div class="mb-2 text-2xl">📺</div>
            <div class="text-lg">暂无视频</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.video-wall {
  @apply flex h-full w-full flex-col gap-[10px];

  .video-item {
    border: 1px solid hsl(var(--overlay));
    border-radius: 5px;
    overflow: hidden;

    &.active {
      border: 2px solid hsl(var(--primary));
    }
  }
}

//1分屏
.screen-1 {
  grid-template-rows: repeat(1, 1fr);
  grid-template-columns: repeat(1, 1fr);
}

//4分屏
.screen-4 {
  grid-template-rows: repeat(2, 1fr);
  grid-template-columns: repeat(2, 1fr);
}

//9分屏
.screen-9 {
  grid-template-rows: repeat(3, 1fr);
  grid-template-columns: repeat(3, 1fr);
}

//16分屏
.screen-16 {
  grid-template-rows: repeat(4, 1fr);
  grid-template-columns: repeat(4, 1fr);
}
</style>
