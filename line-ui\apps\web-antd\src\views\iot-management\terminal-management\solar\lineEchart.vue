<template>
  <EchartsUI ref="trendLineEchart" style="height: 100%" />
</template>
<script lang="ts" setup>
import { onMounted, ref, defineProps, watch, nextTick } from 'vue';

import {
  EchartsUI,
  type EchartsUIType,
  useEcharts,
} from '@vben/plugins/echarts';
const props = defineProps({
  eachartData: {
    type: Object,
    default: () => {},
  },
});

const trendLineEchart = ref<EchartsUIType>();

const { renderEcharts } = useEcharts(trendLineEchart);

const initChart = (datas: any) => {
  const xdata = datas.xdata;
  const dataScores: any = [];
  if (datas.data.length > 0) {
    datas.data.forEach((element: any) => {
      dataScores.push({
        name: element.name,
        data: element.value,
        // smooth: true,
        symbol: 'none',
        type: 'line',
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(220,220,220,0.53)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(220,220,220,0.03)', // 0% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      });
    });
  }

  renderEcharts({
    title: {
      text: datas.name,
      textStyle: {
        fontSize: 16, // 标题字体大小
      },
    },
    legend: {
      itemWidth: 8,
      itemHeight: 8,
      itemGap: 8,
      icon: 'circle',
      right: '1%',
      textStyle: {
        fontSize: 12,
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
    grid: {
      left: '3%',
      right: '5%',
      bottom: '5%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xdata,
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      minInterval: 1, //坐标点分割刻度是1
      axisLine: {
        show: true,
      },
      axisTick: {
        // 坐标刻度相关设置
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          // color: 'rgba(255,255,255,0.9)'
        },
      },
    },
    series: dataScores,
  });
};
watch(
  () => props.eachartData,
  (newV) => {
    nextTick(() => {
      initChart(newV);
    });
  },
  {
    deep: true,
  },
);
onMounted(() => {
  initChart(props.eachartData);
});
</script>
