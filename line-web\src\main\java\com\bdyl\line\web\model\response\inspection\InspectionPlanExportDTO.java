package com.bdyl.line.web.model.response.inspection;

import java.time.LocalDate;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 导出类
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class InspectionPlanExportDTO {

    /**
     * 计划名称
     */
    @ExcelProperty("计划名称")
    private String name;

    /**
     * 巡检周期类型
     */
    @ExcelProperty("巡检周期类型")
    private String cycleType;

    /**
     * 周期值
     */
    @ExcelProperty("周期值")
    private Integer cycleValue;

    /**
     * 摄像头列表
     */
    @ExcelProperty("摄像头列表")
    private String cameraNames;

    /**
     * 摄像头数量
     */
    @ExcelProperty("摄像头数量")
    private Integer cameraCount;

    /**
     * 启动日期
     */
    @ExcelProperty("启动日期")
    private LocalDate startDate;

    /**
     * 巡检负责人姓名
     */
    @ExcelProperty("巡检负责人")
    private String responsibleUserName;

    /**
     * 启用状态
     */
    @ExcelProperty("启用状态")
    private String status;

    /**
     * 计划描述
     */
    @ExcelProperty("计划描述")
    private String description;

    /**
     * 日期值(周类型:1-7表示星期几，月类型:1-31表示每月几号)
     */
    @ExcelProperty("日期值")
    private Integer dayValue;

    /**
     * 开始时间(日/周/月类型使用)
     */
    @ExcelProperty("开始时间")
    private String startTime;

    /**
     * 结束时间(日/周/月类型使用)
     */
    @ExcelProperty("结束时间")
    private String endTime;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
