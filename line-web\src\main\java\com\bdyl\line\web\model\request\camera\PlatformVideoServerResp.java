package com.bdyl.line.web.model.request.camera;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 视频服务响应对象，包含视频服务和国标流信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlatformVideoServerResp {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 边缘设备编码
     */
    private String edgeCode;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 摄像头码流通道
     */
    private String streamChannel;

    /**
     * 视频流获取方式
     */
    private String streamSource;

    /**
     * 厂商
     */
    private String vendor;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 端口
     */
    private Integer port;

    /**
     * 应用名
     */
    private String app;

    /**
     * 流ID
     */
    private String stream;

    /**
     * 通道ID
     */
    private String channelId;

    /**
     * RTSP地址
     */
    private String rtspUrl;

    /**
     * 媒体流URL信息列表，支持更灵活的URL描述
     */
    private List<StreamUrlInfo> streamUrlInfos;

    /**
     * 绑定的算法模型code列表
     */
    private List<ModelDTO> cameraModel;

    /**
     * 国标流信息
     */
    private PlatformGbStream gbStream;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updateTime;
}
