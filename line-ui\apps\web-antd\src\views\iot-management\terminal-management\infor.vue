<script lang="ts" setup>
import { onMounted, reactive, ref, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import CompImage from './solar/imgCont.vue';
import CompLineEchart from './solar/lineEchart.vue';
// import CompHistoryTable from './solar/historyTable.vue';
import { Page } from '@vben/common-ui';

import {
  getSolarPanelStat24h_Api,
  getSolarPanelRealTime_Api,
} from '#/api/core/terminal';

const router = useRouter();
const sourcesData = ref();
const terminalId = ref();
// const activeKey = ref('1');
const dayTime = ref(dayjs());
const chartData_fuzai = reactive({
  id: 'fuzaiChart',
  name: '负载',
  xdata: ['0:00', '04:00', '08:00', '12:00', '16:00', '23:00'],
  data: [
    { name: '直流负载电压', value: [0, 0, 0, 0, 0, 0] },
    { name: '直流负载电流', value: [0, 0, 0, 0, 0, 0] },
    { name: '直流负载功率', value: [0, 0, 0, 0, 0, 0] },
  ],
});
const chartData_pv = reactive({
  id: 'pvChart',
  name: 'PV组件',
  xdata: ['0:00', '04:00', '08:00', '12:00', '16:00', '23:00'],
  data: [
    { name: '电压', value: [0, 0, 0, 0, 0, 0] },
    { name: '电流', value: [0, 0, 0, 0, 0, 0] },
    { name: '发电功率', value: [0, 0, 0, 0, 0, 0] },
  ],
});
const chartData_xdc = reactive({
  id: 'xdcChart',
  name: '蓄电池',
  xdata: ['0:00', '04:00', '08:00', '12:00', '16:00', '23:00'],
  data: [
    { name: '电压', value: [0, 0, 0, 0, 0, 0] },
    { name: '电流', value: [0, 0, 0, 0, 0, 0] },
    { name: '剩余电量', value: [0, 0, 0, 0, 0, 0] },
  ],
});

// 返回上一页
const goBack = () => {
  router.replace({ name: 'TerminalManagement' }); // 需要跳转的页面
};

// 时间切换
const onChange = (_date: any, _dateString: any) => {
  getSolarPanelStat24h();
};

// 获取蓄电池24小时数据
const getSolarPanelStat24h = async () => {
  const res = await getSolarPanelStat24h_Api({
    terminalId: terminalId.value,
    day: dayTime.value.format('YYYY-MM-DD'),
  });
  if (res.length) {
    const hours = res.map((item: any) => item.hour);
    // 蓄电池
    chartData_xdc.xdata = hours;
    chartData_xdc.data = chartData_xdc.data.map((item: any): any => {
      if (item.name === '电压') {
        item.value = res.map((item: any) => item.batteryVoltage);
      } else if (item.name === '电流') {
        item.value = res.map((item: any) => item.batteryCurrent);
      } else {
        item.value = res.map((item: any) => item.batteryLevel);
      }
      return item;
    });

    // pv组件
    chartData_pv.xdata = hours;
    chartData_pv.data = chartData_pv.data.map((item: any): any => {
      if (item.name === '电压') {
        item.value = res.map((item: any) => item.pvVoltage);
      } else if (item.name === '电流') {
        item.value = res.map((item: any) => item.pvCurrent);
      } else {
        item.value = res.map((item: any) => item.pvPower);
      }
      return item;
    });

    // 负载
    chartData_fuzai.xdata = hours;
    chartData_fuzai.data = chartData_fuzai.data.map((item: any): any => {
      if (item.name === '直流负载电压') {
        item.value = res.map((item: any) => item.dcLoadVoltage);
      } else if (item.name === '直流负载电流') {
        item.value = res.map((item: any) => item.dcLoadCurrent);
      } else {
        item.value = res.map((item: any) => item.dcLoadPower);
      }
      return item;
    });
  }
};

// 获取实时数据
const getSolarPanelRealTime = async () => {
  const res = await getSolarPanelRealTime_Api(terminalId.value);
  if (res) {
    sourcesData.value = res;
  }
};

onMounted(() => {
  const terminalTemp = localStorage.getItem('terminalTemp');
  terminalId.value = JSON.parse(terminalTemp || '{}').terminalId;
  getSolarPanelStat24h();
  getSolarPanelRealTime();
});
onUnmounted(() => {
  localStorage.removeItem('terminalTemp');
});
</script>

<template>
  <Page>
    <a-card>
      <div style="text-align: right" class="pb-2 pr-4 pt-2">
        <a-button type="primary" @click="goBack">返回上页</a-button>
      </div>
      <div
        style="height: 92%; position: relative; background: #fff"
        class="ml-4 mr-4"
      >
        <!-- <a-tabs v-model:activeKey="activeKey" :tab-position="tabPosition">
        <a-tab-pane key="1" tab="实时监控"> -->
        <div class="left_img">
          <CompImage
            :sourcesData="sourcesData"
            :terminalId="terminalId"
          ></CompImage>
        </div>
        <div class="right_chart">
          <div>
            历史24h运行曲线&emsp;
            <a-date-picker
              v-model:value="dayTime"
              format="YYYY-MM-DD"
              @change="onChange"
              :allowClear="false"
            />
          </div>
          <a-card class="charthei" size="small">
            <CompLineEchart
              :eachartData="chartData_fuzai"
              :chartId="chartData_fuzai.id"
              :currentData="chartData_fuzai.data"
            />
          </a-card>
          <a-card class="charthei" size="small">
            <CompLineEchart
              :eachartData="chartData_pv"
              :chartId="chartData_pv.id"
              :currentData="chartData_pv.data"
            />
          </a-card>
          <a-card class="charthei" size="small">
            <CompLineEchart
              :eachartData="chartData_xdc"
              :chartId="chartData_xdc.id"
              :currentData="chartData_xdc.data"
            />
          </a-card>
        </div>
        <!-- </a-tab-pane>
        <a-tab-pane key="2" tab="历史数据">
          <CompHistoryTable></CompHistoryTable>
        </a-tab-pane> 
      </a-tabs>-->
      </div>
    </a-card>
  </Page>
</template>
<style scoped lang="scss">
.left_img {
  width: 800px;
  height: 100%;
  min-height: 700px;
  /*border: 1px solid #ddd;*/
  position: relative;
}
.right_chart {
  width: calc(100% - 810px);
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  /*border: 1px solid #ddd;*/
  padding-top: 0.5%;
  padding-right: 0.5%;
}
.charthei {
  height: 200px;
  // border: 1px solid #e0e0e0;
  margin-top: 10px;
}
:deep(.ant-card-body) {
  height: 100%;
}
// :deep(.ant-tabs) {
//   height: 100%;
// }
// :deep(.ant-tabs-tab) {
//   padding: 12px 6px;
// }
// :deep(.ant-tabs-content) {
//   height: 100%;
// }
</style>
