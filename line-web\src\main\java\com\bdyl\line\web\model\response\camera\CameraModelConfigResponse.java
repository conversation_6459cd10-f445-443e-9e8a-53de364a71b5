package com.bdyl.line.web.model.response.camera;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

import com.bdyl.line.web.model.dto.PointDTO;

/**
 * 摄像头-模型配置响应DTO
 */
@Data
public class CameraModelConfigResponse {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 组织ID
     */
    private Long organId;
    /**
     * 摄像头ID
     */
    private Long cameraId;
    /**
     * 模型编码
     */
    private String modelCode;
    /**
     * 置信度
     */
    private BigDecimal confidence;
    /**
     * 区域点集
     */
    private List<PointDTO> regionPoints;
}
